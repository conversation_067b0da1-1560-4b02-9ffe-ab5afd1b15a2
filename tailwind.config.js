const colors = require('tailwindcss/colors')
const plugin = require('tailwindcss/plugin')

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './ui/**/*.{js,ts,jsx,tsx}',
  ],
  darkMode: 'class',

  theme: {
    extend: {
      fontFamily: {
        // TODO: Make the fallback configurable in the backend
        body: ['var(--font-body)', 'sans-serif'],
        heading: ['var(--font-heading)', 'sans-serif'],
        display: ['var(--font-display)', 'sans-serif'],
        mono: ['var(--font-mono)', 'monospace'],
        // sans: ['var(--font-sans)', 'sans-serif'],
        // serif: ['var(--font-serif)', 'serif'],
      },

      // Font sizes are harcoded until font sized feature is completed in the backend
      fontSize: {
        '3xl': ['1.875rem', '2.5rem'],
        '6xl': '3rem',
      },

      zIndex: {
        max: '9999997',
        dialog: '9999998',
        dialogOpen: '9999999',
      },
      content: {
        quote: "'\\201C'",
      },
      aspectRatio: {
        '1/1': '1 / 1',
        '21/9': '21 / 9',
        '16/9': '16 / 9',
        '9/16': '9 / 16',
        '4/3': '4 / 3',
        '3/4': '3 / 4',
        '2/3': '2 / 3',
      },
    },
    screens: {
      'xs': '350px',
      'sm': '480px',
      'md': '768px',
      'lg': '976px',
      'xl': '1100px',
      '2xl': '1440px',
    },
    colors: {
      // Custom colors
      color1: {
        50: 'rgb(var(--color1-50) / <alpha-value>)',
        100: 'rgb(var(--color1-100) / <alpha-value>)',
        200: 'rgb(var(--color1-200) / <alpha-value>)',
        300: 'rgb(var(--color1-300) / <alpha-value>)',
        400: 'rgb(var(--color1-400) / <alpha-value>)',
        500: 'rgb(var(--color1-500) / <alpha-value>)',
        600: 'rgb(var(--color1-600) / <alpha-value>)',
        700: 'rgb(var(--color1-700) / <alpha-value>)',
        800: 'rgb(var(--color1-800) / <alpha-value>)',
        900: 'rgb(var(--color1-900) / <alpha-value>)',
        950: 'rgb(var(--color1-950) / <alpha-value>)',
      },
      color2: {
        50: 'rgb(var(--color2-50) / <alpha-value>)',
        100: 'rgb(var(--color2-100) / <alpha-value>)',
        200: 'rgb(var(--color2-200) / <alpha-value>)',
        300: 'rgb(var(--color2-300) / <alpha-value>)',
        400: 'rgb(var(--color2-400) / <alpha-value>)',
        500: 'rgb(var(--color2-500) / <alpha-value>)',
        600: 'rgb(var(--color2-600) / <alpha-value>)',
        700: 'rgb(var(--color2-700) / <alpha-value>)',
        800: 'rgb(var(--color2-800) / <alpha-value>)',
        900: 'rgb(var(--color2-900) / <alpha-value>)',
        950: 'rgb(var(--color2-950) / <alpha-value>)',
      },
      color3: {
        50: 'rgb(var(--color3-50) / <alpha-value>)',
        100: 'rgb(var(--color3-100) / <alpha-value>)',
        200: 'rgb(var(--color3-200) / <alpha-value>)',
        300: 'rgb(var(--color3-300) / <alpha-value>)',
        400: 'rgb(var(--color3-400) / <alpha-value>)',
        500: 'rgb(var(--color3-500) / <alpha-value>)',
        600: 'rgb(var(--color3-600) / <alpha-value>)',
        700: 'rgb(var(--color3-700) / <alpha-value>)',
        800: 'rgb(var(--color3-800) / <alpha-value>)',
        900: 'rgb(var(--color3-900) / <alpha-value>)',
        950: 'rgb(var(--color3-950) / <alpha-value>)',
      },
      color4: {
        50: 'rgb(var(--color4-50) / <alpha-value>)',
        100: 'rgb(var(--color4-100) / <alpha-value>)',
        200: 'rgb(var(--color4-200) / <alpha-value>)',
        300: 'rgb(var(--color4-300) / <alpha-value>)',
        400: 'rgb(var(--color4-400) / <alpha-value>)',
        500: 'rgb(var(--color4-500) / <alpha-value>)',
        600: 'rgb(var(--color4-600) / <alpha-value>)',
        700: 'rgb(var(--color4-700) / <alpha-value>)',
        800: 'rgb(var(--color4-800) / <alpha-value>)',
        900: 'rgb(var(--color4-900) / <alpha-value>)',
        950: 'rgb(var(--color4-950) / <alpha-value>)',
      },
      color5: {
        50: 'rgb(var(--color5-50) / <alpha-value>)',
        100: 'rgb(var(--color5-100) / <alpha-value>)',
        200: 'rgb(var(--color5-200) / <alpha-value>)',
        300: 'rgb(var(--color5-300) / <alpha-value>)',
        400: 'rgb(var(--color5-400) / <alpha-value>)',
        500: 'rgb(var(--color5-500) / <alpha-value>)',
        600: 'rgb(var(--color5-600) / <alpha-value>)',
        700: 'rgb(var(--color5-700) / <alpha-value>)',
        800: 'rgb(var(--color5-800) / <alpha-value>)',
        900: 'rgb(var(--color5-900) / <alpha-value>)',
        950: 'rgb(var(--color5-950) / <alpha-value>)',
      },
      color6: {
        50: 'rgb(var(--color6-50) / <alpha-value>)',
        100: 'rgb(var(--color6-100) / <alpha-value>)',
        200: 'rgb(var(--color6-200) / <alpha-value>)',
        300: 'rgb(var(--color6-300) / <alpha-value>)',
        400: 'rgb(var(--color6-400) / <alpha-value>)',
        500: 'rgb(var(--color6-500) / <alpha-value>)',
        600: 'rgb(var(--color6-600) / <alpha-value>)',
        700: 'rgb(var(--color6-700) / <alpha-value>)',
        800: 'rgb(var(--color6-800) / <alpha-value>)',
        900: 'rgb(var(--color6-900) / <alpha-value>)',
        950: 'rgb(var(--color6-950) / <alpha-value>)',
      },
      gray: {
        50: 'rgb(var(--gray-50) / <alpha-value>)',
        100: 'rgb(var(--gray-100) / <alpha-value>)',
        200: 'rgb(var(--gray-200) / <alpha-value>)',
        300: 'rgb(var(--gray-300) / <alpha-value>)',
        400: 'rgb(var(--gray-400) / <alpha-value>)',
        500: 'rgb(var(--gray-500) / <alpha-value>)',
        600: 'rgb(var(--gray-600) / <alpha-value>)',
        700: 'rgb(var(--gray-700) / <alpha-value>)',
        800: 'rgb(var(--gray-800) / <alpha-value>)',
        900: 'rgb(var(--gray-900) / <alpha-value>)',
        950: 'rgb(var(--gray-950) / <alpha-value>)',
      },

      // System colors
      danger: {
        50: 'rgb(var(--danger-50) / <alpha-value>)',
        100: 'rgb(var(--danger-100) / <alpha-value>)',
        200: 'rgb(var(--danger-200) / <alpha-value>)',
        300: 'rgb(var(--danger-300) / <alpha-value>)',
        400: 'rgb(var(--danger-400) / <alpha-value>)',
        500: 'rgb(var(--danger-500) / <alpha-value>)',
        600: 'rgb(var(--danger-600) / <alpha-value>)',
        700: 'rgb(var(--danger-700) / <alpha-value>)',
        800: 'rgb(var(--danger-800) / <alpha-value>)',
        900: 'rgb(var(--danger-900) / <alpha-value>)',
        950: 'rgb(var(--danger-950) / <alpha-value>)',
      },
      warning: {
        50: 'rgb(var(--warning-50) / <alpha-value>)',
        100: 'rgb(var(--warning-100) / <alpha-value>)',
        200: 'rgb(var(--warning-200) / <alpha-value>)',
        300: 'rgb(var(--warning-300) / <alpha-value>)',
        400: 'rgb(var(--warning-400) / <alpha-value>)',
        500: 'rgb(var(--warning-500) / <alpha-value>)',
        600: 'rgb(var(--warning-600) / <alpha-value>)',
        700: 'rgb(var(--warning-700) / <alpha-value>)',
        800: 'rgb(var(--warning-800) / <alpha-value>)',
        900: 'rgb(var(--warning-900) / <alpha-value>)',
        950: 'rgb(var(--warning-950) / <alpha-value>)',
      },
      info: {
        50: 'rgb(var(--info-50) / <alpha-value>)',
        100: 'rgb(var(--info-100) / <alpha-value>)',
        200: 'rgb(var(--info-200) / <alpha-value>)',
        300: 'rgb(var(--info-300) / <alpha-value>)',
        400: 'rgb(var(--info-400) / <alpha-value>)',
        500: 'rgb(var(--info-500) / <alpha-value>)',
        600: 'rgb(var(--info-600) / <alpha-value>)',
        700: 'rgb(var(--info-700) / <alpha-value>)',
        800: 'rgb(var(--info-800) / <alpha-value>)',
        900: 'rgb(var(--info-900) / <alpha-value>)',
        950: 'rgb(var(--info-950) / <alpha-value>)',
      },
      success: {
        50: 'rgb(var(--success-50) / <alpha-value>)',
        100: 'rgb(var(--success-100) / <alpha-value>)',
        200: 'rgb(var(--success-200) / <alpha-value>)',
        300: 'rgb(var(--success-300) / <alpha-value>)',
        400: 'rgb(var(--success-400) / <alpha-value>)',
        500: 'rgb(var(--success-500) / <alpha-value>)',
        600: 'rgb(var(--success-600) / <alpha-value>)',
        700: 'rgb(var(--success-700) / <alpha-value>)',
        800: 'rgb(var(--success-800) / <alpha-value>)',
        900: 'rgb(var(--success-900) / <alpha-value>)',
        950: 'rgb(var(--success-950) / <alpha-value>)',
      },

      white: colors.white,
      black: colors.black,

      transparent: 'transparent',
      current: 'currentColor',
    },
  },

  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms')({ strategy: 'class' }),

    // Prevents the blue highlighting of some elements in Webkit when tapping them.
    plugin(function ({ addUtilities }) {
      addUtilities({
        '*': { '-webkit-tap-highlight-color': 'transparent' },
      })
    }),

    // Sets color for li markers (bullet or numbered lists)
    plugin(function ({ addUtilities }) {
      addUtilities({
        'li::marker': { color: 'rgba(0,0,0,.5)' },
        '.dark li::marker': { color: 'rgba(255,255,255,.5)' },
      })
    }),

    // Increases font size (and line-height) for Arabic (ar)
    plugin(function ({ addUtilities }) {
      const sizesMap = {
        'xs': ['0.875', '1.25'],
        'sm': ['1', '1.5'],
        'base': ['1.125', '1.75'],
        'lg': ['1.25', '1.75'],
        'xl': ['1.5', '2'],
        '2xl': ['1.875', '2.25'],
        '3xl': ['2.25', '2.5'],
        '4xl': ['3', '3'],
        '5xl': ['3.75', '4'],
        '6xl': ['4.5', '5'],
        '7xl': ['6', '6'],
        '8xl': ['8', '7'],
        '9xl': ['9', '8'],
      }

      addUtilities(
        Object.entries(sizesMap).reduce(
          (acc, [key, [fontSize, lineHeight]]) => {
            acc[`*:lang(ar) .text-${key}`] = {
              'font-size': `${fontSize}rem`,
              'line-height': `${lineHeight}rem`,
            }
            return acc
          },
          {}
        )
      )
    }),

    // Sets color for li markers (bullet or numbered lists)
    plugin(function ({ addUtilities }) {
      addUtilities({
        '.mapboxgl-ctrl-logo': { display: 'none !important' },
      })
    }),

    // Hides scrollbar
    plugin(function ({ addUtilities }) {
      addUtilities({
        /* Hide scrollbar for Chrome, Safari and Opera */
        '.no-scrollbar::-webkit-scrollbar': { display: 'none' },

        /* Hide scrollbar for IE, Edge and Firefox */
        '.no-scrollbar': {
          '-ms-overflow-style': 'none' /* IE and Edge */,
          'scrollbar-width': 'none' /* Firefox */,
        },
      })
    }),

    // Adds variant to control whenever a style need to be applied within an element with a `.centered` (link the Container block).
    plugin(function ({ addVariant, e }) {
      addVariant('centered', ({ modifySelectors, separator }) => {
        modifySelectors(({ className }) => {
          return `.centered .${e(`centered${separator}${className}`)}`
        })
      })
    }),

    // Adds a class to apply a mask gradient that turns the sides of an element transparent
    plugin(function ({ addUtilities }) {
      // This class supports values from 0 - 10
      const range = Array.from({ length: 11 }, (_, i) => i)

      const directions = {
        x: '90deg', // Horizontal gradient
        y: '0deg', // Vertical gradient
      }

      // Generate mask gradient classes dynamically
      const maskGradientUtilities = Object.entries(directions).reduce(
        (acc, [axis, angle]) => {
          range.forEach(start => {
            range.forEach(end => {
              const className = `.opacity-gradient-${axis}-${start}-${end}`
              acc[className] = {
                maskImage: `linear-gradient(${angle}, transparent 0%, black ${start}%, black ${100 - end}%, transparent 100%)`,
              }
            })
          })

          return acc
        },
        {}
      )

      addUtilities(maskGradientUtilities, ['responsive'])
    }),

    // Used instead of overflow-x-hidden in Carousel, because using overflow-x-hidden and overflow-y-visible at the same time doesn't work
    plugin(function ({ addUtilities }) {
      addUtilities({
        '.clip-path-inset-0': { clipPath: 'inset(0)' },
      })
    }),
  ],
}
