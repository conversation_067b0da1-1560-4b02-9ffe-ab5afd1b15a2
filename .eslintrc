{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:@next/next/recommended"
  ],
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 12,
    "sourceType": "module"
  },
  "plugins": ["react", "react-refresh", "jsx-a11y"],
  "overrides": [
    {
      "files": ["*.jsx", "*.js"]
    }
  ],
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "rules": {
    "no-const-assign": "warn",
    "no-this-before-super": "warn",
    "no-unreachable": "error",
    "no-unused-vars": "warn",
    "constructor-super": "warn",
    "no-console": "warn",
    "valid-typeof": "warn",
    "react/display-name": "off",
    "react/react-in-jsx-scope": "off",
    "jsx-a11y/anchor-is-valid": "off",
    "react-hooks/rules-of-hooks": "warn",
    "react-hooks/exhaustive-deps": "warn", // Checks effect dependencies
    "react/prop-types": "off",
    "react-refresh/only-export-components": "error"
  }
}
