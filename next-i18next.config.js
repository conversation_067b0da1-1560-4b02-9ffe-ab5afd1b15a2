const path = require('path')

module.exports = {
  i18n: {
    defaultLocale: 'en',
    locales: [
      'en',
      'es',
      'fr',
      'de',
      'ar',
      'aa',
      'sq',
      'am',
      'as',
      'az-Latn',
      'bam',
      'bn',
      'bho-Deva',
      'my',
      'hne-Deva',
      'zh-Hans',
      'zh-Hant',
      'cs',
      'nl',
      'dz',
      'ee-Latn',
      'ff',
      'grt-Latn',
      'gu',
      'ha-Latn',
      'he',
      'hi',
      'hmn',
      'ig',
      'id',
      'it',
      'ja',
      'jv',
      'kab',
      'kn',
      'ks-Arab',
      'kk',
      'kha-Beng',
      'km',
      'ko',
      'kmr-Latn',
      'ckb-Arab',
      'lo',
      'mag-Deva',
      'mai',
      'ms',
      'ml',
      'mr',
      'mni-Latn',
      'lus-Latn',
      'mn',
      'mos-Latn',
      'ne',
      'or',
      'om',
      'ps',
      'fa',
      'fa-AF',
      'pl',
      'pt',
      'pa',
      'ru',
      'sat-Olck',
      'skr-Arab',
      'ksw-Mymr',
      'shn-Mymr',
      'sd',
      'si',
      'so',
      'su',
      'sw',
      'tg',
      'ta',
      'te',
      'th',
      'bo',
      'tr',
      'tw-Latn',
      'uk',
      'ur',
      'ug',
      'uz-Latn',
      'vi',
      'wo',
      'yo',
      'rhg-Latn',
      'mad-Latn',
    ],
  },
  localePath: path.resolve('./public/static/locales'),
  reloadOnPrerender: process.env.NODE_ENV === 'development',
}
