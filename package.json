{"name": "base-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "dotenv -e ${ENV_FILE:-.env.local} next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@floating-ui/react": "^0.26.16", "@headlessui/react": "^1.7.18", "@mapbox/mapbox-gl-geocoder": "^5.0.3", "@maptiler/client": "^1.8.1", "@maptiler/geocoding-control": "^1.4.1", "@next/bundle-analyzer": "^14.2.3", "@serwist/next": "^9.1.1", "@stripe/react-stripe-js": "^2.7.1", "@stripe/stripe-js": "^2.4.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13", "@vimeo/player": "^2.23.0", "autoprefixer": "^10.4.19", "body-parser": "^1.20.3", "clsx": "^2.1.1", "date-fns": "^2.30.0", "dinero.js": "^1.9.1", "disqus-react": "^1.1.5", "embla-carousel-autoplay": "^8.1.6", "embla-carousel-fade": "^8.1.6", "embla-carousel-react": "^8.1.6", "html-react-parser": "^5.1.10", "i18next": "^23.11.5", "lodash": "^4.17.21", "mapbox-gl": "^3.10.0", "maplibre-gl": "4.7.1", "next": "^14.2.26", "next-i18next": "^15.3.0", "next-seo": "^6.5.0", "postcss": "^8.4.38", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.51.5", "react-i18next": "^14.1.1", "react-map-gl": "^8.0.1", "react-query": "^3.39.3", "react-share": "^5.1.0", "react-tooltip": "^5.26.4", "tailwindcss": "^3.4.3", "video.js": "^8.12.0"}, "devDependencies": {"@types/howler": "^2.2.11", "@types/video.js": "^7.3.58", "dotenv-cli": "^7.4.2", "eslint": "^8.56.0", "eslint-config-next": "^14.2.3", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.5.14", "serwist": "^9.1.1"}}