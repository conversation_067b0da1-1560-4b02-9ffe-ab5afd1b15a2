import {
  Serwist,
  NetworkFirst,
  NetworkOnly,
  CacheFirst,
  StaleWhileRevalidate,
  ExpirationPlugin,
  RangeRequestsPlugin,
} from 'serwist'

// Get the SW manifest once to avoid multiple references
const swManifest = self.__SW_MANIFEST || []

const PAGES_CACHE_NAME = {
  rscPrefetch: 'pages-rsc-prefetch',
  rsc: 'pages-rsc',
  html: 'pages',
}

// Critical URLs to ensure are available offline - only include guaranteed static assets
const urlsToPrecache = [
  // PWA manifest
  { url: '/site.webmanifest', revision: null },
]

// Dynamically add PWA downloads data route from SW manifest
const addPwaDownloadsDataRoute = () => {
  try {
    // First check if the data route is already in the SW manifest
    const existingDataRoute = swManifest.find(
      entry => entry.url && entry.url.includes('/offline-downloads.json')
    )

    if (existingDataRoute) {
      return // Already included, no need to add manually
    }

    // Extract build ID from the SW manifest entries
    const buildManifestEntry = swManifest.find(
      entry => entry.url && entry.url.includes('_buildManifest.js')
    )

    let buildId = null

    if (buildManifestEntry) {
      // Extract build ID from path like /_next/static/{buildId}/_buildManifest.js
      const buildIdMatch = buildManifestEntry.url.match(
        /\/_next\/static\/([^/]+)\/_buildManifest\.js/
      )
      if (buildIdMatch) {
        buildId = buildIdMatch[1]
      }
    }

    // If we couldn't extract from _buildManifest, try from any _next/static entry
    if (!buildId) {
      const staticEntry = swManifest.find(
        entry =>
          entry.url &&
          entry.url.includes('/_next/static/') &&
          entry.url.includes('/chunks/')
      )
      if (staticEntry) {
        const buildIdMatch = staticEntry.url.match(/\/_next\/static\/([^/]+)\//)
        if (buildIdMatch) {
          buildId = buildIdMatch[1]
        }
      }
    }

    // Add the PWA downloads data route if we found a build ID
    if (buildId) {
      urlsToPrecache.push({
        url: `/_next/data/${buildId}/offline-downloads.json?slug=offline-downloads`,
        revision: null,
      })
    }
  } catch (error) {
    // Silent error handling for production
  }
}

// Add the data route dynamically
addPwaDownloadsDataRoute()

// Note: PWA downloads data routes are handled by runtime caching instead of precaching
// to avoid build ID mismatch issues that can cause service worker installation to fail

const serwist = new Serwist({
  // Use auto-generated manifest for all static resources (JS, CSS, etc.)
  // This automatically includes the correct build-time file names
  precacheEntries: [...swManifest, ...urlsToPrecache],
  precacheOptions: {
    cleanupOutdatedCaches: true,
    concurrency: 10,
    fallbackToNetwork: true,
  },
  // Enable automatic activation for better user experience
  skipWaiting: true, // Activate immediately when installed
  clientsClaim: true, // Take control of existing clients immediately
  runtimeCaching: [
    // PRIORITY 1: Critical PWA Downloads page resources (Enhanced for Safari)

    // Cache the PWA downloads page HTML with enhanced offline-first strategy
    {
      matcher: ({ url }) => url.pathname.startsWith('/offline-downloads'),
      handler: new NetworkFirst({
        cacheName: 'offline-downloads-page',
        networkTimeoutSeconds: 3, // Quick timeout to fallback to cache
        plugins: [
          {
            cacheKeyWillBeUsed: async ({ request }) => {
              const url = new URL(request.url)
              // For static pages, use clean URL without query params
              return `${url.origin}${url.pathname}`
            },
            cacheWillUpdate: async ({ response }) => {
              // Enhanced validation for static pages
              if (!response) {
                return false
              }
              const isValid =
                response.status === 200 &&
                response.headers.get('content-type')?.includes('text/html')
              return isValid
            },
          },
        ],
      }),
    },

    // Cache Next.js data requests for offline-downloads (static page data)
    {
      matcher: ({ url }) => {
        // Match both static data and potential SSG data patterns for offline-downloads
        const isStaticData = /\/_next\/data\/.+\/offline-downloads\.json/i.test(
          url.pathname
        )
        const isSSGData =
          url.pathname.includes('offline-downloads') &&
          url.pathname.includes('/_next/data/')
        // Also match any valid data route pattern for offline-downloads
        const isDataRoute =
          /\/_next\/data\/[^/]+\/offline-downloads\.json/i.test(url.pathname)

        return isStaticData || isSSGData || isDataRoute
      },
      handler: new NetworkFirst({
        cacheName: 'offline-downloads-data',
        networkTimeoutSeconds: 3,
        plugins: [
          {
            cacheKeyWillBeUsed: async ({ request }) => {
              // Clean URL for consistent caching
              return `${request.url.split('?')[0]}`
            },
            cacheWillUpdate: async ({ response }) => {
              const isValid =
                response &&
                response.status === 200 &&
                response.headers.get('content-type')?.includes('json')
              return isValid
            },
          },
        ],
      }),
    },

    // PRIORITY 2: Enhanced Next.js static resources

    {
      matcher: /^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,
      handler: new CacheFirst({
        cacheName: 'google-fonts-webfonts',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 4,
            maxAgeSeconds: 365 * 24 * 60 * 60, // 365 days
            maxAgeFrom: 'last-used',
          }),
        ],
      }),
    },
    {
      matcher: /^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,
      handler: new StaleWhileRevalidate({
        cacheName: 'google-fonts-stylesheets',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 4,
            maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
            maxAgeFrom: 'last-used',
          }),
        ],
      }),
    },
    {
      matcher: /\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,
      handler: new StaleWhileRevalidate({
        cacheName: 'static-font-assets',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 4,
            maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
            maxAgeFrom: 'last-used',
          }),
        ],
      }),
    },
    {
      matcher: /\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,
      handler: new StaleWhileRevalidate({
        cacheName: 'static-image-assets',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 64,
            maxAgeSeconds: 30 * 24 * 60 * 60, // 30 days
            maxAgeFrom: 'last-used',
          }),
        ],
      }),
    },
    {
      matcher: /\/_next\/static.+\.js$/i,
      handler: new CacheFirst({
        cacheName: 'next-static-js-assets',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 64,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
            maxAgeFrom: 'last-used',
          }),
        ],
      }),
    },
    {
      matcher: /\/_next\/image\?url=.+$/i,
      handler: new StaleWhileRevalidate({
        cacheName: 'next-image',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 64,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
            maxAgeFrom: 'last-used',
          }),
        ],
      }),
    },
    {
      matcher: /\.(?:mp3|wav|ogg)$/i,
      handler: new CacheFirst({
        cacheName: 'static-audio-assets',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 32,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
            maxAgeFrom: 'last-used',
          }),
          new RangeRequestsPlugin(),
        ],
      }),
    },
    {
      matcher: /\.(?:mp4|webm)$/i,
      handler: new CacheFirst({
        cacheName: 'static-video-assets',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 32,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
            maxAgeFrom: 'last-used',
          }),
          new RangeRequestsPlugin(),
        ],
      }),
    },
    {
      matcher: /\.(?:js)$/i,
      handler: new StaleWhileRevalidate({
        cacheName: 'static-js-assets',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 48,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
            maxAgeFrom: 'last-used',
          }),
        ],
      }),
    },
    {
      matcher: /\.(?:css|less)$/i,
      handler: new StaleWhileRevalidate({
        cacheName: 'static-style-assets',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 32,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
            maxAgeFrom: 'last-used',
          }),
        ],
      }),
    },
    {
      matcher: /\/_next\/data\/.+\/.+\.json/i,
      handler: new NetworkFirst({
        cacheName: 'next-data',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 32,
            maxAgeSeconds: 60 * 60, // 1 hour
            maxAgeFrom: 'last-used',
          }),
        ],
      }),
    },
    {
      matcher: /\.(?:json|xml|csv)$/i,
      handler: new NetworkFirst({
        cacheName: 'static-data-assets',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 32,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
            maxAgeFrom: 'last-used',
          }),
        ],
      }),
    },
    {
      // Exclude /api/auth/* to fix auth callback
      // https://github.com/serwist/serwist/discussions/28
      matcher: /\/api\/auth\/.*/,
      handler: new NetworkOnly({
        plugins: [
          new ExpirationPlugin({
            maxEntries: 16,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
          }),
        ],
        networkTimeoutSeconds: 10, // fallback to cache if API does not response within 10 seconds
      }),
    },
    {
      matcher: ({ sameOrigin, url: { pathname } }) =>
        sameOrigin && pathname.startsWith('/api/'),
      method: 'GET',
      handler: new NetworkFirst({
        cacheName: 'apis',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 16,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
            maxAgeFrom: 'last-used',
          }),
        ],
        networkTimeoutSeconds: 10, // fallback to cache if API does not response within 10 seconds
      }),
    },
    {
      matcher: ({ request, url: { pathname }, sameOrigin }) =>
        request.headers.get('RSC') === '1' &&
        request.headers.get('Next-Router-Prefetch') === '1' &&
        sameOrigin &&
        !pathname.startsWith('/api/'),
      handler: new NetworkFirst({
        cacheName: PAGES_CACHE_NAME.rscPrefetch,
        plugins: [
          new ExpirationPlugin({
            maxEntries: 32,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
          }),
        ],
      }),
    },
    {
      matcher: ({ request, url: { pathname }, sameOrigin }) =>
        request.headers.get('RSC') === '1' &&
        sameOrigin &&
        !pathname.startsWith('/api/'),
      handler: new NetworkFirst({
        cacheName: PAGES_CACHE_NAME.rsc,
        plugins: [
          new ExpirationPlugin({
            maxEntries: 32,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
          }),
        ],
      }),
    },
    {
      matcher: ({ request, url: { pathname }, sameOrigin }) =>
        request.headers.get('Content-Type')?.includes('text/html') &&
        sameOrigin &&
        !pathname.startsWith('/api/'),
      handler: new NetworkFirst({
        cacheName: PAGES_CACHE_NAME.html,
        plugins: [
          new ExpirationPlugin({
            maxEntries: 32,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
          }),
        ],
      }),
    },
    {
      matcher: ({ url: { pathname }, sameOrigin }) =>
        sameOrigin && !pathname.startsWith('/api/'),
      handler: new NetworkFirst({
        cacheName: 'others',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 32,
            maxAgeSeconds: 24 * 60 * 60, // 24 hours
          }),
        ],
      }),
    },
    {
      matcher: ({ sameOrigin }) => !sameOrigin,
      handler: new NetworkFirst({
        cacheName: 'cross-origin',
        plugins: [
          new ExpirationPlugin({
            maxEntries: 32,
            maxAgeSeconds: 60 * 60, // 1 hour
          }),
        ],
        networkTimeoutSeconds: 10,
      }),
    },
  ],
})

// Enhanced event listeners with Safari-specific error handling
serwist.addEventListeners()
