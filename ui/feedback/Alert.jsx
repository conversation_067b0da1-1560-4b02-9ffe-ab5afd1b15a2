import React from 'react'
import PropTypes from 'prop-types'

const styles = {
  danger:
    'text-danger-600 bg-danger-100  border-danger-300 dark:bg-danger-900 dark:text-danger-50 dark:border-danger-700',
  info: 'text-color1-600 bg-color1-100 border-color1-200 dark:bg-color1-800 dark:text-color1-50 dark:border-color1-700',
  success:
    'text-success-600 bg-success-100 border-success-300 dark:bg-success-900 dark:text-success-50 dark:border-success-700',
  warning:
    'text-warning-600 bg-warning-100 border-warning-200 dark:bg-warning-700 dark:text-warning-100 dark:border-warning-500',
  neutral:
    'text-gray-600 bg-gray-100 border-gray-300 dark:bg-gray-700 dark:text-gray-50 dark:border-gray-600',
}

export default function Alert({
  actions,
  children,
  className = '',
  message,
  title,
  type = 'info',
}) {
  const typeClasses = styles[type] || styles.info

  return (
    <div
      className={`space-y-4 rounded-md border p-6 ${typeClasses} ${className}`}
    >
      <div className="space-y-2">
        {title && (
          <h4 className="text-xl font-bold uppercase tracking-wide">{title}</h4>
        )}

        {children ||
          (message && (
            <div>{children || <p className="text-lg">{message}</p>}</div>
          ))}
      </div>
      {actions && <div className="flex justify-end">{actions}</div>}
    </div>
  )
}

Alert.propTypes = {
  children: PropTypes.node,
  actions: PropTypes.node,
  className: PropTypes.string,
  message: PropTypes.string,
  title: PropTypes.string,
  type: PropTypes.oneOf(['danger', 'info', 'neutral', 'success', 'warning']),
}
