import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const sizes = {
  dot: 'px-1 py-1',
  xs: 'px-2 py-px text-xs',
  sm: 'px-2 py-1 text-sm',
  md: 'px-3 py-2',
  lg: 'px-4 py-2 text-xl',
}

const LabelIcon = dynamic(() => import('ui/data-display/LabelIcon'))

export default function Badge({ className = '', label, icon, size }) {
  const sizeClass = sizes[size] || (label ? sizes.sm : sizes.dot)

  return (
    <span
      className={`inline-block whitespace-nowrap rounded-full ${sizeClass} ${className}`}
    >
      {size !== 'dot' && (
        <LabelIcon className="leading-none" label={label} icon={icon} />
      )}
    </span>
  )
}

Badge.propTypes = {
  className: PropTypes.string,
  icon: PropTypes.string,
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  size: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'dot']),
}
