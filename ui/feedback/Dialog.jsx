import PropTypes from 'prop-types'
import { useEffect, useRef } from 'react'

import Button from 'ui/buttons/Button'

export default function Dialog({
  children,
  className = '',
  contentClassName,
  id,
  isOpen,
  onClose,
  prefix,
  showClose = true,
  subtitle,
  title,
}) {
  const dialogRef = useRef(null)

  useEffect(() => {
    if (dialogRef.current) {
      if (isOpen) {
        dialogRef.current.showModal()
      } else {
        dialogRef.current.close()
      }
    }
  }, [isOpen])

  return (
    <dialog
      id={id}
      ref={dialogRef}
      className={`not-prose min-w-[320px] rounded-lg bg-white p-0 shadow-2xl backdrop:bg-black backdrop:bg-opacity-40 ${className}`}
      onClose={onClose} // eslint-disable-line react/no-unknown-property
      onCancel={onClose} // eslint-disable-line react/no-unknown-property
      onClick={e => {
        e.stopPropagation()
      }}
      role="presentation"
    >
      {(title || subtitle || showClose) && (
        <div className="flex flex-row items-center justify-between gap-4 px-6 py-3">
          <div className="space-y-0">
            <h3 className="font-semibold text-lg">
              {prefix && <span className="text-gray-500">{prefix} - </span>}{' '}
              {title}
            </h3>
            {subtitle && <h4 className="text-gray-400 text-xs">{subtitle}</h4>}
          </div>
          {showClose && (
            <Button onClick={onClose} icon="times" variant="flat" />
          )}
        </div>
      )}
      <div className={contentClassName ?? 'flex flex-grow flex-col px-6 pb-6'}>
        {children}
      </div>
    </dialog>
  )
}
Dialog.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  contentClassName: PropTypes.string,
  id: PropTypes.string,
  isOpen: PropTypes.bool,
  onClose: PropTypes.func,
  prefix: PropTypes.node,
  showClose: PropTypes.bool,
  subtitle: PropTypes.node,
  title: PropTypes.node,
}

export function DialogActions({ children }) {
  return (
    <div className="-mx-6 mt-6 flex flex-row justify-end space-x-2 border-t px-6 pb-0 pt-4 rtl:space-x-reverse">
      {children}
    </div>
  )
}
DialogActions.propTypes = {
  children: PropTypes.node,
}
