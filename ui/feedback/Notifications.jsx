import React, { useCallback, useEffect, useRef, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { useNavigationContext } from 'ui/navigation/Navigation'

const Button = dynamic(() => import('ui/buttons/Button'))
const RichText = dynamic(() => import('ui/typography/RichText'))

export default function Notifications({
  items,
  offsetClass = 'top-16 lg:top-24',
}) {
  const [stickyClass, setStickyClass] = useState('relative')
  const wrapRef = useRef()
  const { setContentTopMargin } = useNavigationContext()

  useEffect(() => {
    function stickNavbar() {
      if (window !== undefined) {
        const { height } = wrapRef.current?.getBoundingClientRect() || {}

        if (window.scrollY > 0) {
          setContentTopMargin(height - window.scrollY)
        } else {
          setContentTopMargin(0)
        }

        window.scrollY > 0
          ? setStickyClass(`fixed left-0 right-0 z-50 ${offsetClass}`)
          : setStickyClass('relative')
      }
    }

    window.addEventListener('scroll', stickNavbar)

    return () => {
      window.removeEventListener('scroll', stickNavbar)
    }
  }, [offsetClass, setContentTopMargin])

  if (!Array.isArray(items)) return null

  return (
    <div
      className={`bg-color1-500 bg-opacity-90 shadow backdrop-blur-lg ${stickyClass}`}
      ref={wrapRef}
    >
      <div className="container mx-auto max-w-screen-xl divide-y divide-color1-600 divide-opacity-50 px-6 xl:px-12">
        {items.map(notification => (
          <Notification
            key={notification.id}
            id={notification.id}
            title={notification.title}
            message={notification.message}
            ctaLabel={notification.ctaLabel}
            ctaUrl={notification.ctaUrl}
          />
        ))}
      </div>
    </div>
  )
}
Notifications.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      message: PropTypes.object,
      title: PropTypes.string,
    })
  ),
  offsetClass: PropTypes.string,
}

export function Notification({ title, message, id, ctaLabel, ctaUrl }) {
  const [closed, setClosed] = useState(false)
  const storeKey = `notification-${id}`

  useEffect(() => {
    async function getStoredValue() {
      const storedClosed = await JSON.parse(localStorage.getItem(storeKey))
      setClosed(!!storedClosed)
    }

    getStoredValue()
  }, [storeKey])

  const onClose = useCallback(() => {
    setClosed(true)
    localStorage.setItem(storeKey, true)
  }, [storeKey])

  if (closed) return null

  return (
    <div className="py-3">
      <div className="flex flex-row items-center space-x-4 rounded-lg p-4 pl-6 shadow-none transition-all duration-200 ease-in-out hover:bg-color1-400 hover:shadow-md rtl:space-x-reverse">
        <div className="flex grow flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-x-4 md:space-y-0 rtl:md:space-x-reverse">
          <div className="grow space-y-1">
            <h3 className="text-xl font-bold">{title}</h3>
            <RichText doc={message} />
          </div>

          {ctaLabel && ctaUrl && (
            <Button
              label={ctaLabel}
              url={ctaUrl}
              className="self-start md:self-auto"
            />
          )}
        </div>
        <div>
          <Button variant="flat" icon="times" onClick={onClose} />
        </div>
      </div>
    </div>
  )
}
Notification.propTypes = {
  id: PropTypes.string,
  message: PropTypes.object,
  title: PropTypes.string,
  ctaLabel: PropTypes.string,
  ctaUrl: PropTypes.string,
}
