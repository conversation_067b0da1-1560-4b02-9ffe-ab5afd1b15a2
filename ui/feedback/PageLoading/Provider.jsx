import React, { useState } from 'react'
import PropTypes from 'prop-types'

import { useRouter } from 'next/router'
import { PageLoadingContext } from './hooks'

export default function PageLoadingProvider({ children }) {
  const router = useRouter()
  const [pageLoading, setPageLoading] = useState(null)

  const isPageLoading = pageLoading === router.asPath
  const setPage = url => setPageLoading(router.asPath !== url ? null : url)
  const stopPageLoading = () => setPageLoading(null)

  return (
    <PageLoadingContext.Provider
      value={{
        setPage,
        stopPageLoading,
        isPageLoading,
      }}
    >
      {children}
    </PageLoadingContext.Provider>
  )
}
PageLoadingProvider.propTypes = {
  children: PropTypes.node,
}
