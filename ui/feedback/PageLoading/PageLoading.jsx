import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'

import { usePageLoading } from './hooks'

export default function PageLoading({ className = '' }) {
  const [fakeProgress, setFakeProgress] = useState(0)
  const { stopPageLoading, isPageLoading } = usePageLoading()

  useEffect(() => {
    if (!isPageLoading) {
      stopPageLoading(null)
    }
  }, [isPageLoading, stopPageLoading])

  useEffect(() => {
    let interval
    if (!isPageLoading) {
      setFakeProgress(0)
      clearInterval(interval)
    } else {
      interval = setInterval(() => {
        if (fakeProgress < 100) {
          setFakeProgress(fakeProgress + 20)
        } else {
          clearInterval(interval)
        }
      }, 500)
    }
    return () => clearInterval(interval)
  }, [fakeProgress, isPageLoading])

  const visibleClass = isPageLoading ? `height-1 pt-2` : 'height-0 p-0'

  return (
    <div className={`fixed z-max flex w-full bg-color1-300 ${className}`}>
      <div
        className={`w-full bg-gradient-to-r from-color1-400 via-color1-600 to-color1-500 transition-all duration-300 ease-in-out ${visibleClass}`}
        style={{
          width: `${fakeProgress}%`,
        }}
      />
    </div>
  )
}
PageLoading.propTypes = {
  className: PropTypes.string,
}
