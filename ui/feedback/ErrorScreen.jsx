import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Image = dynamic(() => import('ui/data-display/Image'))

export default function ErrorScreen({
  title = "Oops! It's not you, it's us",
  kicker = 'Error',
  message = 'Someting went wrong and the developers team has been notified. Sorry for this inconvenience',
  goHomeLabel = 'Go back to home',
}) {
  return (
    <div className="flex flex-col items-center justify-center bg-white">
      <div className="flex h-screen flex-col items-center justify-between p-8 md:max-w-4xl md:flex-row md:space-x-4 rtl:md:space-x-reverse lg:max-w-6xl">
        <div className="flex max-w-sm items-center justify-center p-4 md:max-w-lg lg:px-8">
          <Image src="/images/error.svg" alt="Error lady and bug" />
        </div>
        <div className="flex grow flex-col space-y-6 p-8 md:justify-center">
          <div className="space-y-2">
            <h3 className="font-semibold text-danger-600 text-xl">{kicker}</h3>
            <h1 className="font-semibold text-color1-700 text-4xl">{title}</h1>
          </div>
          <p className="text-gray-600 text-lg">{message}</p>
          <p>
            <a
              href="/"
              className="text-color1-600 hover:text-color1-400 hover:underline"
            >
              {goHomeLabel}
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
ErrorScreen.propTypes = {
  goHomeLabel: PropTypes.string,
  kicker: PropTypes.string,
  message: PropTypes.string,
  title: PropTypes.string,
}
