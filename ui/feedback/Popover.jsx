import PropTypes from 'prop-types'

import { FloatingPortal, shift, useFloating } from '@floating-ui/react'
import { Popover as UIPopover } from '@headlessui/react'

export default function Popover({
  children,
  panelClass = '',
  placement = 'bottom',
  trigger,
}) {
  const { refs, floatingStyles } = useFloating({
    placement,
    middleware: [shift()],
  })

  return (
    <UIPopover className="relative">
      {({ open }) => (
        <>
          <UIPopover.Button ref={refs.setReference}>{trigger}</UIPopover.Button>
          {open && (
            <span className="absolute left-1/2 z-0 -ml-2 block h-4 w-4 rotate-45 border-l border-t bg-white" />
          )}
          <FloatingPortal>
            <UIPopover.Panel
              className={`absolute z-50 -ml-2 min-w-[320px] px-2 ${panelClass}`}
              ref={refs.setFloating}
              style={floatingStyles}
            >
              {({ close }) => (
                <div className="z-10 m-2 w-full rounded-lg bg-white shadow-xl">
                  {typeof children === 'function'
                    ? children({ close })
                    : children}
                </div>
              )}
            </UIPopover.Panel>
          </FloatingPortal>
        </>
      )}
    </UIPopover>
  )
}
Popover.propTypes = {
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
  panelClass: PropTypes.string,
  placement: PropTypes.string,
  trigger: PropTypes.node,
}
