import React, { useMemo } from 'react'
import PropTypes from 'prop-types'

import { Trans, useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

import Checkbox from 'ui/data-entry/Checkbox'
import Icon from 'ui/icons/Icon'

import Al<PERSON> from '../Alert'
import { useManageNewsletterSubscription } from './api'
import Submit from 'ui/data-entry/Submit'
import { CheckboxGroupController } from '../../data-entry/CheckboxGroup'

const Link = dynamic(() => import('ui/navigation/Link'))
const Form = dynamic(() => import('ui/data-entry/Form'))
const Input = dynamic(() => import('ui/data-entry/Input'))
const CheckboxGroup = dynamic(() => import('ui/data-entry/CheckboxGroup'))

export function NewletterSubscriptionForm({
  action = 'subscribe',
  buttonLabel,
  buttonVariant,
  className = '',
  emailErrorMessage,
  emailLabel,
  emailPlaceholderLabel,
  fields,
  icon,
  requiredMessage,
  successDescription,
  successTitle,
  termsUrl,
}) {
  const { t } = useTranslation()

  const { mutate, isLoading, isSuccess, error } =
    useManageNewsletterSubscription(action)

  const onSubmit = data => {
    mutate(data)
  }

  // These are the messages the field can use to display errors
  const errorMessages = {
    required: requiredMessage || t('errorRequired'),
    emailNotValid: emailErrorMessage || t('errorEmailNotValid'),
  }

  // This is the error message sent by the server if the email is not valid.
  const fieldError = useMemo(
    () => (error ? { type: 'emailNotValid' } : undefined),

    // TODO: Set this up to handle multiple error types. Currently we only want to show the email error.
    // Boolean(error)
    //   ? // errors is stringified object in error.message, of which many errors could be returned.
    //     JSON.parse(error?.message)?.errors?.some(
    //       e => e.type === 'string.email'
    //     )
    //     ? { type: 'emailNotValid' }
    //     : // If error is true, but not the specific email error, then show general error
    //       { type: 'error' }
    //   : undefined,
    [error]
  )

  if (!['subscribe', 'unsubscribe'].includes(action)) {
    return null
  }

  if (isSuccess) {
    return (
      <div className={`flex flex-col gap-4 md:flex-row ${className}`}>
        {/* <h3 className="mb-1 text-lg font-bold uppercase leading-normal lg:text-xl">
          {title || t('newsletterSubscription')}
        </h3> */}
        <Alert
          type="success"
          title={
            successTitle ||
            t(
              action === 'subscribe'
                ? 'newsletterSubscriptionSuccessTitle'
                : 'newsletterUnsubscribeSuccessTitle'
            )
          }
          message={successDescription || ''}
        />
        {/* <Button label={t('done')} onClick={reset} /> */}
      </div>
    )
  }

  return (
    <>
      <Form
        className={`flex flex-col items-start justify-center gap-4 py-6 lg:flex-row lg:justify-start ${className}`}
        onSubmit={onSubmit}
        disabled={isLoading}
        noSpacing
      >
        <div className="flex flex-col gap-4">
          <Input
            className="min-w-[300px]"
            inputClass="border-none shadow h-14" // Look, I'm not proud of this either. It has to match the button, and the button already has the md class as padding. 😅
            name="email"
            type="email"
            label={emailLabel}
            error={fieldError}
            errorMessages={errorMessages}
            placeholder={emailPlaceholderLabel}
            renderIcon={
              isLoading
                ? () => (
                    <Icon
                      name="spinner-third"
                      className="text-secondary absolute right-0 top-0 flex h-full items-center justify-center px-1 py-2"
                      iconClass="animate-spin"
                    />
                  )
                : undefined
            }
            required
          />
          {fields?.map(
            ({ name, label, typeOption, help, options, type, required }) => {
              if (type === 'CheckboxGroup') {
                return (
                  <CheckboxGroup
                    help={help}
                    key={name}
                    label={label}
                    name={name}
                    options={options}
                    required={required}
                  />
                )
              }

              return (
                <Input
                  key={name}
                  name={name}
                  inputClass="border-none shadow h-14"
                  label={label}
                  type={typeOption} // Either 'text' or 'number' in the case of type === 'Input', which we're assuming for now.
                  help={help}
                  required={required}
                />
              )
            }
          )}
          {termsUrl && (
            <Checkbox
              name="terms"
              required
              label={
                <Trans
                  i18nKey="acceptPrivacyPolicy"
                  components={{
                    url: (
                      <Link
                        className="text-color1-700 underline"
                        to={termsUrl}
                      />
                    ),
                  }}
                />
              }
            />
          )}
        </div>

        <div>
          <Submit
            label={buttonLabel || t('subscribe')}
            loading={isLoading}
            variant={buttonVariant}
            icon={icon}
          />
        </div>
      </Form>
    </>
  )
}

NewletterSubscriptionForm.propTypes = {
  action: PropTypes.oneOf(['subscribe', 'unsubscribe']),
  className: PropTypes.string,
  successTitle: PropTypes.string,
  successDescription: PropTypes.string,
  fields: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string,
      label: PropTypes.string,
      type: PropTypes.oneOf(['text']), // Add more types when needed or supported
      required: PropTypes.bool,
      order: PropTypes.number,
    })
  ),
  icon: PropTypes.string,
  termsUrl: PropTypes.string,
  emailLabel: PropTypes.string,
  emailPlaceholderLabel: PropTypes.string,
  buttonLabel: PropTypes.string,
}
