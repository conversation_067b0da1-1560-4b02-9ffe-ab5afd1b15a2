import React, { useMemo } from 'react'
import PropTypes from 'prop-types'

export default function ProgressBar({ className = '', max = 100, value }) {
  const percentageStyle = useMemo(() => {
    const percentage = parseInt((value / max) * 100)
    return { width: `${percentage}%` }
  }, [value, max])

  return (
    <div className={`flex h-4 rounded-md bg-gray-300 ${className}`}>
      <div
        className={`rounded-l-md bg-color1-500 ${
          value === max ? 'rounded-r-md' : ''
        }`}
        style={percentageStyle}
      />
    </div>
  )
}
ProgressBar.propTypes = {
  className: PropTypes.string,
  max: PropTypes.number,
  value: PropTypes.number,
}
