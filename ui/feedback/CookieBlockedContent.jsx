import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

import { useUserCookieConsent } from 'components/CookieConsentProvider'

import { isDocEmpty } from 'ui/typography/RichText'
import clsx from 'clsx'

const Button = dynamic(() => import('ui/buttons/Button'))
const RichText = dynamic(() => import('ui/typography/RichText'))

export default function CookieBlockedContent({
  className,
  blockCondition,
  buttonSize,
  children,
  customTitle,
  customMessage,
  customQuestion,
  customAccept,
  typeCondition,
}) {
  const { t } = useTranslation('cookies')
  const { userConsent, updateUserConsent, disabled } = useUserCookieConsent()

  if (disabled) return children

  return userConsent &&
    (userConsent?.[typeCondition] || userConsent?.[blockCondition]) ? (
    children
  ) : (
    <div
      className={clsx(
        'space-y-8 rounded-md bg-gray-200 p-6 dark:bg-gray-800 dark:text-white md:p-8',
        className
      )}
    >
      <div className="space-y-4">
        <h4 className="text-xl font-bold uppercase">
          {customTitle || t('contentBlocked')}
        </h4>

        {!isDocEmpty(customMessage) ? (
          <RichText doc={customMessage} />
        ) : (
          <div>
            <p>
              {t('contentBlockedMessage', { cookieType: t(typeCondition) })}
            </p>
            <p className="text-lg font-semibold">
              {customQuestion || t('contentBlockedQuestion')}
            </p>
          </div>
        )}
      </div>
      <div className="flex flex-col space-x-0 w-full space-y-4 text-center md:flex-row md:justify-center md:space-x-4 md:space-y-0 rtl:md:space-x-reverse">
        {blockCondition && (
          <Button
            label={t('acceptOnlyContent', { content: t(blockCondition) })}
            onClick={() => {
              updateUserConsent({ ...userConsent, [blockCondition]: true })
            }}
            size={buttonSize}
            labelClass="text-sm md:text-base"
          />
        )}
        <Button
          label={
            customAccept ||
            t('acceptCookieType', { cookieType: t(typeCondition) })
          }
          onClick={() => {
            updateUserConsent({ ...userConsent, [typeCondition]: true })
          }}
          size={buttonSize}
          labelClass="text-sm md:text-base"
        />
      </div>
    </div>
  )
}
CookieBlockedContent.propTypes = {
  className: PropTypes.string,
  blockCondition: PropTypes.string,
  buttonSize: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  children: PropTypes.node,
  customTitle: PropTypes.string,
  customMessage: PropTypes.object,
  customQuestion: PropTypes.string,
  customAccept: PropTypes.string,
  typeCondition: PropTypes.oneOf([
    'necessary',
    'functional',
    'tracking',
    'targeting',
  ]),
}
