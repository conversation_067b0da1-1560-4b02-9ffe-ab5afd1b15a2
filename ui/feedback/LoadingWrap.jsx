import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Loading = dynamic(() => import('ui/feedback/Loading'))

export default function LoadingWrap({ children, loading }) {
  return (
    <div className="relative">
      <div
        className={`z-10 transition-all duration-300 ease-in-out ${
          loading ? 'opacity-70' : 'opacity-100'
        }`}
      >
        {children}
      </div>
      <div
        className={`absolute -inset-2 flex items-center justify-center transition-all duration-300 ease-in-out ${
          loading ? 'opacity-100' : 'scale-0 opacity-0'
        }`}
      >
        <div className="rounded-lg bg-black bg-opacity-60 px-4 text-white">
          <Loading />
        </div>
      </div>
    </div>
  )
}
LoadingWrap.propTypes = {
  children: PropTypes.node,
  loading: PropTypes.bool,
}
