import PropTypes from 'prop-types'
import React from 'react'

import {
  FloatingFocusManager,
  FloatingOverlay,
  FloatingPortal,
  useId,
  useMergeRefs,
} from '@floating-ui/react'

import Button from 'ui/buttons/Button'
import { DialogContext, useDialog, useDialogContext } from './hooks'
import { useSiteDesign } from 'ui/helpers/custom-styles/useSiteDesign'
import { getPlayerModalOffset } from 'ui/data-display/Player/utils/getPlayerModalOffset'

/**
 * Dialog is a component that renders a Dialog.
 * @param {React.ReactNode} children - The content of the Dialog.
 * @param {boolean} initialOpen - If true, the Dialog will be open by default.
 * @param {boolean} open - If true, the Dialog will be open.
 * @param {function} onOpenChange - A function that will be called when the Dialog is opened or closed.
 * @returns {React.ReactNode} - The Dialog component.
 */
export function Dialog({ children, ...options }) {
  const dialog = useDialog(options)
  return (
    <DialogContext.Provider value={dialog}>{children}</DialogContext.Provider>
  )
}
Dialog.propTypes = {
  children: PropTypes.node.isRequired,
  initialOpen: PropTypes.bool,
  open: PropTypes.bool,
  onOpenChange: PropTypes.func,
}

/**
 * DialogContent is a component that renders the content of a Dialog.
 * It can be used as a wrapper for any element.
 * @param {React.ReactNode} children - The content of the Dialog.
 * @param {string} title - The title of the Dialog.
 * @param {string} description - The description of the Dialog.
 * @param {string} boxClass - The class name of the Dialog box.
 * @param {string} contentClass - The class name of the Dialog content.
 * @param {string} overlayClass - The class name of the Dialog overlay.
 * @param {function} getBoxClass - A function that returns the class name of the Dialog content.
 * @param {function} getOverlayClass - A function that returns the class name of the Dialog overlay.
 * @param {React.Ref} ref - A ref to the Dialog content.
 */
export const DialogContent = React.forwardRef(
  function DialogContent(props, propRef) {
    const {
      title,
      description,
      children,
      boxClass,
      getBoxClass,
      overlayClass,
      getOverlayClass,
      contentClass,
      titleClass,
      openClass,
      closeClass,
      closeIconClass,
      transparent,
      ...rest
    } = props

    const {
      context: floatingContext,
      open,
      setOpen,
      ...context
    } = useDialogContext()
    const ref = useMergeRefs([context.refs.setFloating, propRef])
    const { variants: buttonVariants } = useSiteDesign('components.button')

    const labelId = useId()
    const descriptionId = useId()

    const overlayClasses =
      typeof getOverlayClass === 'function'
        ? getOverlayClass(open)
        : overlayClass
    const boxClasses =
      typeof getBoxClass === 'function' ? getBoxClass(open) : boxClass

    const contentMaxHeightOffset = getPlayerModalOffset(
      buttonVariants,
      'xl', // Close button size
      'flat', // Close button variant
      4 // Content gap size
    )

    return (
      <FloatingPortal>
        <FloatingOverlay
          className={`fixed inset-0 flex items-center justify-center bg-black/60 transition-all duration-500 ease-in-out ${
            open
              ? 'z-dialogOpen opacity-100 backdrop-blur-sm'
              : 'pointer-events-none z-dialog opacity-0 backdrop-blur-0'
          } ${overlayClasses}`}
          lockScroll={open}
        >
          <FloatingFocusManager context={floatingContext}>
            <div
              className={`flex-0 fixed flex grow-0 transform-gpu flex-col gap-2 p-6 transition-all duration-500 ease-in-out ${
                open
                  ? openClass || 'translate-y-0 scale-100'
                  : closeClass || 'translate-y-12 scale-90'
              } ${
                transparent
                  ? ''
                  : `bg-white ${open ? 'shadow-xl' : ' shadow-none'}`
              } ${boxClasses} rounded-lg`}
              ref={ref}
              aria-labelledby={title ? labelId : undefined}
              aria-describedby={description ? descriptionId : undefined}
              {...context.getFloatingProps(rest)}
            >
              <div
                className={`flex items-center justify-between gap-4 ${
                  titleClass ? 'pr-4' : ''
                }`}
              >
                <div className={titleClass ? '' : 'px-4'}>
                  {title && (
                    <h2
                      className={` ${titleClass || 'text-lg font-semibold'} ${
                        transparent ? 'text-white' : ''
                      }`}
                      id={labelId}
                    >
                      {title}
                    </h2>
                  )}
                  {description && (
                    <p
                      className={`text-sm ${
                        transparent ? 'text-white/60' : 'text-gray-400'
                      }`}
                      id={descriptionId}
                    >
                      {description}
                    </p>
                  )}
                </div>

                <Button
                  onClick={() => setOpen(false)}
                  icon="times"
                  variant="flat"
                  size="xl"
                  className={closeIconClass}
                />
              </div>
              <div
                data-name="content"
                className={contentClass}
                style={{
                  maxHeight: `calc(100% - ${contentMaxHeightOffset}rem)`,
                }}
              >
                {children}
              </div>
            </div>
          </FloatingFocusManager>
        </FloatingOverlay>
      </FloatingPortal>
    )
  }
)
DialogContent.propTypes = {
  boxClass: PropTypes.string,
  children: PropTypes.node.isRequired,
  contentClass: PropTypes.string,
  description: PropTypes.string,
  getBoxClass: PropTypes.func,
  getOverlayClass: PropTypes.func,
  overlayClass: PropTypes.string,
  title: PropTypes.string,
  transparent: PropTypes.bool,
}

/**
 * Dia§logTrigger is a component that opens a Dialog when clicked.
 * It can be used as a button or as a wrapper for any element.
 *
 * @param {React.ReactNode} children - The content of the trigger.
 * @param {boolean} asChild - If true, the trigger will be rendered as a wrapper for the children.
 */
export const DialogTrigger = React.forwardRef(function DialogTrigger(
  { children, asChild = false, ...props },
  propRef
) {
  const context = useDialogContext()
  const childrenRef = children.ref
  const ref = useMergeRefs([context.refs.setReference, propRef, childrenRef])

  // `asChild` allows the user to pass any element as the anchor
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(
      children,
      context.getReferenceProps({
        ref,
        ...props,
        ...children.props,
        'data-state': context.open ? 'open' : 'closed',
      })
    )
  }

  return (
    <button
      ref={ref}
      // The user can style the trigger based on the state
      data-state={context.open ? 'open' : 'closed'}
      {...context.getReferenceProps(props)}
    >
      {children}
    </button>
  )
})
DialogTrigger.propTypes = {
  children: PropTypes.node.isRequired,
  asChild: PropTypes.bool,
}
