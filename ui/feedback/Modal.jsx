import React from 'react'
import PropTypes from 'prop-types'

export default function Modal({ title }) {
  return (
    <div>
      <div className="fixed top-0 left-0 z-50 w-screen h-screen">
        <div className="absolute w-screen h-screen bg-gray-700 opacity-25"></div>
        <div className="z-20 flex items-center justify-center min-h-screen">
          <div className="container z-20 h-64 max-w-md p-8 bg-white rounded-lg opacity-100">
            <h1>{title}</h1>
          </div>
        </div>
      </div>
    </div>
  )
}

Modal.propTypes = {
  title: PropTypes.string,
}
