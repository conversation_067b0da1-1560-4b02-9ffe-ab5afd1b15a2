import React from 'react'
import PropTypes from 'prop-types'

export default function ButtonToggle({
  activeColor = 'bg-success-500',
  className = '',
  disabled,
  active,
  onChange,
}) {
  const activeClass = active ? activeColor : 'bg-gray-200'
  const disabledClass = disabled
    ? 'opacity-50 cursor-not-allowed'
    : 'cursor-pointer'

  return (
    <button
      className={`w-12 h-6 border border-gray-400 rounded-full transition-colors ease-in-out duration-300 cursor-pointer ${activeClass} ${disabledClass} ${className}`}
      onClick={onChange}
    >
      <span
        className={`block w-6 h-6 -mt-px bg-white border border-gray-400 rounded-full shadow-md transition-all ease-in-out duration-300 ${
          active ? 'ml-6' : 'ml-0'
        }`}
      ></span>
    </button>
  )
}
ButtonToggle.propTypes = {
  activeColor: PropTypes.string,
  className: PropTypes.string,
  active: PropTypes.bool,
  disabled: PropTypes.bool,
  onChange: PropTypes.func,
}
