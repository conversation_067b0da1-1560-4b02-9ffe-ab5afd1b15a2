import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useButtonStyles } from 'ui/helpers/custom-styles/components/button'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))

export default function Button({
  children,
  className = '',
  color,
  disabled,
  fullWidth,
  icon,
  iconClass = '',
  iconPosition = 'left',
  iconStyle,
  id,
  label,
  labelClass = '',
  onClick,
  secondaryIcon,
  secondaryIconClass,
  size,
  title,
  type = 'button',
  url,
  variant = 'base',
}) {
  const iconPositionClass = iconPosition === 'right' ? 'flex-row-reverse' : ''

  const fullWidthClass = fullWidth ? 'w-full' : ''
  const disabledClass = disabled ? 'opacity-60' : ''

  const buttonStyles = useButtonStyles({ variant, size, color })

  return (
    <Link
      type={url ? null : type}
      as={url ? 'a' : 'button'}
      className={`inline-flex shrink-0 select-none items-center justify-center gap-2 whitespace-nowrap text-center leading-none focus:outline-none ${url || onClick ? '' : '!cursor-default'} ${buttonStyles.className} ${fullWidthClass} ${disabledClass} ${className}`}
      disabled={disabled}
      id={id}
      to={disabled ? null : url}
      onClick={disabled ? null : onClick}
      title={title}
      style={buttonStyles.style}
    >
      <div className={`flex flex-row gap-2 ${iconPositionClass}`}>
        {icon && (
          <Icon
            className={`h-full w-full ${iconClass}`}
            iconClassName="h-[1em] w-[1em]"
            name={icon}
            style={iconStyle}
          />
        )}
        {label && (
          <span className={`whitespace-nowrap ${labelClass}`}>
            {label ? label : children}
          </span>
        )}
      </div>
      {secondaryIcon && (
        <Icon
          className={`h-full w-full ${secondaryIconClass}`}
          iconClassName="h-[1em] w-[1em]"
          name={secondaryIcon}
        />
      )}
    </Link>
  )
}

Button.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  fullWidth: PropTypes.bool,
  id: PropTypes.string,
  icon: PropTypes.node,
  iconClass: PropTypes.string,
  iconPosition: PropTypes.oneOf(['left', 'right']),
  iconStyle: PropTypes.object,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  onClick: PropTypes.func,
  size: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  url: PropTypes.string,
  secondaryIcon: PropTypes.string,
  title: PropTypes.string,
  type: PropTypes.oneOf(['button', 'submit', 'cancel']),
  variant: PropTypes.oneOf([
    'base',
    'primary',
    'secondary',
    'tertiary',
    'flat',
    'link',
    'danger',
  ]),
}
