import PropTypes from 'prop-types'
import { useCallback, useState } from 'react'

import { Menu } from '@headlessui/react'

import {
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
  useTransitionStyles,
} from '@floating-ui/react'
import clsx from 'clsx'
import { isFunction } from 'lodash'
import Button from 'ui/buttons/Button'
import { Search } from 'ui/data-entry/Search'
import { getBackgroundColor } from 'ui/helpers/getColor'
import useBorder from 'ui/helpers/useBorder'
import useBorderRadius from 'ui/helpers/useBorderRadius'
import useDivider from 'ui/helpers/useDivider'
import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'
import Icon from 'ui/icons/Icon'
import Link from 'ui/navigation/Link'

/**
 * A button with a menu as a dropdown list.
 */
export default function Dropdown({
  alignment = { xs: 'bottom-start' },
  bgColor,
  border,
  borderRadius,
  button,
  children,
  className = '',
  color,
  direction,
  divider,
  hasMaxHeight = true,
  hideArrow,
  icon,
  iconClass,
  id,
  itemsAs,
  itemsClass,
  label,
  labelClass,
  offset: offsetValue = 8,
  onClick,
  openedIcon,
  panelAs,
  panelClass,
  secondaryIconClass,
  size: buttonSize = 'md',
  variant = 'secondary',
}) {
  const [isOpen, setIsOpen] = useState(false)

  const placement = useValueAtBreakpoint(alignment)

  const bgColorClass = bgColor ? getBackgroundColor(bgColor) : 'bg-white'
  const borderClasses = useBorder(border) ?? 'border border-color1-500/50'
  const borderRadiusClasses = useBorderRadius(borderRadius) ?? 'rounded-lg'
  const dividerClasses = useDivider(divider, direction)
  const maxHeightClass = hasMaxHeight ? 'max-h-96' : ''

  const fromBottom = placement?.startsWith('bottom')

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    placement,
    middleware: [
      shift(),
      offset(parseInt(offsetValue, 10)),
      size({
        apply({ rects, elements }) {
          Object.assign(elements.floating.style, {
            minWidth: `${rects.reference.width}px`,
          })
        },
      }),
    ],
  })
  const { isMounted, styles } = useTransitionStyles(context, {
    initial: {
      opacity: 0,
      transform: fromBottom ? 'translateY(8px)' : 'translateY(-4px)',
    },
    open: {
      opacity: 1,
      transform: 'translateY(0)',
    },
  })

  const toggle = useCallback(() => {
    setIsOpen(prevIsOpen => !prevIsOpen)
    onClick?.()
  }, [onClick])

  const close = useCallback(() => {
    setIsOpen(false)
  }, [])

  const click = useClick(context)
  const dismiss = useDismiss(context)

  const { getReferenceProps, getFloatingProps } = useInteractions([
    click,
    dismiss,
  ])

  const Panel = panelAs ?? 'div'

  return (
    <Menu
      as="div"
      className={clsx('relative', className)}
      id={id}
      open={isMounted}
    >
      <Menu.Button
        as="div"
        className="flex items-center focus:outline-none"
        onClick={toggle}
        ref={refs.setReference}
        {...getReferenceProps()}
      >
        {button ? (
          typeof button === 'function' ? (
            button({ open: isOpen })
          ) : (
            button
          )
        ) : (
          <Button
            label={label}
            labelClass={labelClass}
            icon={isMounted ? openedIcon ?? icon : icon}
            iconClass={iconClass}
            color={color}
            pressed={isMounted}
            secondaryIcon={hideArrow ? undefined : 'chevron-down'}
            secondaryIconClass={clsx(
              'transition-all ease-in-out duration-300 opacity-50 text-xs',
              {
                'rotate-180': isMounted,
              },
              secondaryIconClass
            )}
            size={buttonSize}
            variant={variant}
          />
        )}
      </Menu.Button>
      {isMounted && (
        <FloatingPortal>
          <Panel
            className={clsx('absolute z-max', panelClass)}
            {...getFloatingProps()}
            ref={refs.setFloating}
            style={floatingStyles}
          >
            <Menu.Items
              as={itemsAs}
              className={clsx(
                'flex flex-col shadow-lg drop-shadow-md focus:outline-none',
                {
                  'overflow-y-auto': hasMaxHeight,
                },
                bgColorClass,
                borderClasses,
                borderRadiusClasses,
                dividerClasses,
                itemsClass,
                maxHeightClass
              )}
              style={styles}
              static
            >
              {typeof children === 'function'
                ? children({ open: isMounted, close })
                : children}
            </Menu.Items>
          </Panel>
        </FloatingPortal>
      )}
    </Menu>
  )
}
const alignmentPropType = PropTypes.oneOf([
  'bottom-start',
  'bottom-end',
  'top-start',
  'top-end',
])
Dropdown.propTypes = {
  alignment: PropTypes.shape({
    xs: alignmentPropType,
    sm: alignmentPropType,
    md: alignmentPropType,
    lg: alignmentPropType,
    xl: alignmentPropType,
  }),
  bgColor: PropTypes.string,
  border: PropTypes.object,
  borderRadius: PropTypes.object,
  button: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
  className: PropTypes.string,
  direction: PropTypes.string,
  divider: PropTypes.object,
  hasMaxHeight: PropTypes.bool,
  hideArrow: PropTypes.bool,
  icon: PropTypes.node,
  iconClass: PropTypes.string,
  id: PropTypes.string,
  itemsAs: PropTypes.string,
  itemsClass: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  name: PropTypes.string,
  offset: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onClick: PropTypes.func,
  openedIcon: PropTypes.node,
  panelAs: PropTypes.string,
  panelClass: PropTypes.string,
  secondaryIconClass: PropTypes.string,
  size: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  variant: PropTypes.oneOf([
    'base',
    'primary',
    'secondary',
    'tertiary',
    'success',
    'info',
    'warn',
    'danger',
    'flat',
    'link',
    'light',
  ]),
}

const itemTypesStyles = {
  basic: {
    active: 'bg-color1-100 text-color1-600',
    normal: 'text-gray-600 hover:bg-color1-600 hover:text-color1-50',
    selected: 'bg-color1-100 text-color1-500',
    disabled: 'text-gray-300',
  },
  warn: {
    active: 'bg-warning-500 text-warning-50',
    normal: 'text-warning-500 hover:bg-warning-500 hover:text-warning-50',
    selected: 'bg-warning-100 text-warning-500',
    disabled: 'text-warning-300',
  },
  danger: {
    active: 'bg-danger-500 text-danger-50',
    normal: 'text-danger-500 hover:bg-danger-500 hover:text-danger-50',
    selected: 'bg-danger-100 text-danger-500',
    disabled: 'text-danger-300',
  },
}

/**
 * The individual Dropdown list item
 */
export function DropdownItem({
  as,
  className = '',
  disabled,
  href,
  icon,
  label,
  help,
  loading,
  onClick,
  CustomLink,
  selected,
  type = 'basic',
  ...rest
}) {
  const disabledClass = disabled ? '!cursor-not-allowed' : 'cursor-pointer'
  const typeClass = itemTypesStyles[type] ?? itemTypesStyles.basic

  // If a custom link component is provided, use it, otherwise use the default Link component
  const LinkComponent = CustomLink ?? Link

  return (
    <Menu.Item {...rest} as={as} disabled={disabled} label={label}>
      {({ active }) => (
        <LinkComponent
          className={`group flex flex-row items-center justify-between gap-3 px-4 py-3 text-start transition-colors duration-300 ease-in-out ${
            typeClass[
              disabled
                ? 'disabled'
                : selected
                  ? 'selected'
                  : active
                    ? 'active'
                    : 'normal'
            ]
          } ${disabledClass} ${className}`}
          to={href}
          onClick={onClick}
          basic={false}
        >
          {(loading || icon) && (
            <div className="flex w-4 items-center justify-center">
              <Icon name={loading ? 'spinner-third' : icon} spin={loading} />
            </div>
          )}
          <div className="flex flex-grow flex-col whitespace-nowrap">
            <span className={selected ? 'font-semibold' : 'font-normal'}>
              {label}
            </span>
            {help && <small className="text-xs opacity-75">{help}</small>}
          </div>
          {selected && (
            <div className="flex w-4 items-center justify-center text-sm">
              <Icon name="check" />
            </div>
          )}
        </LinkComponent>
      )}
    </Menu.Item>
  )
}
DropdownItem.propTypes = {
  as: PropTypes.node,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  href: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  icon: PropTypes.node,
  label: PropTypes.node.isRequired,
  loading: PropTypes.bool,
  onClick: PropTypes.func,
  selected: PropTypes.bool,
  type: PropTypes.oneOf(['basic', 'warn', 'danger']),
}
Dropdown.Item = DropdownItem
Dropdown.Item.displayName = 'Dropdown.Item'

/**
 * The divider item
 */
export function DropdownDivider() {
  return <div className="my-1 border-b border-gray-300" />
}

Dropdown.Divider = DropdownDivider
Dropdown.Divider.displayName = 'Dropdown.Divider'

/**
 * The search item
 */
export function DropdownSearch({ onSearch, value, placeholder }) {
  return (
    <Search
      className="p-4"
      onChange={e => isFunction(onSearch) && onSearch(e.target.value)}
      onKeyDown={e => {
        if (e.code === 'Space') {
          e.stopPropagation()
        }
      }}
      value={value}
      showButton={false}
      placeholder={placeholder}
      icon="search"
    />
  )
}
DropdownSearch.propTypes = {
  onSearch: PropTypes.func,
  placeholder: PropTypes.string,
  value: PropTypes.string,
}
Dropdown.Search = DropdownSearch
Dropdown.Search.displayName = 'Dropdown.Search'
