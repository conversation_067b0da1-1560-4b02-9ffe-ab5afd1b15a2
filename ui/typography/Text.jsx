import React from 'react'

import { getTextColor } from 'ui/helpers/getColor'
import useFontWeight from 'ui/helpers/useFontWeight'
import useTextSize from 'ui/helpers/useTextSize'
import { useFontFamily } from 'ui/helpers/useFontFamily'
import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'
import useTextAlign from 'ui/helpers/useTextAlign'

/**
 */

/**
 * Text component
 * @param {Object|import('ui/helpers/useTextAlign').TextAlign} align The alignment of the text
 * @param {'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'p' | 'blockquote'} [as='p'] The HTML element to use for the text
 * @param {string} children The content of the text
 * @param {string} className Additional classes for the text
 * @param {string} color The color of the text
 * @param {string} defaultColorClass The default color class for the text
 * @param {string} defaultTextSize The default text size
 * @param {string} fontFamily The font family of the text
 * @param {import('ui/helpers/useFontWeight').FontWeight} fontWeight The font weight of the text
 * @param {string} id The id of the text
 * @param {boolean} noWrap Whether to wrap the text
 * @param {string} text The text content
 * @param {string} textCase The text case of the text
 * @param {import('ui/helpers/useTextSize').TextSize} textSize The text size of the text
 * @param {string} textSizeCustom The custom text size of the text
 * @returns {React.ReactElement} The Text component
 */
export default function Text({
  align = { xs: 'start' },
  as = 'p',
  children,
  className = '',
  color,
  defaultColorClass = '',
  defaultTextSize,
  fontFamily,
  fontWeight,
  id,
  noWrap,
  text,
  textCase = '',
  textSize,
  textSizeCustom,
}) {
  const alignClass = useTextAlign(align)
  const fontFamilyClass = useFontFamily(fontFamily)
  const fontWeightClass = useFontWeight(fontWeight)
  const textSizeClass = useTextSize(textSize, defaultTextSize)

  const textSizeValue = useValueAtBreakpoint(textSize)
  const textSizeCustomValue = useValueAtBreakpoint(textSizeCustom?.value)

  const colorClass = getTextColor(
    color,
    defaultColorClass || 'text-gray-800 dark:text-white'
  )

  const whiteSpaceClass = noWrap ? 'whitespace-nowrap' : ''

  return React.createElement(
    as,
    {
      id,
      className: `${colorClass} ${whiteSpaceClass} ${alignClass} ${textSizeClass} ${fontFamilyClass} ${fontWeightClass} ${textCase} ${className}`,
      style:
        textSizeValue === 'custom'
          ? {
              fontSize: `${textSizeCustomValue}${textSizeCustom?.unit}`,
              lineHeight: 1,
            }
          : {},
    },
    text || children
  )
}
