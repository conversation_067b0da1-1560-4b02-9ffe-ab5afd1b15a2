import React from 'react'
import PropTypes from 'prop-types'

import omit from 'lodash/omit'

import Text from './Text'

export default function Paragraph({ children, className, indent, ...props }) {
  const indentClass = indent ? 'px-0 sm:px-8 md:px-12 lg:px-20' : ''

  return (
    <Text
      {...props}
      as="p"
      className={`${indentClass} ${className}`}
      defaultTextSize="lg"
    >
      {children}
    </Text>
  )
}

Paragraph.propTypes = {
  ...omit(Text.propTypes, 'as'),
  indent: PropTypes.bool,
}
