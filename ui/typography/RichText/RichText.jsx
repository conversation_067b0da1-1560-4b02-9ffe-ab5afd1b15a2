import React from 'react'
import Serialize from './components/Serialize'

/**
 * RichText component
 * @param {Object} props The component props
 * @param {string} props.className Additional class names to apply to the component
 * @param {Object} props.doc The tiptap document to render
 * @param {string} props.id The id to apply to the component
 * @param {boolean} props.decreaseHeaders Whether to decrease the size of headers
 * @param {string} props.textSize The text size to apply to the component
 * @param {string} props.color The color to apply to the component
 * @returns {React.ReactElement|null} The rendered component or null if the doc is not valid
 */
export default function RichText({
  className = '',
  doc,
  id,
  decreaseHeaders,
  textSize,
  color,
}) {
  const text =
    typeof doc === 'object' && Array.isArray(doc.content)
      ? doc.content.map((node, i) => (
          <Serialize
            node={node}
            key={`node-${i}`}
            id={`node-${i}`}
            renderProps={{
              decreaseHeaders,
              textSize,
              color,
              docContent: doc.content, // Pass the whole doc content to the serialize function
            }}
          />
        ))
      : null

  if (!text) return null

  return (
    <div className={`prose max-w-none dark:prose-invert ${className}`} id={id}>
      {text}
    </div>
  )
}
