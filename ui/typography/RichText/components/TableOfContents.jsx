import { slugify } from 'utils/strings'

const intentationClasses = {
  1: 'ml-4',
  2: 'ml-8',
  3: 'ml-12',
  4: 'ml-16',
  5: 'ml-20',
  6: 'ml-24',
}

/**
 * Table of Content block component
 * @param {Object} props Component props
 * @param {string} props.title Title of the table of contents
 * @param {Array} props.headings List of headings
 * @returns {React.ReactElement} Table of Content component
 */
export default function TableOfContent({ title, headings }) {
  return (
    <div className="not-prose flex flex-col gap-1">
      {title && (
        <label className="text-xl font-bold text-gray-700 dark:text-gray-100">
          {title}
        </label>
      )}
      <ul className="flex flex-col gap-1 p-2 text-gray-400">
        {headings.map((headingNode, index) => {
          const { attrs, content } = headingNode
          const { level } = attrs
          const text = content.map(n => n.text).join('')
          const id = slugify(text)

          return (
            <li
              className={`list-disc ${intentationClasses[level]}`}
              key={`${id}-${index}`}
            >
              <a
                className="text-color1-800 hover:underline dark:text-color1-100"
                href={`#${id}`}
              >
                {text}
              </a>
            </li>
          )
        })}
      </ul>
    </div>
  )
}
