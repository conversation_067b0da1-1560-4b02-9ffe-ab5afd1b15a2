import React from 'react'

import { intersperse } from 'utils/arrays'

import { mappings } from './mappings'

/**
 * Serializes a tiptap node to React element
 * @param {Object} props The component props
 * @param {Object} props.node The node to serialize
 * @param {string} props.key The key to use for the node
 * @param {Object} props.renderProps The props to pass to the render function
 * @param {string} props.renderProps.textSize The text size to apply to the component
 * @param {string} props.renderProps.color The color to apply to the component
 * @param {string} props.renderProps.decreaseHeaders Whether to decrease the size of headers
 * @param {string} props.renderProps.className Additional class names to apply to the component
 * @param {string} props.renderProps.docContent The full content of the document
 * @returns {React.ReactElement|null}
 */
export default function Serialize({ node, id, renderProps = {} }) {
  // TEXT (LEAF) NODE:
  if (node.type === 'text' && typeof node.text === 'string') {
    // Set node text as initial children
    let children = node.text

    if (children.match('\n')) {
      const lines = children.split('\n')

      children = intersperse(lines, index => (
        <br key={`text-br-${id}-${index}`} />
      ))
    }

    // Nest children in mark elements (if any)
    if (node.marks) {
      // Bold in links have to be handled differently, if the text of a link is bold, or italics then the style is applied inside the link
      // Then the link must be sorted to the end of the marks.
      const sortedMarks = [...node.marks].sort((a, b) => {
        if (a.type === 'link') return 1 // a comes first
        if (b.type === 'link') return -1 // b comes first
        return 0
      })

      let i = 0
      for (const mark of sortedMarks) {
        const { tag, render, className } = mappings[mark.type] || mappings.span
        const textKey = `text-${id}-${i}`

        children =
          typeof render === 'function'
            ? render(mark, textKey, children, renderProps)
            : React.createElement(
                tag,
                {
                  className,
                  key: textKey,
                },
                children
              )
        i++
      }
    }

    return children
  }

  // BLOCK (ELEMENT) NODE:
  else {
    const { type, content, attrs } = node
    const nodeConf = mappings[type]

    if (!nodeConf) {
      console.warn(`Missing render for block "${type}"`) //  eslint-disable-line no-console
      return null
    }

    const { tag, render, className } = nodeConf
    const children = content?.map((childNode, i) => (
      <Serialize
        node={childNode}
        id={`${id}-${i}`}
        key={`node-${i}`}
        renderProps={renderProps}
      />
    ))

    return typeof render === 'function'
      ? render(node, id, children, renderProps)
      : React.createElement(tag, { key: id, className, ...attrs }, children)
  }
}
