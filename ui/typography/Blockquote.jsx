import React from 'react'
import PropTypes from 'prop-types'

import Text from './Text'

export default function Blockquote({
  id,
  children,
  className = '',
  color,
  textSize,
  textSizeCustom,
  fontWeight,
}) {
  return (
    <Text
      as="blockquote"
      className={`relative pl-6 font-serif italic leading-tight before:absolute before:-left-1 before:top-0 before:opacity-50 before:content-quote before:text-4xl ${className}`}
      color={color}
      defaultColorClass="text-gray-600 dark:text-white"
      id={id}
      textSize={textSize}
      textSizeCustom={textSizeCustom}
      defaultTextSize="xl"
      fontWeight={fontWeight}
    >
      {children}
    </Text>
  )
}

Blockquote.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  color: PropTypes.string,
  fontWeight: PropTypes.string,
  id: PropTypes.string,
  indent: PropTypes.bool,
  textSize: PropTypes.object,
  textSizeCustom: PropTypes.object,
}
