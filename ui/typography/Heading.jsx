import Text from './Text'

const sizes = {
  h1: '4xl',
  h2: '3xl',
  h3: '2xl',
  h4: 'xl',
  h5: 'lg',
}

/**
 * Heading component
 * @param {object} props The props for the Heading component
 * @param {'h1'|'h2'|'h3'|'h4'|'h5'} [props.as='h1'] The HTML element to use for the heading
 * @param {string} props.children The content of the heading
 * @param {string} props.className Additional classes for the heading
 * @param {string} props.color The color of the text
 * @param {string} props.title The title of the heading
 * @param {string} [props.fontWeight='bold'] The font weight of the text
 * @returns {React.ReactElement} The Heading component
 */
export default function Heading({
  as = 'h1',
  children,
  className,
  color,
  title,
  fontWeight = 'bold',
  ...props
}) {
  return (
    <Text
      {...props}
      as={as}
      className={
        color ? className : `text-gray-700 dark:text-gray-200 ${className}`
      }
      color={color}
      defaultTextSize={sizes[as] || sizes.h1}
      fontWeight={fontWeight}
    >
      {title || children}
    </Text>
  )
}
