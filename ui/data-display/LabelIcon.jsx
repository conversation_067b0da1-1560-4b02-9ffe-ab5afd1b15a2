import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function LabelIcon({
  className = '',
  icon,
  iconClass = '',
  label,
  labelClass = '',
  reverse,
  vertical,
}) {
  return (
    <span
      className={`flex items-center leading-tight ${
        vertical
          ? `flex-col space-y-2 ${
              reverse ? 'flex-col-reverse space-y-reverse' : ''
            }`
          : `flex-row space-x-2 ${
              reverse
                ? 'flex-row-reverse space-x-reverse'
                : 'rtl:space-x-reverse'
            }`
      } ${className}`}
    >
      {icon && <Icon className={iconClass} name={icon} />}
      <span className={`leading-tight ${labelClass}`}>{label}</span>
    </span>
  )
}

LabelIcon.propTypes = {
  className: PropTypes.string,
  icon: PropTypes.string,
  iconClass: PropTypes.string,
  label: PropTypes.node.isRequired,
  labelClass: PropTypes.string,
  vertical: PropTypes.bool,
  reverse: PropTypes.bool,
}
