import React from 'react'
import PropTypes from 'prop-types'

export default function ListTitle({ title, extra }) {
  if (!title && !extra) return null

  return (
    <div className="flex items-center justify-between space-x-4 rtl:space-x-reverse">
      <h3 className="font-bold uppercase leading-normal text-lg lg:text-xl">
        {title}
      </h3>
      {extra && <div>{extra}</div>}
    </div>
  )
}
ListTitle.propTypes = {
  title: PropTypes.node,
  extra: PropTypes.node,
}
