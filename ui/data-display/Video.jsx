import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Player = dynamic(() => import('ui/data-display/Player'))

export default function Video({
  caption,
  className,
  copyright,
  ...playerProps
}) {
  return (
    <div className={`overflow-hidden rounded-md ${className}`}>
      <Player {...playerProps} />
      {(caption || copyright) && (
        <div className="bg-gray-100 px-8 py-4">
          {caption && <div className="font-bold">{caption}</div>}
          {copyright && <div className="mt-1">{copyright}</div>}
        </div>
      )}
    </div>
  )
}

Video.propTypes = {
  accountId: PropTypes.string,
  caption: PropTypes.string,
  className: PropTypes.string,
  copyright: PropTypes.string,
  id: PropTypes.string,
  playerId: PropTypes.string,
  provider: PropTypes.oneOf(['youtube', 'vimeo', 'jetstream', 'soundcloud']),
}
