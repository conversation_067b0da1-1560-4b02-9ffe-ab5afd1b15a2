import clsx from 'clsx'
import PropTypes from 'prop-types'

import useBackgroundImage from 'ui/helpers/useBackroundImage'

export default function Box({
  bgImage,
  children,
  className = 'p-6',
  id,
  positionClass,
  style,
}) {
  const bgImageStyle = useBackgroundImage(bgImage, 'center', 600)

  if (!children) return null

  return (
    <div
      className={clsx(
        {
          'overflow-hidden rounded-lg bg-cover bg-no-repeat': bgImage,
        },
        positionClass ?? 'relative',
        className
      )}
      id={id}
      style={{ ...style, ...(bgImageStyle || {}) }}
    >
      {children}
    </div>
  )
}
Box.propTypes = {
  bgImage: PropTypes.object,
  children: PropTypes.node,
  className: PropTypes.string,
  id: PropTypes.string,
  positionClass: PropTypes.string,
  style: PropTypes.object,
}
