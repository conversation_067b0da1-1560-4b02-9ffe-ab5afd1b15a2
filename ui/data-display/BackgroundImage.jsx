import React from 'react'
import PropTypes from 'prop-types'

import useBackgroundImage from 'ui/helpers/useBackroundImage'

export default function BackgroundImage({
  className = '',
  dark,
  image,
  position = 'center',
  quality,
  width,
}) {
  const bgStyle = useBackgroundImage(image, position, width, quality)

  if (!bgStyle) return null

  const darkClass = dark ? 'brightness-50' : ''

  return (
    <div
      className={`absolute inset-0 bg-cover bg-no-repeat ${darkClass} ${className}`}
      style={bgStyle}
    />
  )
}
BackgroundImage.propTypes = {
  className: PropTypes.string,
  dark: PropTypes.bool,
  image: PropTypes.object,
  position: PropTypes.string,
  quality: PropTypes.number,
  width: PropTypes.number,
}
