import React, { useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { useTranslation } from 'next-i18next'

import { ConditionalLink } from 'ui/navigation/Link'
import useBorderRadius from 'ui/helpers/useBorderRadius'

const Button = dynamic(() => import('ui/buttons/Button'))
const Image = dynamic(() => import('ui/data-display/Image'))
const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const PictureDialog = dynamic(() => import('./PictureDialog'))

const alignments = {
  'none': '',
  'left': 'w-full mr-0 sm:w-1/2 sm:mr-8 sm:float-left md:w-1/3',
  'right': 'w-full mr-0 sm:w-1/2 sm:ml-8 sm:float-right md:w-1/3',
  'center-full': 'w-full mx-0',
  'center-medium': 'mx-0 md:mx-12 md:mx-20',
}

const transformOrigin = {
  center: 'origin-center',
  left: 'origin-left',
  right: 'origin-right',
}

export default function Picture({
  alt,
  align = 'none',
  aspectRatio,
  borderRadius,
  caption,
  className = '',
  copyright,
  clickToEnlarge,
  enableAnimation,
  animationOrigin = 'center',
  file,
  hasCustomWidth,
  id,
  imageClass = '',
  innerClassName = '',
  onClick,
  rounded = true,
  roundedTop,
  roundedBottom,
  sizes,
  src,
  style,
  transparent,
  url,
}) {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)
  const hasDescription = caption || copyright
  const pictureAlt = alt || caption || copyright

  const borderRadiusClasses = useBorderRadius(borderRadius)
  const transparentClass = transparent ? '' : 'bg-gray-100 dark:bg-gray-800'

  const widthClass = hasCustomWidth ? '' : 'w-full'

  const roundedClass = borderRadius
    ? borderRadiusClasses
    : transparent
      ? ''
      : roundedTop
        ? 'rounded-t-md'
        : roundedBottom
          ? 'rounded-b-md'
          : rounded
            ? 'rounded-3xl'
            : ''
  // This design is now set to have 3xl as rounded, it could be a nice addition to be able to set this from site appearance

  const onToggle = useCallback(() => {
    if (clickToEnlarge) {
      setOpen(!open)
    }
  }, [clickToEnlarge, open])

  return (
    <>
      <Clickable
        as="figure"
        className={`relative flex flex-col justify-center ${widthClass} ${alignments[align]} ${
          enableAnimation
            ? `transition-all duration-200 hover:scale-105 ${transformOrigin[animationOrigin]}`
            : ''
        } ${className}`}
        id={id}
        onClick={clickToEnlarge ? onToggle : onClick}
        style={style}
      >
        <div
          className={`group relative flex w-full flex-col overflow-hidden ${transparentClass} ${roundedClass} ${innerClassName}`}
          id={id}
        >
          <ConditionalLink to={url} condition={url}>
            <Image
              className={`w-full m-0 ${imageClass} ${
                transparent && rounded ? 'overflow-hidden rounded-md' : ''
              } ${
                enableAnimation
                  ? 'hover:scale-105 duration-300 transition-all'
                  : ''
              }`}
              alt={pictureAlt}
              aspectRatio={aspectRatio}
              src={src}
              file={file}
              sizes={sizes}
            />
          </ConditionalLink>

          {(hasDescription || clickToEnlarge) && (
            <div
              className={`flex items-center justify-between ${transparentClass} ${
                transparent
                  ? 'absolute bottom-0 left-0 right-0 translate-y-full bg-gradient-to-b from-transparent to-gray-800 pt-4 transition-transform group-hover:translate-y-0'
                  : 'relative'
              }`}
            >
              {hasDescription ? (
                <div className="px-4 py-3 tracking-wide">
                  {caption && (
                    <div
                      className={`font-semibold text-sm md:text-base ${
                        transparent
                          ? 'text-white'
                          : 'text-gray-800 dark:text-gray-400'
                      }`}
                    >
                      {caption}
                    </div>
                  )}
                  {copyright && (
                    <div className="text-gray-500 text-xs md:text-sm">
                      {copyright}
                    </div>
                  )}
                </div>
              ) : (
                <div />
              )}
              {clickToEnlarge && (
                <Button
                  title={t('enlarge')}
                  onClick={() => {}}
                  size="lg"
                  color="none"
                  variant="flat"
                  icon="expand-arrows"
                  className={`${transparent ? 'text-gray-100' : 'text-primary-700'} px-3`}
                />
              )}
            </div>
          )}
        </div>
      </Clickable>
      {clickToEnlarge && (
        <PictureDialog
          caption={caption}
          copyright={copyright}
          file={file}
          onClose={() => setOpen(false)}
          open={open}
          pictureAlt={pictureAlt}
          src={src}
        />
      )}
    </>
  )
}
Picture.propTypes = {
  alt: PropTypes.string,
  align: PropTypes.oneOf([
    'none',
    'left',
    'right',
    'center-full',
    'center-medium',
  ]),
  aspectRatio: PropTypes.string,
  borderRadius: PropTypes.object,
  caption: PropTypes.string,
  className: PropTypes.string,
  clickToEnlarge: PropTypes.bool,
  copyright: PropTypes.string,
  file: PropTypes.object,
  hasCustomWidth: PropTypes.bool,
  id: PropTypes.string,
  imageClass: PropTypes.string,
  innerClassName: PropTypes.string,
  onClick: PropTypes.func,
  rounded: PropTypes.bool,
  roundedBottom: PropTypes.bool,
  roundedTop: PropTypes.bool,
  src: PropTypes.string,
  sizes: PropTypes.string,
  style: PropTypes.object,
  transparent: PropTypes.bool,
  url: PropTypes.string,
}
