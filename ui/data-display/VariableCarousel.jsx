import React, { useEffect, useRef, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))

/*
 * This carousel currently centers the buttons to the image in the first item in the carousel.
 * This is done by passing a ref to the first item in the carousel, and then using that ref to get the height of the first item.
 * Please put appropriate classnames on the item.
 */
export default function VariableCarousel({
  color,
  firstItemRef,
  children,
  gradientColor,
}) {
  const trackRef = useRef()

  const [buttonHeight, setButtonHeight] = useState(0) // This is the top of the button, so we can position it correctly
  const [scrollPosition, setScrollPosition] = useState(0)
  const [scrollWidth, setScrollWidth] = useState(0)

  // We get the height of the image to position the buttons correctly through the use of a ResizeObserver
  // The image will start at height 0, and then when the image is loaded, the height will be set.
  useEffect(() => {
    const firstElement = firstItemRef.current?.getElementsByTagName('img')?.[0]
    if (firstElement) {
      const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          if (entry.target === firstElement) {
            setButtonHeight(entry.contentRect.height)
          }
        }
      })

      resizeObserver.observe(firstElement)

      // Cleanup function
      return () => {
        resizeObserver.disconnect()
      }
    }
  }, [firstItemRef])

  // This is to set the scroll position so we can hide the left button when we are at the beginning
  useEffect(() => {
    const handleScroll = () => {
      if (trackRef.current) {
        setScrollPosition(trackRef.current.scrollLeft)
        setScrollWidth(
          trackRef.current.scrollWidth - trackRef.current.clientWidth
        )
      }
    }

    handleScroll()
    const current = trackRef.current

    current?.addEventListener('scroll', handleScroll)

    return () => {
      current?.removeEventListener('scroll', handleScroll)
    }
  }, [trackRef, scrollPosition])

  // if (episodes?.count === 0) return null

  const onDirectionClick = direction => {
    return () => {
      if (trackRef.current) {
        const trackWidth = trackRef.current.offsetWidth
        const scrollPercentage = 0.9 // 20% scroll
        const scrollAmount = trackWidth * scrollPercentage
        trackRef.current.scrollBy({
          left: direction === 'left' ? -scrollAmount : scrollAmount,
          behavior: 'smooth',
        })
      }
    }
  }

  return (
    <div className="group relative flex max-w-full overflow-x-auto md:flex-row">
      <NavButton
        scrollPosition={scrollPosition}
        onDirectionClick={onDirectionClick}
        height={buttonHeight}
        direction="left"
        color={color}
        gradientColor={gradientColor}
      />

      <NavButton
        scrollPosition={scrollPosition}
        onDirectionClick={onDirectionClick}
        height={buttonHeight}
        direction="right"
        scrollWidth={scrollWidth}
        color={color}
        gradientColor={gradientColor}
      />

      <div
        ref={trackRef}
        className={`relative flex snap-x snap-proximity flex-row gap-8 overflow-x-auto no-scrollbar rtl:flex-row-reverse`}
      >
        {children}
      </div>
    </div>
  )
}
VariableCarousel.propTypes = {
  children: PropTypes.node,
  episodes: PropTypes.shape({
    count: PropTypes.number,
    items: PropTypes.arrayOf(PropTypes.object),
  }),
}

function NavButton({
  scrollPosition,
  scrollWidth,
  onDirectionClick,
  height,
  gradientColor,
  direction,
  color,
}) {
  const positionClass = direction === 'left' ? 'left-0' : 'right-0'
  const hiddenClass =
    scrollPosition === (direction === 'left' ? 0 : scrollWidth) ||
    scrollPosition + 1 <= (direction === 'right' ? 0 : scrollWidth)
      ? 'hidden'
      : ''
  const gradientClass =
    direction === 'left' ? 'bg-gradient-to-l' : 'bg-gradient-to-r'

  return (
    <button
      onClick={onDirectionClick(direction)}
      className={`pointer-events-none absolute z-20 flex items-center justify-center p-2 align-middle text-gray-600  ${positionClass} ${hiddenClass} ${
        color ? `hover:text-${color}` : 'group-hover:text-color3-900'
      } ${gradientColor} ${gradientClass}`}
      style={{ height: `${height + 1}px` }}
    >
      <Icon
        name={direction === 'left' ? 'chevron-left' : 'chevron-right'}
        size={50}
        className={`text-4xl opacity-0 transition-opacity duration-300  group-hover:pointer-events-auto  group-hover:opacity-100 md:text-6xl`}
      />
    </button>
  )
}

// You can send values to override the default values to customize the item.
export function VariableCarouselItem({
  index,
  length,
  firstItemRef,
  firstMargin,
  lastMargin,
  margin,
  className,
  children,
}) {
  // TODO: Find a good way to set these values like min-w-[calc(25%-44px)] in the parent component, or even in the backend
  // Probably should be customizable in the appearance tab in the backend.
  // TODO: get me a good default style we could have here, and then set reminded design to be a custom style all from the backend.

  const firstItemMargin = firstMargin || 'ml-10'

  const lastItemMargin = lastMargin || 'ml-0 mr-10'

  const marginClass = margin || ''

  // If a className is passed, use that, otherwise use the default className
  const itemClass =
    className ||
    `min-w-[250px] touch-auto snap-start scroll-mx-10 xl:min-w-[calc(25%-44px)]`

  return (
    <div
      ref={index === 0 ? firstItemRef : null}
      className={`${
        index === 0
          ? firstItemMargin
          : length - 1 === index
            ? lastItemMargin
            : marginClass
      } ${itemClass}`}
    >
      {children}
    </div>
  )
}
