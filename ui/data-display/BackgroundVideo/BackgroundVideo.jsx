import PropTypes from 'prop-types'
import React from 'react'

import { useImageUrl } from '../Image'
import Player from '../Player'

export function BackgroundVideo({
  accountId,
  className,
  poster,
  provider,
  videoId,
  style,
}) {
  const posterUrl = useImageUrl(poster, 1200)

  if (!videoId || !provider) {
    return null
  }

  return (
    <div
      className={`absolute inset-0 -z-0 pointer-events-none ${className}`}
      style={style}
    >
      <Player
        accountId={accountId}
        autoplay
        controls={false}
        fullSize
        id={videoId}
        loop
        muted
        poster={poster ? posterUrl : undefined}
        provider={provider}
        variant="background"
      />
    </div>
  )
}

BackgroundVideo.propTypes = {
  accountId: PropTypes.string,
  className: PropTypes.string,
  poster: PropTypes.object,
  videoId: PropTypes.string,
  // Only cloudflare is supported at the moment
  provider: PropTypes.oneOf(['cloudflare']),
  style: PropTypes.object,
}
