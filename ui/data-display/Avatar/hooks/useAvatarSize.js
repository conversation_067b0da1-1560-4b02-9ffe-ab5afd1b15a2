import { useMemo } from 'react'

import useBreakpoint from 'ui/helpers/useBreakpoint'
import { breakpointKeys } from 'utils/media'
import { isObject } from 'utils/types'

export function useAvatarSize(size, defaultSize = 'sm') {
  const breakpoint = useBreakpoint()

  return useMemo(() => {
    if (!size) {
      return defaultSize
    }

    if (typeof size === 'string') {
      return size
    }

    if (!isObject(size)) {
      return defaultSize
    }

    if (size[breakpoint]) {
      return size[breakpoint]
    }

    const breakpointIndex = breakpointKeys.indexOf(breakpoint)
    const prevBreakpoints = breakpointKeys.slice(0, breakpointIndex).reverse()

    let value = defaultSize

    for (const breakpointKey of prevBreakpoints) {
      if (size[breakpointKey]) {
        value = size[breakpointKey]
        break
      }
    }
    return value
  }, [breakpoint, defaultSize, size])
}
