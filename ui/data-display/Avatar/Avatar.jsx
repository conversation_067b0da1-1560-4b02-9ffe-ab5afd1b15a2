import PropTypes from 'prop-types'
import { Fragment } from 'react'

import clsx from 'clsx'

import dynamic from 'next/dynamic'

import Button from 'ui/buttons/Button'
import { getBackgroundColor, getTextColor } from 'ui/helpers/getColor'

import Image from '../Image'
import { useAvatarSize } from './hooks/useAvatarSize'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function Avatar({
  size = 'sm',
  title,
  subtitle,
  icon = 'user',
  iconColor = 'primary-800',
  iconBackgroundColor = 'primary-100',
  description,
  className,
  url,
  callToAction,
  image,
}) {
  const avatarSize = useAvatarSize(size)

  const avatarImage = image

  return (
    <div
      className={clsx(
        'flex items-center',
        {
          'gap-2': avatarSize === 'sm',
          'gap-4': avatarSize === 'md',
          'gap-8': avatarSize === 'lg',
        },
        className
      )}
    >
      {avatarImage ? (
        <Image
          file={avatarImage}
          sizes="300px"
          className={clsx(
            'shrink-0 overflow-hidden rounded-full object-cover',
            {
              'h-10 w-10': avatarSize === 'sm',
              'h-16 w-16': avatarSize === 'md',
              'h-40 w-40': avatarSize === 'lg',
            }
          )}
        />
      ) : (
        <Icon
          name={icon}
          size={avatarSize === 'lg' ? '2em' : '1em'}
          className={clsx(
            'flex items-center justify-center rounded-full',
            {
              'h-10 w-10 p-2': avatarSize === 'sm',
              'h-16 w-16 p-6': avatarSize === 'md',
              'h-40 w-40 p-8': avatarSize === 'lg',
            },
            getBackgroundColor(iconBackgroundColor)
          )}
          iconClass={getTextColor(iconColor)}
        />
      )}
      <div
        className={clsx('flex flex-col', {
          'space-y-1': avatarSize === 'lg' && (subtitle || description),
        })}
      >
        {title && (
          <h4
            className={clsx('font-bold', {
              'text-sm': avatarSize === 'sm',
              'text-lg': avatarSize === 'md',
              'text-xl': avatarSize === 'lg',
            })}
          >
            {title}
          </h4>
        )}
        <div>
          {subtitle && (
            <p
              className={clsx('opacity-75', {
                'text-sm': avatarSize !== 'lg',
              })}
            >
              {subtitle}
            </p>
          )}

          <p
            className={clsx('font-light', {
              'text-sm': avatarSize === 'sm',
            })}
          >
            {typeof description === 'string'
              ? description.split(/\r?\n/).map((text, index) => (
                  <Fragment key={index}>
                    {index > 0 && <br />}
                    {text}
                  </Fragment>
                ))
              : description}
          </p>
        </div>
        {url && callToAction && (
          <div>
            <Button
              url={url}
              label={callToAction}
              icon="arrow-right-long"
              variant="secondary"
              size={avatarSize === 'sm' ? 'xs' : 'sm'}
            />
          </div>
        )}
      </div>
    </div>
  )
}

Avatar.propTypes = {
  icon: PropTypes.string,
  iconColor: PropTypes.string,
  iconBackgroundColor: PropTypes.string,
  className: PropTypes.string,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  description: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  url: PropTypes.string,
  callToAction: PropTypes.string,
  image: PropTypes.object,
  dynamicPerson: PropTypes.bool,
  person: PropTypes.object,
  personRole: PropTypes.object,
  personRoles: PropTypes.arrayOf(PropTypes.string),
  size: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.oneOf(['sm', 'md', 'lg']),
  ]),
}
