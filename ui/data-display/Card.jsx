import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { isEmpty } from 'utils/objects'

const Button = dynamic(() => import('ui/buttons/Button'))
const Icon = dynamic(() => import('ui/icons/Icon'))
const ConditionalLink = dynamic(() =>
  import('ui/navigation/Link').then(m => m.ConditionalLink)
)
const Image = dynamic(() => import('ui/data-display/Image'))

const lineClamp = {
  1: 'line-clamp-1',
  2: 'line-clamp-2',
  3: 'line-clamp-3',
  4: 'line-clamp-4',
  5: 'line-clamp-5',
  6: 'line-clamp-6',
}

export default function Card({
  callToAction,
  callToActionVariant,
  className = '',
  description,
  extra,
  icon,
  id,
  image,
  imageAlt = '',
  imageAspectRatio = 'auto',
  kicker,
  maxLinesDescription,
  maxLinesTitle,
  subtitle,
  title,
  url,
}) {
  return (
    <div className={`w-full ${className}`} id={id}>
      <div className="relative flex h-full flex-col justify-between rounded-lg bg-white shadow dark:bg-gray-700">
        {image && !isEmpty(image) && (
          <div className="relative block">
            <ConditionalLink
              className="cursor-pointer"
              to={url}
              condition={url}
            >
              <Image
                alt={imageAlt || title}
                file={image}
                className="rounded-t-lg"
                aspectRatio={imageAspectRatio}
                sizes="md:350px 500px"
              />
              {icon && (
                <div className="absolute inset-0 flex items-center justify-center text-white transition duration-300 text-7xl hover:scale-125">
                  <Icon name={icon} />
                </div>
              )}
            </ConditionalLink>
          </div>
        )}
        <div className="flex flex-grow flex-col justify-between space-y-2 p-3 md:p-4">
          <div>
            {kicker && (
              <p className="uppercase tracking-wide text-color1-500 text-sm">
                {kicker}
              </p>
            )}
            <h4
              className={`font-semibold leading-snug text-base md:text-lg lg:text-xl ${
                maxLinesTitle ? lineClamp[maxLinesTitle] : ''
              }`}
            >
              <ConditionalLink
                className="dark:text-gray-100"
                condition={url}
                to={url}
              >
                {title}
              </ConditionalLink>
            </h4>
          </div>
          {subtitle && (
            <p className="font-semibold text-gray-500 dark:text-gray-200">
              {subtitle}
            </p>
          )}
          {description && (
            <p className="text-normal flex-grow leading-snug text-gray-500 text-sm dark:text-gray-200 md:leading-normal md:text-base">
              {maxLinesDescription ? (
                <span className={lineClamp[maxLinesDescription]}>
                  {description}
                </span>
              ) : (
                description
              )}
            </p>
          )}
          {extra}
          {callToAction && url && (
            <div className="self-end pt-2">
              <Button
                label={callToAction}
                variant={callToActionVariant}
                url={url}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
Card.propTypes = {
  className: PropTypes.string,
  callToAction: PropTypes.string,
  callToActionVariant: PropTypes.string,
  description: PropTypes.node,
  extra: PropTypes.node,
  id: PropTypes.string,
  icon: PropTypes.string,
  image: PropTypes.object,
  imageAlt: PropTypes.string,
  imageAspectRatio: PropTypes.oneOf(['auto', '1/1', '3/4', '4/3', '16/9']),
  kicker: PropTypes.node,
  linkType: PropTypes.oneOf(['url', 'file']),
  maxLinesTitle: PropTypes.oneOf([1, 2, 3, 4, 6]),
  maxLinesDescription: PropTypes.oneOf([1, 2, 3, 4, 5, 6]),
  subtitle: PropTypes.node,
  title: PropTypes.node,
  url: PropTypes.string,
}
