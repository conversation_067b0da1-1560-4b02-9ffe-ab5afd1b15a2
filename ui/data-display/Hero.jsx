import PropTypes from 'prop-types'

import clsx from 'clsx'

import Image from 'ui/data-display/Image'
import { Hyphenate } from 'ui/helpers/Hyphenate'

import { useHeight } from 'ui/helpers/useHeight'
import usePadding from 'ui/helpers/usePadding'
import { useAlignFromPlacement, usePlacement } from 'ui/helpers/usePlacement'
import useSpacing from 'ui/helpers/useSpacing'
import { useTextFieldWithModifiers } from 'ui/helpers/useTextFieldWithModifiers'
import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'
import Button from 'ui/buttons/Button'
import useWidth from 'ui/helpers/useWidth'

const HERO_IMAGE_QUALITY = 80

function getHeroImageFormat(image, heightValue) {
  return function ({ width }) {
    const aspectRatio = image.width / image.height
    const height = width / aspectRatio

    if (height < heightValue) {
      return `h:${heightValue},q:${HERO_IMAGE_QUALITY}`
    }

    return `w:${width},q:${HERO_IMAGE_QUALITY}`
  }
}

export default function Hero({
  callToAction,
  callToActionVariant,
  className,
  height,
  image,
  imageAlign = 'center',
  kicker,
  padding,
  spacing,
  textPosition = 'center',
  textWidth,
  title,
  url,
}) {
  const heightValue = useValueAtBreakpoint(height?.value)
  const heightStyles = useHeight(height)
  const textWidthClasses = useWidth(textWidth)

  const textPositionValue =
    typeof textPosition === 'object' ? textPosition : { xs: textPosition }
  const textPositionValueAtBreakpoint = useValueAtBreakpoint(textPositionValue)
  const textPositionClasses =
    usePlacement(textPositionValue) ?? 'items-center justify-center'

  const [kickerValue, kickerClassNames] = useTextFieldWithModifiers(kicker)
  const [titleValue, titleClassNames] = useTextFieldWithModifiers(title)

  const spacingClasses = useSpacing(spacing)
  const paddingClasses = usePadding(padding)

  const textAlignClass =
    useAlignFromPlacement(textPositionValue) ?? 'text-center'

  return (
    (image || title) && (
      <article
        className={clsx(
          'relative mx-auto flex w-full transition-all 2xl:px-0',
          paddingClasses,
          textPositionClasses,
          className
        )}
        style={heightStyles}
      >
        {image && (
          <Image
            alt={titleValue}
            className="absolute inset-0 max-h-full min-h-full w-full object-cover object-bottom"
            file={image}
            getImageFormat={getHeroImageFormat(image, heightValue)}
            lazy={false}
            objectFit="cover"
            objectPosition={imageAlign}
            priority={true}
            quality={HERO_IMAGE_QUALITY}
            sizes="2xl:1920px xl:1536px lg:1280px md:1024px 768px"
          />
        )}
        {titleValue && (
          <div
            className={clsx(
              'relative flex max-w-screen-xl flex-col transition-all xl:mx-auto',
              {
                'items-end': textPositionValueAtBreakpoint?.includes('right'),
                'items-center':
                  !textPositionValueAtBreakpoint?.includes('right') &&
                  !textPositionValueAtBreakpoint?.includes('left'),
              },
              spacingClasses || 'gap-16 md:gap-24',
              textWidthClasses
            )}
          >
            {kicker && (
              <p className={clsx(kickerClassNames, textAlignClass)}>
                {kickerValue}
              </p>
            )}
            <h1
              className={clsx(
                {
                  'lg:mt-4': textPositionClasses?.includes('items-start'),
                },
                textAlignClass,
                titleClassNames
              )}
            >
              <Hyphenate text={titleValue} />
            </h1>
            {callToAction && url && (
              <div className={textAlignClass}>
                <Button
                  url={url}
                  label={callToAction}
                  icon="arrow-right-long"
                  variant={callToActionVariant}
                />
              </div>
            )}
          </div>
        )}
      </article>
    )
  )
}
Hero.propTypes = {
  callToAction: PropTypes.string,
  callToActionVariant: PropTypes.string,
  className: PropTypes.string,
  height: PropTypes.object,
  image: PropTypes.object,
  imageAlign: PropTypes.string,
  padding: PropTypes.object,
  spacing: PropTypes.object,
  textPosition: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  textWidth: PropTypes.object,
  title: PropTypes.object,
  url: PropTypes.string,
}
