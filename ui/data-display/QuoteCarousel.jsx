import { useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import Autoplay from 'embla-carousel-autoplay'
import Fade from 'embla-carousel-fade'

import dynamic from 'next/dynamic'
import Avatar from './Avatar'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from './EmblaCarousel'

const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Text = dynamic(() => import('ui/typography/Text'))

const quoteStart = {
  none: '',
  singleQuotes: '\u2018', //‘
  doubleQuotes: '\u201C', //“
  germanDoubleQuotes: '\u201E', //„
  guillemets: '\u00AB\u00A0', //«
}

const quoteEnd = {
  none: '',
  singleQuotes: '\u2019', //’
  doubleQuotes: '\u201D', //”
  germanDoubleQuotes: '\u201C', //“
  guillemets: '\u00A0\u00BB', // »
}

export default function QuoteCarousel({
  automatic = true,
  className = '',
  duration = 5000,
  transition = 'slide',
  quotationType = 'none',
  showName = true,
  showAvatar = true,
  navigationArrows = true,
  navigationDots = true,
  fontFamily = '',
  fontWeight = 'normal',
  italic = false,
  textSize = '',
  id,
  items,
}) {
  const [api, setApi] = useState()
  const [current, setCurrent] = useState(0)

  useEffect(() => {
    if (!api) {
      return
    }

    setCurrent(api.selectedScrollSnap())

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap())
    })
  }, [api])

  const plugins = []

  if (automatic) {
    plugins.push(
      Autoplay({
        delay: duration,
        stopOnInteraction: false,
      })
    )
  }

  if (transition === 'fade') {
    plugins.push(Fade())
  }

  return (
    <Carousel
      setApi={setApi}
      opts={{ loop: true }}
      plugins={plugins}
      className={className}
      id={id}
    >
      <CarouselContent>
        {items.map((item, i) => (
          <CarouselItem
            className="flex justify-center"
            key={`carousel-item-${i}`}
          >
            <div className="flex flex-col px-8 gap-y-6 justify-center items-center w-11/12">
              <Text
                text={`${quoteStart[quotationType]}${item.quote}${quoteEnd[quotationType]}`}
                fontFamily={fontFamily}
                fontWeight={fontWeight}
                textSize={textSize}
                align="center"
                className={italic ? 'italic' : ''}
              />
              {item.author && showAvatar && (
                <Avatar
                  title={item.author}
                  subtitle={item.role}
                  image={item.avatar}
                  size="md"
                  iconBackgroundColor="gray-100"
                />
              )}
              {item.author && !showAvatar && showName && (
                <Text
                  text={`— ${item.author}`}
                  fontWeight="semibold"
                  textSize="xl"
                  align="center"
                />
              )}
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
      {items.length > 1 && navigationArrows && (
        <>
          <CarouselPrevious />
          <CarouselNext />
        </>
      )}
      {items.length > 1 && navigationDots && (
        <div className="flex flex-row justify-center pt-6">
          {items.map((_, i) => (
            <Clickable
              key={`carousel-indicator-${i}`}
              onClick={() => api.scrollTo(i)}
            >
              <span
                className={`flex w-2.5 h-2.5 mx-2 right-1 ring-color1-400 rounded-full ${current == i ? 'bg-gray-950' : 'bg-gray-400'}`}
              />
            </Clickable>
          ))}
        </div>
      )}
    </Carousel>
  )
}
QuoteCarousel.propTypes = {
  automatic: PropTypes.bool,
  className: PropTypes.string,
  duration: PropTypes.number,
  transition: PropTypes.oneOf(['slide', 'fade']),
  quotationType: PropTypes.oneOf([
    'none',
    'singleQuotes',
    'doubleQuotes',
    'germanDoubleQuotes',
    'guillemets',
  ]),
  showName: PropTypes.bool,
  showAvatar: PropTypes.bool,
  navigationArrows: PropTypes.bool,
  navigationDots: PropTypes.bool,
  fontFamily: PropTypes.string,
  italic: PropTypes.bool,
  fontWeight: PropTypes.string,
  textSize: PropTypes.object,
  id: PropTypes.string,
  items: PropTypes.arrayOf(
    PropTypes.shape({ alt: PropTypes.string, file: PropTypes.object })
  ),
}
