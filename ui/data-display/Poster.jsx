import React from 'react'

import dynamic from 'next/dynamic'

import useBorderRadius from 'ui/helpers/useBorderRadius'

const Button = dynamic(() => import('ui/buttons/Button'))
const Image = dynamic(() => import('ui/data-display/Image'))
const Player = dynamic(() => import('ui/data-display/Player'))

const transformOrigin = {
  center: 'origin-center',
  left: 'origin-left',
  right: 'origin-right',
}

/**
 * Poster component to render an image that is in potrait format with hover-triggered text and buttons
 * @param {Object} props
 * @param {Boolean} props.enableAnimation Enables animation on hover (or when touched on mobile)
 * @param {'center' | 'left' | 'right'} props.animationOrigin The origin of the animation
 * @param {Boolean} props.animateImage Enables animation of the inner image
 * @param {Object} props.borderRadius The border radius of the component
 * @param {Object} props.primaryButton The primary button object
 * @param {String} props.primaryButton.type The type of the primary button
 * @param {String} props.primaryButton.variant The variant of the primary button
 * @param {String} props.primaryButton.label The label of the primary button
 * @param {String} props.primaryButton.icon The icon of the primary button
 * @param {String} props.primaryButton.iconPosition The icon position of the primary button
 * @param {String} props.primaryButton.url The URL of the primary button
 * @param {String} props.primaryButton.videoId The video ID of the primary button
 * @param {String} props.primaryButton.accountID The account ID for the video of the primary button
 * @param {String} props.primaryButton.provider The provider for the video of the primary button
 * @param {Object} props.secondaryButton The secondary button object
 * @param {String} props.secondaryButton.type The type of the secondary button
 * @param {String} props.secondaryButton.variant The variant of the secondary button
 * @param {String} props.secondaryButton.label The label of the secondary button
 * @param {String} props.secondaryButton.icon The icon of the secondary button
 * @param {String} props.secondaryButton.iconPosition The icon position of the secondary button
 * @param {String} props.secondaryButton.url The URL of the secondary button
 * @param {String} props.secondaryButton.videoId The video ID of the secondary button
 * @param {String} props.secondaryButton.accountID The account ID for the video of the secondary button
 * @param {String} props.secondaryButton.provider The provider for the video of the secondary button
 * @param {String} props.className The class name of the component
 * @param {String} props.title The title that's shown on hover
 * @param {String} props.description The description that's shown on hover
 * @param {Object} props.image The image object
 * @param {String} props.alt The alt text of the image
 *
 * @returns {React.ReactElement} The Poster component
 */
export default function Poster({
  enableAnimation,
  animationOrigin = 'center',
  animateImage = true,
  borderRadius,
  primaryButton,
  secondaryButton,
  className = '',
  title,
  description,
  image,
  alt,
}) {
  const borderRadiusClasses = useBorderRadius(borderRadius)

  return (
    <div
      className={`group/poster relative aspect-3/4 overflow-hidden bg-white dark:bg-primary-dark-700 shadow ${borderRadiusClasses} ${
        enableAnimation
          ? `transition-all duration-300 hover:scale-105 ${transformOrigin[animationOrigin]}`
          : ''
      } ${className}`}
    >
      <div
        className={`block aspect-3/4 ${
          animateImage && enableAnimation
            ? 'group-hover/poster:scale-110 duration-300 transition-all'
            : ''
        }`}
      >
        <div className="-my-2 opacity-0 group-hover/poster:opacity-100 group-hover/poster:-m-0 flex transition-all duration-500 absolute z-10 inset-0 bg-gradient-to-t from-black to-transparent items-end justify-center">
          <div className="text-center text-white p-4 space-y-1 mb-2 md:mb-6">
            {title && (
              <div className="font-semibold text-base md:text-lg leading-tight">
                {title}
              </div>
            )}
            {description && (
              <p className="line-clamp-2 md:line-clamp-3 text-xs md:text-base scale-90">
                {description}
              </p>
            )}
            <div className="flex flex-wrap justify-center gap-2">
              {primaryButton?.type === 'url' && (
                <div className="pt-2">
                  <Button
                    icon={primaryButton?.icon}
                    iconPosition={primaryButton?.iconPosition}
                    label={primaryButton?.label}
                    url={primaryButton?.url}
                    variant={primaryButton?.variant}
                  />
                </div>
              )}
              {primaryButton?.type === 'video' && (
                <div className="pt-2">
                  <Player
                    accountID={primaryButton?.accountID}
                    id={primaryButton?.videoId}
                    provider={primaryButton?.provider}
                    variant="lightboxButton"
                    ctaLabel={primaryButton?.label}
                    ctaVariant={primaryButton?.variant}
                  />
                </div>
              )}
              {secondaryButton?.type === 'url' && (
                <div className="pt-2">
                  <Button
                    icon={secondaryButton?.icon}
                    iconPosition={secondaryButton?.iconPosition}
                    label={secondaryButton?.label}
                    url={secondaryButton?.url}
                    variant={secondaryButton?.variant}
                  />
                </div>
              )}
              {secondaryButton?.type === 'video' && (
                <div className="pt-2">
                  <Player
                    accountID={secondaryButton?.accountID}
                    id={secondaryButton?.videoId}
                    provider={secondaryButton?.provider}
                    variant="lightboxButton"
                    ctaLabel={secondaryButton?.label}
                    ctaVariant={secondaryButton?.variant}
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="absolute top-1/2 w-full -translate-y-1/2">
          <Image
            alt={alt}
            file={image}
            aspectRatio="3/4"
            sizes="768px sm:384px"
          />
        </div>
      </div>
    </div>
  )
}
