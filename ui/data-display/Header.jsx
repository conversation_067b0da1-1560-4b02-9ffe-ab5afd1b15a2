import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Heading = dynamic(() => import('ui/typography/Heading'))
const SeparatorLine = dynamic(() => import('ui/data-display/SeparatorLine'))

export default function Header({
  className = '',
  description,
  title,
  subtitle,
  ...rest
}) {
  return (
    <div className={`space-y-4 ${className}`}>
      <Heading as="h1" title={title} {...rest} />
      <SeparatorLine />
      {subtitle && <p className="font-light text-gray-700">{subtitle}</p>}
      {description && <p className="text-gray-800">{description}</p>}
    </div>
  )
}

Header.propTypes = {
  className: PropTypes.string,
  title: PropTypes.node,
  subtitle: PropTypes.node,
  description: PropTypes.node,
}
