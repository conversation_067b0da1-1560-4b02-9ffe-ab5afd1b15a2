import PropTypes from 'prop-types'
import { useRef } from 'react'

import clsx from 'clsx'

import Button from 'ui/buttons/Button'
import CookieBlockedContent from 'ui/feedback/CookieBlockedContent'
import { Dialog, DialogContent } from 'ui/feedback/FloatingDialog'
import { AutoplayNext } from './components/AutoplayNext'
import { PlayerContainer } from './components/PlayerContainer'
import PlayerPoster from './components/Poster'
import { usePlayer } from './hooks/usePlayer'
import { providers } from './utils/getPlayerUrl'
import usePlayerRoundedClass from './hooks/usePlayerRoundedClass'

/**
 * ui/Player Component
 * @param {*} props
 */
export default function Player({
  accountId,
  autoplay,
  className = '',
  id,
  startsAt,
  endsAt,
  playerId,
  provider = 'youtube',
  title,
  playlist,
  playlistIndex,
  sources,
  poster,
  alt,
  showPosterOverlay: initialShowPosterOverlay = false,
  playIcon,
  playIconSize,
  playIconColor,
  playIconHoverColor,
  playIconText,
  ctaLabel,
  ctaVariant,
  ctaSize,
  variant = 'inline',
  cancelVariant,
  pageData,
  rounded = true,
}) {
  const embedRef = useRef(null)

  const {
    type,
    error,
    url,
    isSelfHosted,
    countDown,
    showNextEpisodeState,
    playNextEpisode,
    cancelPlayNextEpisode,
    showPosterOverlay,
    onPosterPlay,
    isDialogOpen,
    setIsDialogOpen,
    onCloseDialog,
  } = usePlayer({
    accountId,
    autoplay:
      autoplay ||
      variant === 'lightboxPoster' ||
      (variant === 'lightboxButton' && !initialShowPosterOverlay),
    endsAt,
    id,
    initialShowPosterOverlay,
    playerId,
    embedRef,
    playlist,
    playlistIndex,
    poster,
    provider,
    sources,
    startsAt,
  })

  // TODO: When they see that they need cookie blocking, read consent given from outside cookie consent.
  const cookiesDisabled = pageData?.site?.cookieSettings?.disabled

  const roundedClass = usePlayerRoundedClass(rounded)

  const renderPlayer = () => {
    const showPoster = variant !== 'lightboxPoster' && showPosterOverlay
    return (
      <div
        className={clsx(
          'aspect-video max-h-full w-full relative',
          roundedClass,
          {
            'bg-gray-300': variant === 'inline',
          }
        )}
        style={
          variant === 'inline'
            ? undefined
            : {
                maxHeight: 'calc(100vh - 7rem)', // 7.5rem is the height of the header and padding
                maxWidth: 'calc(100vw - 3rem)', // 3rem is the width of the side padding
              }
        }
      >
        {['hls', 'mp4-hd', 'mp4-sd'].includes(provider) || cookiesDisabled ? (
          <PlayerContainer
            error={error}
            isSelfHosted={isSelfHosted}
            url={url}
            type={type}
            provider={provider}
            id={id}
            title={title}
            ref={embedRef}
            rounded={rounded}
          />
        ) : (
          <CookieBlockedContent
            blockCondition={`Player-${provider}`}
            typeCondition="tracking"
            className="flex h-full w-full flex-col items-center justify-center"
            buttonSize="sm"
          >
            <PlayerContainer
              error={error}
              isSelfHosted={isSelfHosted}
              url={url}
              type={type}
              provider={provider}
              id={id}
              title={title}
              ref={embedRef}
              rounded={rounded}
            />
          </CookieBlockedContent>
        )}
        {showPoster && (
          <PlayerPoster
            fillContainer={true}
            poster={poster}
            alt={alt}
            provider={provider}
            playIcon={playIcon}
            playIconSize={playIconSize}
            playIconColor={playIconColor}
            playIconHoverColor={playIconHoverColor}
            playIconText={playIconText}
            onClick={onPosterPlay}
            rounded={rounded}
          />
        )}
        <AutoplayNext
          state={showNextEpisodeState}
          countDown={countDown}
          playNext={playNextEpisode}
          cancel={cancelPlayNextEpisode}
          cancelVariant={cancelVariant}
        />
      </div>
    )
  }

  return (
    <div
      id={id}
      className={clsx(
        'relative',
        { 'w-full h-full': variant !== 'lightboxButton' },
        className
      )}
    >
      {variant === 'lightboxButton' && (
        <Dialog open={isDialogOpen} onOpenChange={onCloseDialog}>
          <div className="flex flex-col items-center justify-center">
            <Button
              // className=''
              label={ctaLabel}
              size={ctaSize}
              variant={ctaVariant}
              onClick={() => setIsDialogOpen(true)}
              secondaryIcon={'play'}
            />
          </div>
          <DialogContent
            overlayClass="bg-gradient-to-b from-black/10 to-black/70"
            boxClass="w-screen h-screen"
            contentClass="flex items-center justify-center flex-auto"
            transparent
          >
            {isDialogOpen && renderPlayer()}
          </DialogContent>
        </Dialog>
      )}

      {variant === 'lightboxPoster' && (
        <Dialog open={isDialogOpen} onOpenChange={onCloseDialog}>
          <PlayerPoster
            className="aspect-video h-full w-full"
            poster={poster}
            provider={provider}
            playIcon={playIcon}
            playIconSize={playIconSize}
            playIconColor={playIconColor}
            playIconHoverColor={playIconHoverColor}
            playIconText={playIconText}
            onClick={() => setIsDialogOpen(true)}
          />
          <DialogContent
            overlayClass="bg-gradient-to-b from-black/10 to-black/70"
            boxClass="w-screen h-screen"
            contentClass="flex items-center justify-center flex-auto"
            closeIconClass="text-white" // TODO: Make this a prop
            transparent
          >
            {isDialogOpen && renderPlayer()}
          </DialogContent>
        </Dialog>
      )}

      {variant === 'inline' && renderPlayer()}
    </div>
  )
}
Player.propTypes = {
  provider: PropTypes.oneOf([
    'youtube',
    'jetstream',
    'jetstream-live',
    'vimeo',
    'soundcloud',
    'hls',
    'mp4-hd',
    'mp4-sd',
  ]),
  autoplay: PropTypes.bool,
  id: PropTypes.string,
  accountId: PropTypes.string,
  playerId: PropTypes.string,
  startsAt: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  endsAt: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  className: PropTypes.string,
  title: PropTypes.string,
  sources: PropTypes.arrayOf(
    PropTypes.shape({
      src: PropTypes.string,
      type: PropTypes.string,
    })
  ),
  poster: PropTypes.string,
  alt: PropTypes.string,
  showPosterOverlay: PropTypes.bool,
  playIcon: PropTypes.object,
  playIconSize: PropTypes.object,
  playIconColor: PropTypes.string,
  playlist: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      url: PropTypes.string,
      provider: PropTypes.oneOf([
        'youtube',
        'jetstream',
        'jetstream-live',
        'vimeo',
        'soundcloud',
        'hls',
        'mp4-hd',
        'mp4-sd',
      ]),
    })
  ),
  playlistIndex: PropTypes.number,
  ctaLabel: PropTypes.string,
  ctaVariant: PropTypes.string,
  ctaSize: PropTypes.string,
  variant: PropTypes.string,
  rounded: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.oneOf(['none', 'sm', 'default', 'md', 'lg', 'xl', '2xl']),
  ]),
}
Player.providers = providers
