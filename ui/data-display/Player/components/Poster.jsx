import PropTypes from 'prop-types'

import clsx from 'clsx'

import Image from 'ui/data-display/Image'
import SvgInline from 'ui/data-display/SvgInline'
import { getHoverTextColor, getTextColor } from 'ui/helpers/getColor'
import SvgIcon from 'ui/icons/SvgIcon'
import { isEmpty } from 'utils/objects'

import usePlayIconSize from '../hooks/usePosterIconSize'
import usePlayerRoundedClass from '../hooks/usePlayerRoundedClass'

export default function PlayerPoster({
  fillContainer,
  className,
  poster,
  alt = 'Poster',
  playIcon,
  playIconSize,
  playIconColor,
  playIconHoverColor,
  playIconText,
  onClick,
  rounded = 'xl',
}) {
  const playIconSizeClasses = usePlayIconSize(
    isEmpty(playIconSize) ? { xs: 'md' } : playIconSize
  )
  const playIconColorClass = playIconColor ? getTextColor(playIconColor) : ''
  const playIconHoverColorClass = playIconHoverColor
    ? getHoverTextColor(playIconHoverColor)
    : ''

  const roundedClass = usePlayerRoundedClass(rounded)

  return (
    <button
      type="button"
      className={clsx(
        'group overflow-hidden border-none bg-black focus:outline-none active:outline-none',
        {
          'relative h-full w-full': !fillContainer,
          'absolute left-0 top-0 h-full w-full': fillContainer,
        },
        roundedClass,
        className
      )}
      onClick={onClick}
    >
      <Image
        src={poster}
        className="h-full w-full transition-opacity group-hover:opacity-50 group-active:opacity-50"
        objectFit="cover"
        alt={alt}
      />
      <div className="absolute left-0 top-0 flex h-full w-full flex-col items-center justify-center gap-4">
        {playIcon ? (
          <SvgInline
            className={clsx(
              'fill-current',
              playIconSizeClasses,
              playIconColorClass,
              playIconHoverColorClass
            )}
            file={playIcon}
          />
        ) : (
          <SvgIcon
            name="play"
            size="auto"
            className={clsx(
              'fill-current',
              playIconSizeClasses,
              playIconColorClass,
              playIconHoverColorClass
            )}
          />
        )}
        <p className="text-2xl font-bold text-white">{playIconText}</p>
      </div>
    </button>
  )
}

PlayerPoster.propTypes = {
  fillContainer: PropTypes.bool,
  className: PropTypes.string,
  poster: PropTypes.string,
  playIcon: PropTypes.object,
  playIconSize: PropTypes.object,
  playIconColor: PropTypes.string,
  onClick: PropTypes.func,
  rounded: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.oneOf(['none', 'sm', 'default', 'md', 'lg', 'xl', '2xl']),
  ]),
}
