import React from 'react'
import PropTypes from 'prop-types'
import { useTranslation } from 'next-i18next'

import usePlayerRoundedClass from '../hooks/usePlayerRoundedClass'

export const PlayerContainer = React.forwardRef(
  (
    { error, isSelfHosted, url, type, provider, id, title, rounded = true },
    embedRef
  ) => {
    const { t } = useTranslation('media-library')

    const roundedClass = usePlayerRoundedClass(rounded)

    if (error) {
      return (
        <div className="flex h-full w-full items-center justify-center border">
          Error: {t(error)}
        </div>
      )
    }

    if (isSelfHosted) {
      return <div className="h-full w-full" data-vjs-player="" ref={embedRef} />
    }

    if (!url) {
      return null
    }

    return (
      <iframe
        ref={embedRef}
        allowFullScreen
        className={`h-full w-full ${roundedClass}`}
        frameBorder={0}
        height={type === 'audio' ? 'auto' : undefined}
        id={`player-${provider}-${id}`}
        key={`${provider}-${id}`}
        src={url}
        title={title}
        allow="autoplay"
      />
    )
  }
)

PlayerContainer.propTypes = {
  error: PropTypes.string,
  isSelfHosted: PropTypes.bool,
  url: PropTypes.string,
  type: PropTypes.string,
  provider: PropTypes.string,
  id: PropTypes.string,
  rounded: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.oneOf(['none', 'sm', 'default', 'md', 'lg', 'xl', '2xl']),
  ]),
  title: PropTypes.string,
}
