import { selfHosted } from '../providers/selfHosted'
import { youtube } from '../providers/youtube'

export const getVideoPlayer = ({ provider, embedRef, onEnd, options = {} }) => {
  if (!provider || !embedRef) {
    return null
  }

  if (['hls', 'mp4-hd', 'mp4-sd'].includes(provider)) {
    return selfHosted({
      options,
      onEnd,
      embedRef,
    })
  }

  if (provider === 'youtube') {
    const { autoplay } = options
    return youtube({
      autoplay,
      onEnd,
      embedRef,
    })
  }

  return null
}
