import { hmsToSeconds, hmsToVimeoT } from 'utils/datetime'

const selfHostedVideoProviderDefaults = {
  label: 'Hosted',
  type: 'video',
  baseUrl: '',
  checkRequirements: ({ id }) => {
    return !id ? 'missingVideoUrl' : null
  },
}

/**
 * Video providers only
 */
const videoProviders = [
  {
    name: 'jetstream',
    label: 'Jetstream',
    type: 'video',
    baseUrl: 'https://jstre.am/embed/jsv:',
    checkRequirements: ({ id }) => {
      return !id ? 'missingVideoId' : null
    },
  },
  {
    name: 'jetstream-live',
    label: 'Jetstream',
    type: 'video',
    baseUrl: 'https://jstre.am/embed/jsl:',
    checkRequirements: ({ id }) => {
      return !id ? 'missingVideoId' : null
    },
  },
  {
    name: 'youtube',
    label: 'YouTube',
    type: 'video',
    baseUrl: 'https://www.youtube-nocookie.com/embed/',
    checkRequirements: ({ id }) => {
      return !id ? 'missingVideoId' : null
    },
    getParams: (id, start, end, autoplay) => {
      const params = ['enablejsapi=1']
      if (start) params.push(`start=${hmsToSeconds(start)}`)
      if (end) params.push(`end=${hmsToSeconds(end)}`)
      if (autoplay === true || autoplay === id) params.push(`autoplay=1`)
      return `${id}?${params.join('&')}`
    },
  },
  {
    name: 'vimeo',
    label: 'Vimeo',
    type: 'video',
    baseUrl: 'https://player.vimeo.com/video/',
    checkRequirements: ({ id }) => {
      return !id ? 'missingVideoId' : null
    },
    getParams: (id, start, end, autoplay) => {
      let params = [
        'byline=0', // Hide the user's byline
        'pip=0', // Hide the picture-in-picture button
        'portrait=0', // Hide the user's portrait
        'title=0', // Hide the title
        'dnt=true', // Do not track
      ]
      // Add support for autoplay if requested
      if (autoplay === true || autoplay === id) params.push(`autoplay=1`)

      let hash = ''
      if (start) hash += `#t=${hmsToVimeoT(start)}` // Set start time in hash fragment
      return `${id}?${params.join('&')}${hash}`
    },
  },
  {
    ...selfHostedVideoProviderDefaults,
    name: 'hls',
  },
  {
    ...selfHostedVideoProviderDefaults,
    name: 'mp4-hd',
  },
  {
    ...selfHostedVideoProviderDefaults,
    name: 'mp4-sd',
  },
]

/**
 * Audio providers only
 */
const audioProviders = [
  {
    name: 'soundcloud',
    label: 'SoundCloud',
    type: 'audio',
    baseUrl:
      'https://w.soundcloud.com/player/?url=https%3A//api.soundcloud.com/tracks/',
    checkRequirements: ({ id }) => {
      return !id ? 'missingAudioId' : null
    },
    getParams: (id, start, end) => {
      const params = [
        // 'color=ff5500',
        'auto_play=false',
        'hide_related=true',
        'show_comments=false',
        'show_user=true',
        'show_reposts=false',
      ]
      if (start) params.push(`start=${hmsToSeconds(start)}`)
      if (end) params.push(`end=${hmsToSeconds(end)}`)
      return `${id}?${params.join('&')}`
    },
  },
]

/**
 * All providers (video + audio)
 */
export const providers = [...videoProviders, ...audioProviders]

/**
 * Retuns a provider configuration (if found)
 * @param {string} provider Provider name
 * @returns `object` Provider config
 */
export function getProviderConf(provider) {
  return providers.find(p => p.name === provider)
}

/**
 *
 * @param {string} provider provider name
 * @param {string} videoId video identifier
 * @param {string} startsAt when video should start, formatted like: hh:mm:ss
 * @param {string} endsAt when video should ends, formatted like: hh:mm:ss
 */
export function getPlayerUrl({
  provider,
  id,
  accountId,
  playerId,
  startsAt,
  endsAt,
  autoplay,
}) {
  const providerConf = getProviderConf(provider)

  if (!providerConf) {
    return { url: null, error: 'unknownProvider' }
  }

  const { baseUrl, getPlayer, getParams, checkRequirements, type } =
    providerConf

  if (typeof checkRequirements === 'function') {
    const error = checkRequirements({ id, accountId, playerId })
    if (error) return { url: null, error }
  }

  const playerUrl =
    typeof getPlayer === 'function' ? getPlayer(accountId, playerId) : ''

  const mediaParams =
    typeof getParams === 'function'
      ? getParams(id, startsAt, endsAt, autoplay)
      : id

  const url = `${baseUrl}${playerUrl}${mediaParams}`

  return { url, type }
}
