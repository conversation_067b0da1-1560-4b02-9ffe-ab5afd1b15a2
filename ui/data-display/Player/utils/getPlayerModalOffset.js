const fontSizeOffsets = {
  'xs': 0.75,
  'sm': 0.875,
  'base': 1,
  'lg': 1.125,
  'xl': 1.25,
  '2xl': 1.5,
  '3xl': 1.875,
  '4xl': 2.25,
  '5xl': 3,
  '6xl': 3.75,
  '7xl': 4.5,
  '8xl': 6,
  '9xl': 8,
}

const paddingOffsets = padding => {
  if (padding === 'px') return 1
  return parseInt(padding, 10) * 4
}

export function getPlayerModalOffset(
  variants,
  size = 1,
  variant = 'base',
  gap = 4
) {
  const offsetVariant = variants[variant] ?? variants['base']

  // We need to add the vertical padding of the close button together with the icon size to get the total offset of the button / title
  // variants[variant].padding.top|bottom (e.g. 2)
  // variants[variant].text.size (e.g. 'lg')

  const paddingOffset =
    (paddingOffsets(
      offsetVariant.padding?.top ?? variants['base']?.padding?.top ?? 0
    ) +
      paddingOffsets(
        offsetVariant.padding?.bottom ?? variants['base']?.padding?.bottom ?? 0
      )) /
    16
  const fontSizeOffset =
    fontSizeOffsets[
      offsetVariant.text?.[size] ?? variants['base']?.text?.[size] ?? 'base'
    ]

  const gapOffset = (gap * 4) / 16

  return paddingOffset + fontSizeOffset + gapOffset
}
