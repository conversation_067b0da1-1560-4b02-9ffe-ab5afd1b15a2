const SCRIPT_EL_ID = 'youtube-iframe-api'

export function youtube({
  onPlay,
  onPause,
  onEnd,
  onBuffer,
  onCue,
  onReady,
  autoplay,
  embedRef,
}) {
  let player

  function onPlayerStateChange(event) {
    switch (event.data) {
      // not started
      case -1:
        break
      // ended
      case 0:
        onEnd?.()
        break
      // playing
      case 1:
        onPlay?.()
        break
      // paused
      case 2:
        onPause?.()
        break
      // buffering
      case 3:
        onBuffer?.()
        break
      // cued
      case 5:
        onCue?.()
        break

      default:
        break
    }
  }

  function onPlayerReady() {
    if (autoplay) {
      player.playVideo()
    }
    onReady?.()
  }

  function setPlayer() {
    player = new window.YT.Player(embedRef, {
      playerVars: {
        autoplay: autoplay ? 1 : 0,
      },
      events: {
        onReady: onPlayerReady,
        onStateChange: onPlayerStateChange,
      },
    })
  }

  window.onYouTubeIframeAPIReady = function () {
    if (window.YT && embedRef) {
      setPlayer()
    }
  }

  function initialise() {
    if (!embedRef) {
      return
    }

    if (!document.getElementById(SCRIPT_EL_ID)) {
      const scriptEl = document.createElement('script')
      scriptEl.id = SCRIPT_EL_ID
      scriptEl.src = 'https://www.youtube.com/iframe_api'

      const firstScriptTag = document.getElementsByTagName('script')[0]
      firstScriptTag.parentNode.insertBefore(scriptEl, firstScriptTag)
    }

    if (window.YT) {
      setPlayer()
    }
  }

  function cleanup() {
    // delete window.onYouTubeIframeAPIReady
    // const firstScriptTag = document.getElementsByTagName('script')[0]
    // const scriptEl = document.getElementById(SCRIPT_EL_ID)
    // firstScriptTag.parentNode.removeChild(scriptEl)
    // player?.destroy()
  }

  function play() {
    player?.playVideo()
  }

  return {
    initialise,
    cleanup,
    play,
  }
}
