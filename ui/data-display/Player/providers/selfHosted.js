import videojs from 'video.js'

const defaultVideoJsOptions = {
  controls: true,
  responsive: true,
  fluid: true,
  aspectRatio: '16:9',
}

export function selfHosted({ options, embedRef, onEnd }) {
  let player

  function onPlayerReady() {
    // player.on('dispose', () => {
    //   console.log('player will dispose')
    // })

    player.on('ended', () => {
      onEnd?.()
    })
  }

  function initialise() {
    if (!embedRef) {
      return
    }

    if (!player) {
      if (embedRef.childElementCount === 0) {
        const videoElement = document.createElement('video-js')

        videoElement.classList.add('vjs-big-play-centered')
        embedRef.appendChild(videoElement)

        player = videojs(
          videoElement,
          { ...defaultVideoJsOptions, ...options },
          () => {
            onPlayerReady()
          }
        )
      }
    } else {
      player?.src(options.sources)
    }
  }

  function cleanup() {
    if (player && !player.isDisposed()) {
      player.dispose()
    }
  }

  function play() {
    if (player) {
      player.play()
    }
  }

  return {
    initialise,
    cleanup,
    play,
  }
}
