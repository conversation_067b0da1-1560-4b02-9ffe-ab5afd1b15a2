import React from 'react'

const roundedClasses = {
  'none': 'rounded-none',
  'sm': 'rounded-sm',
  'default': 'rounded',
  'md': 'rounded-md',
  'lg': 'rounded-lg',
  'xl': 'rounded-xl',
  '2xl': 'rounded-2xl',
}

/**
 * Returns the rounded class based on the value passed
 * @param {string | boolean} rounded - The value to determine the rounded class ()
 * @returns {string}
 */
export default function usePlayerRoundedClass(rounded) {
  return React.useMemo(() => {
    // No rounded class
    if (!rounded) return ''
    // Default rounded class when only true is passed
    if (rounded === true) return roundedClasses.default

    // Return the rounded class based on the value, and if it doesn't match any, return the default rounded class
    return roundedClasses[rounded] || roundedClasses.default
  }, [rounded])
}
