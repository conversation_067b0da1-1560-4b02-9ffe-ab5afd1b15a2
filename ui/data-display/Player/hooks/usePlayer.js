import { useRouter } from 'next/router'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { getPlayerUrl } from '../utils/getPlayerUrl'
import { useCountdown } from './useCountdown'
import { getVideoPlayer } from '../utils/getVideoPlayer'

const AUTOPLAY_DELAY = 5

export function usePlayer({
  accountId,
  autoplay: initialAutoplay,
  endsAt,
  id,
  initialShowPosterOverlay,
  playerId,
  embedRef,
  playlist,
  playlistIndex,
  poster,
  provider,
  sources,
  startsAt,
}) {
  const playerRef = useRef(null)
  const router = useRouter()

  const [autoplay, setAutoplay] = useState(
    initialAutoplay || Boolean(router.query?.autoplay)
  )
  const [showPosterOverlay, setShowPosterOverlay] = useState(
    initialShowPosterOverlay
  )
  const [showNextEpisodeState, setShowNextEpisodeState] = useState('INITIAL')
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const { url, type, error } = getPlayerUrl({
    provider,
    id,
    accountId,
    playerId,
    startsAt,
    endsAt,
    autoplay: autoplay || Boolean(router.query?.autoplay),
  })

  const isLast = useMemo(
    () => (playlist ? playlistIndex === playlist.length - 1 : true),
    [playlist, playlistIndex]
  )

  const playNextEpisode = useCallback(async () => {
    if (!isLast) {
      const nextEpisode = playlist[playlistIndex + 1]
      if (nextEpisode) {
        const query = { ...router.query, autoplay: 1 }
        delete query.slug
        await router.push({
          pathname: nextEpisode.url,
          query,
        })
      }
    }
  }, [isLast, playlist, playlistIndex, router])

  const { countDown, startCountdown, resetCountdown } = useCountdown(
    AUTOPLAY_DELAY,
    playNextEpisode
  )

  const cancelPlayNextEpisode = useCallback(() => {
    resetCountdown()
    setShowNextEpisodeState('CANCELLED')
  }, [resetCountdown])

  const onEnd = useCallback(() => {
    if (!isLast && (playlist ?? []).length > 0) {
      setShowNextEpisodeState('COUNTDOWN')
      startCountdown()
    }
  }, [isLast, playlist, startCountdown])

  const onCloseDialog = useCallback(() => {
    setShowPosterOverlay(initialShowPosterOverlay)
    setIsDialogOpen(false)
    setAutoplay(initialAutoplay)
  }, [initialAutoplay, initialShowPosterOverlay])

  const options = useMemo(
    () => ({
      sources,
      poster,
      autoplay: autoplay || Boolean(router.query?.autoplay),
    }),
    [autoplay, poster, router.query?.autoplay, sources]
  )

  const isSelfHosted = useMemo(
    () => ['hls', 'mp4-hd', 'mp4-sd'].includes(provider),
    [provider]
  )

  const createPlayer = useCallback(async () => {
    if (!playerRef.current) {
      playerRef.current = getVideoPlayer({
        provider,
        embedRef: embedRef.current,
        onEnd,
        options,
      })
      playerRef.current?.initialise()
    }
  }, [embedRef, onEnd, options, provider])

  const destroyPlayer = useCallback(async () => {
    await playerRef.current?.cleanup()
    playerRef.current = null
  }, [playerRef])

  useEffect(() => {
    if (embedRef.current) {
      createPlayer()
    }
    return () => {
      destroyPlayer()
    }
  }, [createPlayer, destroyPlayer, onEnd, embedRef])

  const onPosterPlay = useCallback(() => {
    setShowPosterOverlay(false)
    setAutoplay(true)
    playerRef.current?.play()
  }, [playerRef])

  return {
    type,
    error,
    url,
    player: playerRef.current,
    isSelfHosted,
    countDown,
    showNextEpisodeState,
    playNextEpisode,
    cancelPlayNextEpisode,
    showPosterOverlay,
    onPosterPlay,
    isDialogOpen,
    setIsDialogOpen,
    onCloseDialog,
  }
}
