import { useState, useEffect, useCallback, useRef } from 'react'

export const useCountdown = (seconds, onCountdown) => {
  const intervalId = useRef()
  const [countDown, setCountdown] = useState(seconds)

  const startCountdown = useCallback(() => {
    intervalId.current = setInterval(() => {
      setCountdown(currentCountdown => currentCountdown - 1)
    }, 1000)
  }, [])

  const stopCountdown = useCallback(() => {
    if (intervalId.current) {
      clearInterval(intervalId.current)
    }
    setCountdown(0)
  }, [])

  const resetCountdown = useCallback(() => {
    if (intervalId.current) {
      clearInterval(intervalId.current)
    }
    setCountdown(seconds)
  }, [seconds])

  useEffect(() => {
    return () => resetCountdown()
  }, [resetCountdown])

  useEffect(() => {
    if (countDown === 0) {
      stopCountdown()
      onCountdown?.()
    }
  }, [countDown, onCountdown, stopCountdown])

  return { countDown, startCountdown, resetCountdown }
}
