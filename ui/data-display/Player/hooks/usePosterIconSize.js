import { useResponsiveValue } from 'ui/helpers/useResponsiveValue'

const playIconSizes = {
  xs: {
    sm: 'h-12 w-12',
    md: 'h-16 w-16',
    lg: 'h-24 w-24',
  },
  sm: {
    sm: 'sm:h-12 sm:w-12',
    md: 'sm:h-16 sm:w-16',
    lg: 'sm:h-24 sm:w-24',
  },
  md: {
    sm: 'md:h-12 md:w-12',
    md: 'md:h-16 md:w-16',
    lg: 'md:h-24 md:w-24',
  },
  lg: {
    sm: 'lg:h-12 lg:w-12',
    md: 'lg:h-16 lg:w-16',
    lg: 'lg:h-24 lg:w-24',
  },
  xl: {
    sm: 'xl:h-12 xl:w-12',
    md: 'xl:h-16 xl:w-16',
    lg: 'xl:h-24 xl:w-24',
  },
}

export default function usePlayIconSize(size = { xs: 'md' }) {
  return useResponsiveValue(playIconSizes, size)
}
