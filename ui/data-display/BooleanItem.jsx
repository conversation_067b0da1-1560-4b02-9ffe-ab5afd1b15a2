import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function BooleanItem({ label, value }) {
  return (
    <div className="flex items-center space-x-1 leading-tight rtl:space-x-reverse">
      <Icon
        className={`p-1 ${value ? 'text-success-600' : 'text-danger-600'}`}
        name={value ? 'check' : 'close'}
      />
      <div>{label}</div>
    </div>
  )
}
BooleanItem.propTypes = {
  value: PropTypes.bool,
  label: PropTypes.node,
}
