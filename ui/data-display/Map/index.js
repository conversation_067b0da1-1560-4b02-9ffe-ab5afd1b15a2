import { Map } from './Map'

const provider = process.env.NEXT_PUBLIC_MAP_PROVIDER ?? 'mapbox'

let fetchGeocoder = async function () {
  throw new Error('No geocoder provider set')
}

if (provider === 'mapbox') {
  fetchGeocoder = async function () {
    return await import('./providers/mapbox/helpers/fetchGeocoder')
  }
}

if (provider === 'maptiler') {
  fetchGeocoder = async function () {
    return await import('./providers/maptiler/helpers/fetchGeocoder')
  }
}

export { fetchGeocoder }

export default Map
