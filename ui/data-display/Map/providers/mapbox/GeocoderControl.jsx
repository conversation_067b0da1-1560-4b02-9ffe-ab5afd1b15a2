import PropTypes from 'prop-types'

import MapboxGeocoder from '@mapbox/mapbox-gl-geocoder'
import '@mapbox/mapbox-gl-geocoder/dist/mapbox-gl-geocoder.css'
import mapboxgl from 'mapbox-gl'
import { useControl } from 'react-map-gl/mapbox'

import noop from 'utils/noop'

export default function GeocoderControl({
  onLoading = noop,
  onResults = noop,
  onResult = noop,
  onError = noop,
  position = 'top-left',
  ...props
}) {
  const geocoder = useControl(
    () => {
      const ctrl = new MapboxGeocoder({
        ...props,
        mapboxgl,
        accessToken: props.mapboxAccessToken,
        marker: false,
      })
      ctrl.on('loading', onLoading)
      ctrl.on('results', onResults)
      ctrl.on('result', event => {
        const { result } = event
        onResult(result, event)
      })
      ctrl.on('error', onError)
      return ctrl
    },
    {
      position,
    }
  )

  if (geocoder._map) {
    if (
      geocoder.getProximity() !== props.proximity &&
      props.proximity !== undefined
    ) {
      geocoder.setProximity(props.proximity)
    }
    if (
      geocoder.getRenderFunction() !== props.render &&
      props.render !== undefined
    ) {
      geocoder.setRenderFunction(props.render)
    }
    if (
      geocoder.getLanguage() !== props.language &&
      props.language !== undefined
    ) {
      geocoder.setLanguage(props.language)
    }
    if (geocoder.getZoom() !== props.zoom && props.zoom !== undefined) {
      geocoder.setZoom(props.zoom)
    }
    if (geocoder.getFlyTo() !== props.flyTo && props.flyTo !== undefined) {
      geocoder.setFlyTo(props.zoom)
    }
    if (
      geocoder.getPlaceholder() !== props.placeholder &&
      props.placeholder !== undefined
    ) {
      geocoder.setPlaceholder(props.zoom)
    }
    if (
      geocoder.getCountries() !== props.countries &&
      props.countries !== undefined
    ) {
      geocoder.setCountries(props.zoom)
    }
    if (geocoder.getTypes() !== props.types && props.types !== undefined) {
      geocoder.setTypes(props.zoom)
    }
    if (
      geocoder.getMinLength() !== props.minLength &&
      props.minLength !== undefined
    ) {
      geocoder.setMinLength(props.zoom)
    }
    if (geocoder.getLimit() !== props.limit && props.limit !== undefined) {
      geocoder.setLimit(props.zoom)
    }
    if (geocoder.getFilter() !== props.filter && props.filter !== undefined) {
      geocoder.setFilter(props.zoom)
    }
    if (geocoder.getOrigin() !== props.origin && props.origin !== undefined) {
      geocoder.setOrigin(props.zoom)
    }
    if (
      geocoder.getAutocomplete() !== props.autocomplete &&
      props.autocomplete !== undefined
    ) {
      geocoder.setAutocomplete(props.zoom)
    }
    if (
      geocoder.getFuzzyMatch() !== props.fuzzyMatch &&
      props.fuzzyMatch !== undefined
    ) {
      geocoder.setFuzzyMatch(props.zoom)
    }
    if (
      geocoder.getRouting() !== props.routing &&
      props.routing !== undefined
    ) {
      geocoder.setRouting(props.zoom)
    }
    if (
      geocoder.getWorldview() !== props.worldview &&
      props.worldview !== undefined
    ) {
      geocoder.setWorldview(props.zoom)
    }
  }
  return null
}
GeocoderControl.propTypes = {
  autocomplete: PropTypes.bool,
  countries: PropTypes.array,
  filter: PropTypes.any,
  flyTo: PropTypes.any,
  language: PropTypes.string,
  limit: PropTypes.number,
  mapboxAccessToken: PropTypes.string,
  minLength: PropTypes.number,
  onLoading: PropTypes.func,
  onResult: PropTypes.func,
  onResults: PropTypes.func,
  onError: PropTypes.func,
  origin: PropTypes.any,
  placeholder: PropTypes.node,
  position: PropTypes.oneOf([
    'top-left',
    'top-right',
    'bottom-left',
    'bottom-right',
  ]),
  proximity: PropTypes.any,
  fuzzyMatch: PropTypes.any,
  render: PropTypes.func,
  routing: PropTypes.any,
  types: PropTypes.array,
  worldview: PropTypes.any,
  zoom: PropTypes.number,
}
