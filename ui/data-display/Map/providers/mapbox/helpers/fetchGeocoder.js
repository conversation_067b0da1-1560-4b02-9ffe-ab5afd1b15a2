import { getFetch } from 'utils/http'

const MAP_TOKEN = process.env.NEXT_PUBLIC_MAP_TOKEN

export default function fetchMapboxGeocoder({
  term,
  typesParam,
  language,
  country,
}) {
  const searchParam = encodeURIComponent(term)
  const geocodeUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${searchParam}.json?access_token=${MAP_TOKEN}&language=${
    language || 'en'
  }`

  return getFetch(geocodeUrl, { types: typesParam, country })
}
