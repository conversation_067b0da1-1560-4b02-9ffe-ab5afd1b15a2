import PropTypes from 'prop-types'

import 'mapbox-gl/dist/mapbox-gl.css'

import MapMarker from '../../shared/MapMarker'

export default function MaptilerMarker(props) {
  return <MapMarker {...props} />
}
MaptilerMarker.propTypes = {
  coordinates: PropTypes.arrayOf(PropTypes.number),
  current: PropTypes.bool,
  onClick: PropTypes.func,
  popover: PropTypes.node,
  type: PropTypes.oneOf(['church', 'point']),
}
