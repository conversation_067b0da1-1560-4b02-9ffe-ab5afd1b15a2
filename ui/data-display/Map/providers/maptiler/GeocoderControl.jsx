import PropTypes from 'prop-types'

import { GeocodingControl } from '@maptiler/geocoding-control/react'
import '@maptiler/geocoding-control/style.css'

export default function MaptileGeocoderControl({
  countries,
  language,
  limit,
  mapToken,
  onResult,
  position = 'top-left',
}) {
  return (
    <div className="p-4">
      <GeocodingControl
        apiKey={mapToken}
        country={countries}
        language={language}
        position={position}
        limit={limit}
        onPick={onResult}
      />
    </div>
  )
}

MaptileGeocoderControl.propTypes = {
  countries: PropTypes.arrayOf(PropTypes.string),
  language: PropTypes.string,
  limit: PropTypes.number,
  mapToken: PropTypes.string.isRequired,
  onResult: PropTypes.func,
  position: PropTypes.oneOf([
    'top-left',
    'top-right',
    'bottom-left',
    'bottom-right',
  ]),
}
