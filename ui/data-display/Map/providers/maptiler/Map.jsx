import PropTypes from 'prop-types'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import isEqual from 'lodash/isEqual'
import maplibregl from 'maplibre-gl'
import 'maplibre-gl/dist/maplibre-gl.css'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import ReactMapGL, {
  GeolocateControl,
  NavigationControl,
} from 'react-map-gl/mapbox'

import fitMapToBounds from '../../helpers/fitMapToBounds'

const GeocoderControl = dynamic(() => import('./GeocoderControl'))
const MapMarker = dynamic(() => import('./MapMarker'))

const MAP_TOKEN = process.env.NEXT_PUBLIC_MAP_TOKEN

export default function MapTilerMap({
  boundingBox,
  className,
  getMap,
  height = '100%',
  id,
  mapStyle,
  markers = [],
  onChange,
  onGeolocate,
  onMove,
  onResult,
  renderItemPopover,
  showControls,
  showGeolocate,
  showSearch,
  token,
  viewport,
  width = '100%',
}) {
  const mapRef = useRef()
  const boundingBoxRef = useRef()
  const geocoderContainerRef = useRef()

  const { i18n } = useTranslation()

  const [current, setCurrent] = useState(null)

  const { longitude = 0, latitude = 0, zoom = 1 } = viewport || {}

  const mapToken = token ?? MAP_TOKEN

  const fitToBoundsAndResize = useCallback(() => {
    const mapTarget = mapRef?.current
    // Stop effect exceution when:
    if (
      !mapTarget || // - map is not yet available
      !Array.isArray(boundingBox) || // - boundingBox is not an array
      boundingBox.length !== 4 || // - boundingBox is not valid
      isEqual(boundingBoxRef.current, boundingBox) // - map already has been fit to the boundingBox
    ) {
      return undefined
    }

    // Fit map to boundingBox
    fitMapToBounds(mapTarget, boundingBox)

    // Set a ref to current boundingBox to be able to check for changes later
    boundingBoxRef.current = boundingBox

    // Forces a resize to center
    mapTarget.resize()
  }, [boundingBox])

  useEffect(() => {
    fitToBoundsAndResize()
  }, [fitToBoundsAndResize])

  const onLoad = useCallback(() => {
    const mapElem = mapRef?.current

    if (!mapElem) return

    const map = mapElem.getMap()

    // Translate map labels
    map.getStyle().layers.forEach(function (layer) {
      if (layer.type == 'symbol' && layer['source-layer'] === 'place') {
        map.setLayoutProperty(layer.id, 'text-field', [
          'get',
          `name:${i18n.language}`,
        ])
      }
    })

    // Ensures map has the right size
    mapElem.resize()
  }, [i18n.language])

  const onRender = useCallback(() => {
    const mapElem = mapRef?.current

    getMap?.(mapElem)

    fitToBoundsAndResize()
  }, [fitToBoundsAndResize, getMap])

  const flyToMarker = useCallback((coordinates, zoom = 18) => {
    if (!Array.isArray(coordinates)) return

    mapRef.current?.flyTo({
      center: coordinates,
      zoom,
      duration: 1000,
    })

    setCurrent(coordinates)
  }, [])

  // Provide map if getMap function is requested
  useEffect(() => {
    if (mapRef?.current && typeof getMap === 'function') {
      getMap(mapRef?.current)
    }
  }, [getMap])

  const containerStyle = useMemo(() => {
    return {
      width: typeof width === 'number' ? `${width}px` : width,
      height: typeof height === 'number' ? `${height}px` : 'auto',
    }
  }, [width, height])

  return (
    <div
      id={id}
      className={`relative isolate overflow-hidden ${className}`}
      style={containerStyle}
    >
      <div ref={geocoderContainerRef} className="absolute left-4 top-4 z-10" />
      <ReactMapGL
        mapLib={maplibregl}
        initialViewState={{
          longitude,
          latitude,
          zoom,
        }}
        ref={mapRef}
        reuseMaps
        attributionControl="compact"
        mapboxAccessToken={mapToken}
        onResize={fitToBoundsAndResize}
        onLoad={onLoad}
        longitude={longitude}
        latitude={latitude}
        zoom={zoom}
        style={{ position: 'absolute', top: 0, left: 0, bottom: 0, right: 0 }}
        onClick={onChange}
        onRender={onRender}
        mapStyle={
          mapStyle ??
          `https://api.maptiler.com/maps/winter-v2/style.json?key=${mapToken}`
        }
        onMove={({ viewState }) => onMove(viewState)}
      >
        {showControls && <NavigationControl className="right-4 top-4" />}
        {showSearch && (
          <GeocoderControl
            mapToken={mapToken}
            onResult={result => {
              if (result) {
                flyToMarker(result.center, 15)
              }
              onResult?.(result)
            }}
            limit={8}
            language={i18n?.language || 'en'}
          />
        )}
        {showGeolocate && (
          <GeolocateControl position="bottom-right" onGeolocate={onGeolocate} />
        )}
        {markers.map((marker, i) => {
          const { location, type } = marker
          const isCurrent = isEqual(location.coordinates, current)
          return (
            <MapMarker
              coordinates={location.coordinates}
              onClick={flyToMarker}
              current={isCurrent}
              onDragEnd={onChange}
              draggable={typeof onChange === 'function'}
              key={`marker-${i}`}
              popover={
                typeof renderItemPopover === 'function'
                  ? renderItemPopover(marker)
                  : null
              }
              type={type}
            />
          )
        })}
      </ReactMapGL>
    </div>
  )
}
MapTilerMap.propTypes = {
  boundingBox: PropTypes.array,
  className: PropTypes.string,
  getMap: PropTypes.func,
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  id: PropTypes.string,
  mapStyle: PropTypes.string,
  markers: PropTypes.arrayOf(PropTypes.shape({ coordinates: PropTypes.array })),
  onChange: PropTypes.func,
  onGeolocate: PropTypes.func,
  onMove: PropTypes.func,
  onResult: PropTypes.func,
  renderItemPopover: PropTypes.func,
  showControls: PropTypes.bool,
  showGeolocate: PropTypes.bool,
  showSearch: PropTypes.bool,
  token: PropTypes.string,
  viewport: PropTypes.object,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}
