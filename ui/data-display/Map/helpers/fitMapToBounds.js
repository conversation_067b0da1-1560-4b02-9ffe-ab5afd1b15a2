const defaultOptions = {
  duration: 100,
  padding: {
    top: 200,
    bottom: 200,
    left: 200,
    right: 200,
  },
}

/**
 *  Fit a map to a boundingBox
 * @param {object} map The map instance
 * @param {array} boundingBox The bounding box to fit the map to
 * @param {object} options Options for the fitMapToBounds method
 * @param {number} options.duration Duration of the animation
 * @param {object|number} options.padding Minimum padding of the map markers at the edges
 * @returns
 */
export default function fitMapToBounds(map, boundingBox, options = {}) {
  if (!map || !boundingBox) return

  let { duration, padding } = {
    ...defaultOptions,
    ...(options || {}),
  }

  if (typeof padding === 'number') {
    padding = {
      top: padding,
      bottom: padding,
      left: padding,
      right: padding,
    }
  }

  // Fit map to boundingBox
  const [west, south, east, north] = boundingBox
  map.fitBounds(
    [
      [west, south], // south-west corner
      [east, north], // north-east corner
    ],
    {
      padding,
      duration,
    }
  )
}
