import PropTypes from 'prop-types'
import { useCallback, useEffect, useMemo, useState } from 'react'

import { AudioProvider } from 'components/AudioProvider'

import { AudioPlaylist } from './AudioPlaylist'

export function AudioPlaylistContainer({
  id,
  className,
  image,
  title,
  playlistTitle,
  playlist = [],
  showTitle,
  showKicker,
  showAbstract,
  showDescription,
  showPlaylistAbstract,
  scrollPlaylist,
  scrollAreaSize = 'sm',
  variant = 'lg',
  onPlay,
  defaultTrack,
  playlistExtra,
}) {
  const [currentTrack, setCurrentTrack] = useState(defaultTrack ?? playlist[0])

  const currentIndex = useMemo(() => {
    const index = playlist.findIndex(track => track.id === currentTrack.id)
    return index < 0 ? 0 : index
  }, [currentTrack.id, playlist])

  const playTrack = useCallback(track => {
    setCurrentTrack(track)
  }, [])

  const playPrevNextTrack = useCallback(
    direction => () => {
      const index = direction === 'prev' ? currentIndex - 1 : currentIndex + 1
      if (index >= 0 && index < playlist.length) {
        playTrack(playlist[index])
      }
    },
    [currentIndex, playlist, playTrack]
  )

  useEffect(() => {
    setCurrentTrack(defaultTrack ?? playlist[0])
  }, [defaultTrack, playlist])

  return (
    <AudioProvider sources={currentTrack.sources}>
      <AudioPlaylist
        id={id}
        className={className}
        currentTrack={currentTrack}
        currentIndex={currentIndex}
        image={image}
        title={title}
        playTrack={playTrack}
        playlistTitle={playlistTitle}
        playlist={playlist}
        prev={playPrevNextTrack('prev')}
        next={playPrevNextTrack('next')}
        showTitle={showTitle}
        showKicker={showKicker}
        showAbstract={showAbstract}
        showDescription={showDescription}
        showPlaylistAbstract={showPlaylistAbstract}
        scrollPlaylist={scrollPlaylist}
        scrollAreaSize={scrollAreaSize}
        variant={variant}
        onPlay={onPlay}
        playlistExtra={playlistExtra}
      />
    </AudioProvider>
  )
}

AudioPlaylistContainer.propTypes = {
  id: PropTypes.string,
  className: PropTypes.string,
  image: PropTypes.object,
  title: PropTypes.string,
  playlistTitle: PropTypes.string,
  playlist: PropTypes.arrayOf(
    PropTypes.shape({
      sources: PropTypes.arrayOf(
        PropTypes.shape({
          src: PropTypes.string,
          type: PropTypes.string,
          duration: PropTypes.number,
        })
      ),
      title: PropTypes.string,
      kicker: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
      abstract: PropTypes.string,
      description: PropTypes.object,
      duration: PropTypes.number,
    })
  ),
  defaultTrack: PropTypes.object,
  showTitle: PropTypes.bool,
  showKicker: PropTypes.bool,
  showAbstract: PropTypes.bool,
  showDescription: PropTypes.bool,
  showPlaylistAbstract: PropTypes.bool,
  scrollPlaylist: PropTypes.bool,
  scrollAreaSize: PropTypes.oneOf(['sm', 'md', 'lg']),
  variant: PropTypes.oneOf(['sm', 'md', 'lg']),
  onPlay: PropTypes.func,
  playlistExtra: PropTypes.node,
}
