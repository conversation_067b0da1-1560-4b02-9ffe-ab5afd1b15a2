import PropTypes from 'prop-types'

import clsx from 'clsx'
import formatDuration from 'date-fns/formatDuration'
import formatISODuration from 'date-fns/formatISODuration'
import { useTranslation } from 'next-i18next'

import { useFormatLocaleTime } from 'hooks/useFormatLocaleTime'
import Icon from 'ui/icons/Icon'
import { formatTime } from 'utils/datetime'

import { ProgressCircle } from './ProgressCircle'
import { useAudioSource } from '../hooks/useAudioSource'
import { ConditionalLink } from 'ui/navigation/Link'

export function PlaylistItem({
  isActive,
  disabled,
  track,
  state,
  play,
  pause,
  playTrack,
  showPlaylistAbstract,
  variant,
}) {
  const { t } = useTranslation('media-library')

  const formatLocaleTime = useFormatLocaleTime({ format: ['hours', 'minutes'] })

  const { title, kicker, abstract, sources, link, linkLabel } = track
  const { duration, error } = useAudioSource(sources?.[0])

  const hasError = Boolean(error)
  const isDisabled = hasError || disabled

  return (
    <li
      className={clsx('grid grid-cols-2 gap-6 border-t p-6 first:border-none', {
        'sticky bottom-0 top-0 z-10': isActive,
        'bg-gray-50 hover:bg-color1-50 dark:bg-white/5':
          !isActive && !isDisabled,
        'bg-gray-50 text-gray-400 dark:bg-white/5': !isActive && isDisabled,
        'bg-color1-500 text-white dark:bg-white/20': isActive && !isDisabled,
        'bg-color1-500 text-white/80 dark:bg-white/20': isActive && isDisabled,
        'md:grid-cols-[4fr_1fr_1fr]': variant !== 'sm',
      })}
    >
      <div
        className={clsx('col-span-2', {
          'md:col-span-1': variant !== 'sm',
        })}
      >
        {Boolean(kicker) && (
          <p
            className={clsx(
              'font-semibold uppercase text-sm dark:text-white/80',
              {
                'text-gray-500': !isActive && !isDisabled,
                'text-gray-400': !isActive && isDisabled,
                'text-color1-50': isActive && !isDisabled,
                'text-white/80': isActive && isDisabled,
              }
            )}
          >
            {kicker}
          </p>
        )}
        <h4
          className={clsx('font-bold text-lg', {
            'underline-offset-4 hover:underline': Boolean(link) && !isDisabled,
          })}
        >
          <ConditionalLink
            condition={Boolean(link) && !isDisabled}
            to={link}
            title={linkLabel}
            className="block"
          >
            {title}
          </ConditionalLink>
        </h4>

        {showPlaylistAbstract && Boolean(abstract) && (
          <p
            className={clsx('text-sm', {
              'text-color1-50 dark:text-white/80': isActive,
            })}
          >
            {abstract}
          </p>
        )}
      </div>
      <time
        dateTime={
          hasError ? undefined : formatTime(duration, formatISODuration)
        }
        className={clsx(
          'shrink-0 self-center justify-self-start whitespace-nowrap md:px-4',
          {
            'md:justify-self-end': variant !== 'sm',
          }
        )}
      >
        {hasError
          ? t('audioNotAvailable')
          : formatLocaleTime(duration, formatDuration) ?? '–'}
      </time>
      <div className="self-center justify-self-end px-4">
        <button
          onClick={() => {
            if (isActive) {
              state === 'PLAYING' ? pause() : play()
            }
            playTrack(track)
          }}
          className={clsx(
            'relative flex h-14 w-14 shrink-0 items-center justify-center outline-none',
            {
              'text-gray-400': isDisabled,
            }
          )}
          title={
            state === 'PLAYING' && isActive
              ? `${t('pause')} ${title}`
              : `${t('play')} ${title}`
          }
          disabled={isDisabled}
        >
          {isActive && <ProgressCircle duration={duration} />}

          <Icon
            name={
              state === 'PLAYING' && isActive ? 'pause-solid' : 'play-solid'
            }
            className={clsx('h-6 w-6', {
              'translate-x-[2px]': !isActive || state !== 'PLAYING',
            })}
            size="1.5rem"
          />
        </button>
      </div>
    </li>
  )
}

PlaylistItem.propTypes = {
  isActive: PropTypes.bool,
  disabled: PropTypes.bool,
  track: PropTypes.shape({
    id: PropTypes.string,
    title: PropTypes.string,
    kicker: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
    abstract: PropTypes.string,
    link: PropTypes.string,
    linkLabel: PropTypes.string,
    description: PropTypes.object,
    duration: PropTypes.number,
    sources: PropTypes.arrayOf(
      PropTypes.shape({
        src: PropTypes.string,
        type: PropTypes.string,
        duration: PropTypes.number,
      })
    ),
  }),
  state: PropTypes.oneOf([
    'INITIAL',
    'LOADSTARTED',
    'LOADEDMETADATA',
    'PLAYING',
    'PAUSED',
    'WAITING',
    'CANPLAY',
    'CANPLAYTHROUGH',
    'ENDED',
    'EMPTIED',
    'ERROR',
  ]).isRequired,
  play: PropTypes.func,
  pause: PropTypes.func,
  playTrack: PropTypes.func,
  showPlaylistAbstract: PropTypes.bool,
  variant: PropTypes.oneOf(['sm', 'md', 'lg']),
}
