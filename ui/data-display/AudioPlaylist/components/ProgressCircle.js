import React, { useEffect, useRef } from 'react'
import PropTypes from 'prop-types'

import { playedPercent } from 'ui/data-display/AudioPlayer'
import { useCurrentTime } from 'components/AudioProvider'

const RADIUS = 24
const CIRCUMFERENCE = RADIUS * 2 * Math.PI

export function ProgressCircle({ duration }) {
  const circleRef = useRef(null)
  const currentTime = useCurrentTime()

  useEffect(() => {
    if (circleRef.current) {
      circleRef.current.style.strokeDashoffset = `${CIRCUMFERENCE}`
    }
  }, [])

  useEffect(() => {
    if (circleRef.current) {
      const percentPlayed = playedPercent(currentTime, duration)
      const offset = CIRCUMFERENCE - (percentPlayed / 100) * CIRCUMFERENCE
      circleRef.current.style.strokeDashoffset = offset
    }
  }, [currentTime, duration])
  return (
    <>
      <svg viewBox="0 0 56 56" width="56" height="56" className="absolute z-0">
        <circle
          className="origin-center -rotate-90 fill-transparent stroke-white/20"
          strokeWidth="4"
          r={RADIUS}
          cx="28"
          cy="28"
        />
      </svg>

      <svg viewBox="0 0 56 56" width="56" height="56" className="absolute z-10">
        <circle
          ref={circleRef}
          className="origin-center -rotate-90 fill-transparent stroke-white transition-all"
          strokeWidth="4"
          style={{
            strokeDasharray: `${CIRCUMFERENCE} ${CIRCUMFERENCE}`,
          }}
          r={RADIUS}
          cx="28"
          cy="28"
        />
      </svg>
    </>
  )
}

ProgressCircle.propTypes = {
  duration: PropTypes.number,
  currentTime: PropTypes.number,
}
