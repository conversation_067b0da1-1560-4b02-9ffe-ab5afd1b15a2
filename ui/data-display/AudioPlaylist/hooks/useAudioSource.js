import { useCallback, useEffect, useState } from 'react'

export function useAudioSource({ duration, src } = {}) {
  const [dur, setDur] = useState()
  const [error, setError] = useState()

  const setAudioDuration = useCallback(event => {
    setDur(event.target.duration)
  }, [])

  const setAudioError = useCallback(() => {
    setError('Error loading audio source')
  }, [])

  useEffect(() => {
    if (duration) {
      setDur(duration)
    } else {
      const audio = new Audio()
      audio.src = src
      audio.addEventListener('loadedmetadata', setAudioDuration)
      audio.addEventListener('error', setAudioError)

      return () => {
        audio.removeEventListener('loadedmetadata', setAudioDuration)
        audio.removeEventListener('error', setAudioError)
      }
    }
  }, [duration, setAudioDuration, setAudioError, src])

  return {
    duration: dur,
    error,
  }
}
