import PropTypes from 'prop-types'
import { useEffect } from 'react'

import { useTranslation } from 'next-i18next'
import clsx from 'clsx'

import { useAudio } from 'components/AudioProvider'
import { usePrevious } from 'hooks/usePrevious'

import { AudioPlayer } from '../AudioPlayer'
import { PlaylistItem } from './components/PlaylistItem'

export function AudioPlaylist({
  id,
  className,
  image,
  title,
  playlistTitle,
  playlist = [],
  playTrack,
  currentTrack,
  currentIndex,
  prev,
  next,
  variant,
  showTitle,
  showKicker,
  showAbstract,
  showDescription,
  showPlaylistAbstract,
  scrollPlaylist,
  scrollAreaSize,
  onPlay,
  playlistExtra,
}) {
  const { t } = useTranslation('media-library')

  const prevCurrentTrack = usePrevious(currentTrack)

  const { state, canPlay, play, pause, load } = useAudio()

  useEffect(() => {
    if (currentTrack !== prevCurrentTrack) {
      load()
      play()
    }
  }, [currentTrack, load, play, playlist, prevCurrentTrack])

  const { title: trackTitle, kicker, abstract, description } = currentTrack

  return (
    <article
      id={id}
      className={clsx(
        'space-y-8 rounded-lg bg-white p-4 text-gray-700 shadow dark:bg-color1-700 dark:text-white',
        className
      )}
    >
      <AudioPlayer
        {...currentTrack}
        title={showTitle ? trackTitle : undefined}
        kicker={showKicker ? kicker : undefined}
        abstract={showAbstract ? abstract : undefined}
        description={showDescription ? description : undefined}
        image={image}
        subtitle={title}
        variant={variant}
        prev={prev}
        next={next}
        prevDisabled={currentIndex < 1}
        nextDisabled={currentIndex === playlist.length - 1}
        onPlay={onPlay}
      />

      {playlist.length > 0 && (
        <section className="space-y-6 border-t border-gray-200 pt-6">
          <header className="flex items-center justify-between px-6">
            <h3 className="font-bold text-xl">
              {playlistTitle ?? t('playlist')}
            </h3>
            {playlistExtra}
          </header>
          <ol
            className={clsx('rounded-md', {
              'overflow-hidden': !scrollPlaylist,
              'relative overflow-y-auto overflow-x-hidden': scrollPlaylist,
              'max-h-96': scrollPlaylist && scrollAreaSize === 'sm',
              'max-h-[32rem]': scrollPlaylist && scrollAreaSize === 'md',
              'max-h-[48rem]': scrollPlaylist && scrollAreaSize === 'lg',
            })}
          >
            {playlist.map(track => {
              const isActive = currentTrack.id === track.id
              const disabled =
                isActive && (['INITIAL', 'ERROR'].includes(state) || !canPlay)

              return (
                <PlaylistItem
                  key={track.id}
                  track={track}
                  isActive={isActive}
                  disabled={disabled}
                  state={state}
                  play={play}
                  pause={pause}
                  playTrack={playTrack}
                  variant={variant}
                  showPlaylistAbstract={showPlaylistAbstract}
                />
              )
            })}
          </ol>
        </section>
      )}
    </article>
  )
}

AudioPlaylist.propTypes = {
  id: PropTypes.string,
  className: PropTypes.string,
  image: PropTypes.object,
  title: PropTypes.string,
  playlistTitle: PropTypes.string,
  playTrack: PropTypes.func,
  playlist: PropTypes.arrayOf(
    PropTypes.shape({
      sources: PropTypes.arrayOf(
        PropTypes.shape({
          src: PropTypes.string,
          type: PropTypes.string,
          duration: PropTypes.number,
        })
      ),
      title: PropTypes.string,
      kicker: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
      abstract: PropTypes.string,
      description: PropTypes.object,
      duration: PropTypes.number,
      url: PropTypes.string,
    })
  ),
  prev: PropTypes.func,
  next: PropTypes.func,
  currentIndex: PropTypes.number,
  currentTrack: PropTypes.shape({
    id: PropTypes.string,
    sources: PropTypes.arrayOf(
      PropTypes.shape({
        src: PropTypes.string,
        type: PropTypes.string,
        duration: PropTypes.number,
      })
    ),
    title: PropTypes.string,
    kicker: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
    abstract: PropTypes.string,
    description: PropTypes.object,
    duration: PropTypes.number,
    url: PropTypes.string,
  }),
  showTitle: PropTypes.bool,
  showKicker: PropTypes.bool,
  showAbstract: PropTypes.bool,
  showDescription: PropTypes.bool,
  showPlaylistAbstract: PropTypes.bool,
  scrollPlaylist: PropTypes.bool,
  scrollAreaSize: PropTypes.oneOf(['sm', 'md', 'lg']),
  variant: PropTypes.oneOf(['sm', 'md', 'lg']),
  onPlay: PropTypes.func,
  playlistExtra: PropTypes.node,
}
