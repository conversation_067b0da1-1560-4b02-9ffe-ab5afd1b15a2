import React from 'react'
import PropTypes from 'prop-types'

import { useCarousel } from '../CarouselContext'

export function CarouselWrapper({ children }) {
  const { currentIndex } = useCarousel()

  return (
    <div
      className="flex transition-transform duration-500 ease-in-out"
      style={{ transform: `translateX(-${currentIndex * 100}%)` }}
    >
      {React.Children.map(children, (child, i) => (
        <div className="w-full flex-shrink-0" key={`carousel-item-${i}`}>
          {child}
        </div>
      ))}
    </div>
  )
}

CarouselWrapper.propTypes = {
  children: PropTypes.node,
}

export default CarouselWrapper
