import React, { useCallback, useEffect } from 'react'

import dynamic from 'next/dynamic'
import { useCarousel } from '../CarouselContext'

const NavButton = dynamic(() => import('./NavButton'))

export default function Navigation() {
  const {
    automatic,
    duration,
    isPaused,
    totalItems,
    currentIndex,
    setCurrentIndex,
  } = useCarousel()

  useEffect(() => {
    if (!automatic || isPaused) return

    const interval = setInterval(() => {
      setCurrentIndex(prevIndex =>
        prevIndex < totalItems - 1 ? prevIndex + 1 : 0
      )
    }, duration)

    return () => clearInterval(interval)
  }, [automatic, currentIndex, duration, isPaused, setCurrentIndex, totalItems])

  const onPrev = useCallback(() => {
    setCurrentIndex(currentIndex > 0 ? currentIndex - 1 : totalItems - 1)
  }, [currentIndex, setCurrentIndex, totalItems])

  const onNext = useCallback(() => {
    setCurrentIndex(currentIndex < totalItems - 1 ? currentIndex + 1 : 0)
  }, [currentIndex, setCurrentIndex, totalItems])

  return (
    <>
      <NavButton onClick={onPrev} icon="chevron-left" className="left-0" />
      <NavButton onClick={onNext} icon="chevron-right" className="right-0" />
    </>
  )
}
