import React from 'react'

import { useCarousel } from '../CarouselContext'
import Clickable from 'ui/helpers/Clickable'

export default function PaginationDots() {
  const { currentIndex, setCurrentIndex, totalItems } = useCarousel()
  const dotsArray = Array.from({ length: totalItems }, (_, index) => index)
  return (
    <div className="absolute bottom-3 dark:bottom-0 md:bottom-6 lg:bottom-8 dark:lg:bottom-4 inset-x-0 z-30 flex justify-center items-center gap-1">
      {dotsArray.map((item, key) => (
        <Clickable
          key={key}
          onClick={
            currentIndex !== item ? () => setCurrentIndex(item) : undefined
          }
        >
          <div
            className={`h-1 md:h-2 rounded-full transition-all duration-700 ease-in-out ${currentIndex === key ? 'w-4 md:w-6 bg-white/80' : 'w-1 md:w-2 bg-white/20'}`}
          />
        </Clickable>
      ))}
    </div>
  )
}
