import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Icon = dynamic(() => import('ui/icons/Icon'))

export function NavButton({ className = '', icon, onClick }) {
  return (
    <Clickable
      onClick={onClick}
      className={`absolute scale-75 md:scale-100 bottom-0 top-0 z-30 flex items-center justify-center p-4 lg:p-6 duration-500 transition-all ${className}`}
    >
      <div className="flex h-16 w-16 items-center justify-center rounded-full bg-white bg-opacity-0 p-4 text-white text-opacity-80 transition-all duration-300 ease-in-out text-2xl hover:bg-opacity-25 hover:text-opacity-90 lg:text-4xl xl:p-8 opacity-0 md:group-hover/scroller:opacity-100">
        <Icon name={icon} className="drop-shadow-md" />
      </div>
    </Clickable>
  )
}
NavButton.propTypes = {
  className: PropTypes.string,
  icon: PropTypes.string,
  onClick: PropTypes.func,
}

export default NavButton
