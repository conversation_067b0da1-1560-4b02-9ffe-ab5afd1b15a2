import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const CarouselWrapper = dynamic(() => import('./components/CarouselWrapper'))
const CarouselNavigation = dynamic(
  () => import('./components/CarouselNavigation')
)
const PaginationDots = dynamic(() => import('./components/PaginationDots'))

import CarouselProvider from './CarouselContext'

export default function Carousel({
  automatic = true,
  className = '',
  duration = 5000,
  children,
}) {
  const totalChildren = React.Children.count(children)

  return (
    <CarouselProvider
      automatic={automatic}
      duration={duration}
      totalItems={totalChildren}
    >
      <div
        className={`relative group/scroller w-full h-full overflow-hidden ${className}`}
      >
        <CarouselWrapper>{children}</CarouselWrapper>
        {totalChildren > 1 && (
          <>
            <PaginationDots />
            <CarouselNavigation />
          </>
        )}
      </div>
    </CarouselProvider>
  )
}
Carousel.propTypes = {
  automatic: PropTypes.bool,
  children: PropTypes.node,
  className: PropTypes.string,
  duration: PropTypes.number,
}
