import React, { useEffect } from 'react'
import PropTypes from 'prop-types'

import Image from '../Image'
import { Track } from './components/Track'
import { Controls } from './components/Controls'
import { useMatchMedia } from 'hooks/useMatchMedia'
import { media } from 'utils/media'
import RichText from 'ui/typography/RichText'
import { VolumeRateControls } from './components/VolumeRateControls'
import Alert from 'ui/feedback/Alert'
import clsx from 'clsx'
import { Header } from './components/Header'
import { StepControl } from './components/StepControl'
import { useAudio } from 'components/AudioProvider'
import { usePrevious } from 'hooks/usePrevious'
import Link from 'ui/navigation/Link'

export function AudioPlayer({
  id,
  className = '',
  title,
  kicker,
  subtitle,
  image,
  abstract,
  description,
  variant = 'lg',
  prev,
  next,
  prevDisabled,
  nextDisabled,
  liveStream,
  onPlay,
  link,
  linkLabel,
}) {
  const isSm = useMatchMedia(media.sm)
  const isMd = useMatchMedia(media.md) && ['md', 'lg'].includes(variant)

  const {
    state,
    duration,
    rate,
    volume,
    canPlay,
    play,
    pause,
    seek,
    seekToOffset,
    setVolume,
    setRate,
  } = useAudio()

  const prevState = usePrevious(state)

  useEffect(() => {
    if (prevState !== 'PLAYING' && state === 'PLAYING') {
      onPlay?.(id)
    }
  }, [id, onPlay, prevState, state])

  const showStepControls = prev && next

  const disabled = ['INITIAL', 'ERROR'].includes(state) || !canPlay

  return (
    <div
      id={id}
      className={clsx(
        'flex w-full flex-col space-y-4',
        {
          'md:flex-row md:space-x-8 md:space-y-0 rtl:md:space-x-reverse': [
            'md',
            'lg',
          ].includes(variant),
          'items-center': !image,
        },
        className
      )}
    >
      {(image || isMd) && (
        <div
          className={clsx({
            'w-full space-y-6 sm:flex sm:flex-row sm:space-x-6 sm:space-y-0 rtl:sm:space-x-reverse':
              variant === 'sm',
            'w-full space-y-6 sm:flex sm:flex-row sm:space-x-6 sm:space-y-0 rtl:sm:space-x-reverse md:w-2/5 md:flex-col md:space-x-0 md:space-y-6':
              variant === 'md',
            'w-full space-y-6 sm:flex sm:flex-row sm:space-x-6 sm:space-y-0 rtl:sm:space-x-reverse md:w-2/5 md:flex-col md:space-x-0 md:space-y-6 lg:w-1/3':
              variant === 'lg',
          })}
        >
          {image && (
            <div
              className={clsx('relative overflow-hidden rounded-md', {
                'sm:basis-1/2': variant === 'sm',
                'sm:basis-1/2 md:basis-auto': variant !== 'sm',
              })}
            >
              <Image file={image} aspectRatio="1/1" sizes="sm:375px 650px" />
            </div>
          )}
          {image && isSm && !isMd && (
            <Header
              title={title}
              kicker={kicker}
              subtitle={subtitle}
              className="self-center"
            />
          )}
          {isMd && (
            <Controls
              play={play}
              pause={pause}
              seekToOffset={seekToOffset}
              state={state}
              liveStream={liveStream}
              className={clsx({
                'sm:basis-1/2': variant === 'md',
                'sm:basis-1/2 md:basis-auto': variant === 'lg',
              })}
            />
          )}
        </div>
      )}

      <div
        className={clsx('w-full space-y-4 self-start', {
          'md:w-3/5 lg:w-2/3': variant === 'lg',
        })}
      >
        {state === 'ERROR' && (
          <Alert
            title="Something went wrong"
            message="We weren't able to load the audio file. Please try again soon."
            className="w-full"
            type="danger"
          />
        )}

        {(!isSm || (isSm && !image) || isMd) && (
          <Header title={title} kicker={kicker} subtitle={subtitle} />
        )}

        <div
          className={`flex w-full flex-wrap rounded-md bg-gray-100 p-4 dark:bg-white/20 ${
            showStepControls ? 'justify-between' : 'justify-end'
          }`}
        >
          {showStepControls && (
            <div className="flex w-1/2 space-x-2 rtl:space-x-reverse">
              <StepControl
                direction="prev"
                onClick={prev}
                disabled={disabled || prevDisabled}
              />
              <StepControl
                direction="next"
                onClick={next}
                disabled={disabled || nextDisabled}
              />
            </div>
          )}

          <VolumeRateControls
            rate={rate}
            setRate={setRate}
            volume={volume}
            setVolume={setVolume}
            disabled={disabled}
            className="w-1/2 justify-end"
          />

          <Track
            duration={duration}
            onChange={seek}
            className="mt-2 w-full"
            disabled={disabled}
            liveStream={liveStream}
            state={state}
          />
        </div>

        {!isMd && (
          <Controls
            play={play}
            pause={pause}
            seekToOffset={seekToOffset}
            liveStream={liveStream}
            state={state}
            className="w-full"
          />
        )}

        {Boolean(abstract) && <p className="w-full pt-8 md:pt-0">{abstract}</p>}
        {Boolean(link) && Boolean(linkLabel) && (
          <p className="w-full pt-8 md:pt-0">
            <Link to={link} basic>
              {linkLabel}
            </Link>
          </p>
        )}
        {Boolean(description) && (
          <div
            className={clsx('w-full pt-8', {
              'border-t md:pt-4': Boolean(abstract) && Boolean(description),
              'md:pt-0': !(Boolean(abstract) && Boolean(description)),
            })}
          >
            <RichText doc={description} textSize={{ xs: 'sm' }} />
          </div>
        )}
      </div>
    </div>
  )
}

AudioPlayer.propTypes = {
  id: PropTypes.string,
  className: PropTypes.string,
  image: PropTypes.object,
  title: PropTypes.string,
  kicker: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  subtitle: PropTypes.string,
  abstract: PropTypes.string,
  description: PropTypes.object,
  prev: PropTypes.func,
  next: PropTypes.func,
  prevDisabled: PropTypes.bool,
  nextDisabled: PropTypes.bool,
  liveStream: PropTypes.bool,
  variant: PropTypes.oneOf(['sm', 'md', 'lg']),
  link: PropTypes.string,
  linkLabel: PropTypes.string,
  onPlay: PropTypes.func,
}
