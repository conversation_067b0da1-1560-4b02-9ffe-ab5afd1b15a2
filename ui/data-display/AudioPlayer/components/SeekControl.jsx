import React from 'react'
import PropTypes from 'prop-types'
import Icon from 'ui/icons/Icon'

export function SeekControl({ flip, offset, onClick, disabled }) {
  return (
    <button
      disabled={disabled}
      onClick={onClick}
      size="sm"
      className={`relative flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-transparent outline-none hover:opacity-80 ${
        disabled
          ? 'cursor-default text-gray-300'
          : 'group text-gray-700 dark:text-white'
      }`}
    >
      <Icon
        name="arrow-rotate-right"
        className={`absolute top-1/2 left-1/2 h-7 w-7 -translate-x-1/2 -translate-y-1/2 ${
          disabled ? 'text-gray-300' : 'text-gray-700 dark:text-white'
        } ${flip ? '-scale-x-100' : ''}`}
        size="1.75rem"
      />
      <span className="translate-y-[1px] text-xs">{offset}</span>
    </button>
  )
}

SeekControl.propTypes = {
  flip: PropTypes.bool,
  disabled: PropTypes.bool,
  offset: PropTypes.number,
  onClick: PropTypes.func,
}
