import React from 'react'
import PropTypes from 'prop-types'
import { ControlButton } from './ControlButton'
import Icon from 'ui/icons/Icon'

export function StepControl({ onClick, disabled, direction }) {
  return (
    <ControlButton
      onClick={onClick}
      disabled={disabled}
      size="sm"
      title={direction === 'prev' ? 'Previous' : 'Next'}
    >
      <Icon
        name={direction === 'prev' ? 'backward-step' : 'forward-step'}
        className={`h-4 w-4`}
        size="1rem"
      />
    </ControlButton>
  )
}

StepControl.propTypes = {
  direction: PropTypes.oneOf(['prev', 'next']),
  disabled: PropTypes.bool,
  onClick: PropTypes.func,
}
