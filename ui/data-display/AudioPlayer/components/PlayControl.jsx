import React from 'react'
import PropTypes from 'prop-types'
import Icon from 'ui/icons/Icon'
import { ControlButton } from './ControlButton'
import { useTranslation } from 'next-i18next'

export function PlayControl({ play, pause, state }) {
  const { t } = useTranslation('media-library')

  return (
    <ControlButton
      onClick={state === 'PLAYING' ? pause : play}
      disabled={['INITIAL', 'ERROR'].includes(state)}
      size="lg"
      title={state === 'PLAYING' ? t('pause') : t('play')}
    >
      <Icon
        name={state === 'PLAYING' ? 'pause-solid' : 'play-solid'}
        className={`h-8 w-8 ${state !== 'PLAYING' ? 'translate-x-1' : ''}`}
        size="2rem"
      />
    </ControlButton>
  )
}

PlayControl.propTypes = {
  play: PropTypes.func.isRequired,
  pause: PropTypes.func.isRequired,
  state: PropTypes.oneOf([
    'INITIAL',
    'LOADSTARTED',
    'LOADEDMETADATA',
    'PLAYING',
    'PAUSED',
    'WAITING',
    'CANPLAY',
    'CANPLAYTHROUGH',
    'ENDED',
    'EMPTIED',
    'ERROR',
  ]).isRequired,
}
