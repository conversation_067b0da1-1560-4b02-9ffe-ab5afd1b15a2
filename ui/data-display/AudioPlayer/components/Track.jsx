import React, { useCallback, useMemo } from 'react'
import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import formatISODuration from 'date-fns/formatISODuration'

import { playedPercent } from '../utils'
import { formatTime } from 'utils/datetime'
import { useCurrentTime } from 'components/AudioProvider'
import clsx from 'clsx'

export function Track({
  className = '',
  duration,
  onChange,
  disabled,
  liveStream,
  state,
}) {
  const { t } = useTranslation('media-library')
  const currentTime = useCurrentTime()

  const handleChange = useCallback(
    event => {
      onChange?.(Number(event.target.value) / 100)
    },
    [onChange]
  )

  const durationDisplayValue = useMemo(
    () => (duration === Infinity ? t('live') : formatTime(duration)),
    [duration, t]
  )
  const durationValue = useMemo(
    () => (Infinity ? t('live') : formatTime(duration, formatISODuration)),
    [duration, t]
  )

  const isPlaying = state === 'PLAYING'

  const percentPlayed =
    liveStream && isPlaying ? 100 : playedPercent(currentTime, duration)

  return (
    <div className={className}>
      <div
        className={clsx('player-track relative h-4 w-full', {
          'player-track--live': liveStream && isPlaying,
        })}
      >
        <progress
          max={100}
          value={percentPlayed}
          className="absolute top-1/2 h-2 w-full -translate-y-1/2 appearance-none rounded-full border-none bg-white/20 dark:bg-white/20"
        />
        <input
          type="range"
          min={0}
          max={100}
          value={percentPlayed}
          className="absolute top-1/2 w-full -translate-y-1/2 appearance-none bg-transparent outline-none"
          onChange={handleChange}
          disabled={liveStream || disabled}
        />
        {liveStream && isPlaying && (
          <span className="absolute right-0 h-4 w-4 animate-ping rounded-full bg-color1-700 dark:bg-color1-400" />
        )}
      </div>
      <div
        className={`flex w-full justify-between ${
          disabled ? 'text-gray-400' : ''
        }`}
      >
        <time
          dateTime={formatTime(currentTime, formatISODuration)}
          title={t('played')}
        >
          {formatTime(currentTime)}
        </time>

        <time dateTime={durationValue} title={t('duration')}>
          {durationDisplayValue}
        </time>
      </div>
    </div>
  )
}

Track.propTypes = {
  className: PropTypes.string,
  currentTime: PropTypes.number,
  duration: PropTypes.number,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  liveStream: PropTypes.bool,
  state: PropTypes.oneOf([
    'INITIAL',
    'LOADSTARTED',
    'LOADEDMETADATA',
    'PLAYING',
    'PAUSED',
    'WAITING',
    'CANPLAY',
    'CANPLAYTHROUGH',
    'ENDED',
    'EMPTIED',
    'ERROR',
  ]),
}
