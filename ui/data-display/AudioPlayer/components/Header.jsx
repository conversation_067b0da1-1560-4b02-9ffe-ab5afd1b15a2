import React from 'react'
import PropTypes from 'prop-types'

export function Header({ className, title, kicker, subtitle }) {
  const hasContent = [title, kicker, subtitle].some(Boolean)

  if (!hasContent) {
    return null
  }

  return (
    <div className={className}>
      {<PERSON><PERSON><PERSON>(kicker) && (
        <p className="font-semibold uppercase text-gray-500 dark:text-white/80">
          {kicker}
        </p>
      )}
      {<PERSON><PERSON><PERSON>(title) && <h3 className="font-bold text-xl">{title}</h3>}
      {<PERSON><PERSON><PERSON>(subtitle) && <p className="text-lg">{subtitle}</p>}
    </div>
  )
}

Header.propTypes = {
  className: PropTypes.string,
  title: PropTypes.string,
  kicker: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  subtitle: PropTypes.string,
}
