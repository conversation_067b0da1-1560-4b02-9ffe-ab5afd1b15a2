import React from 'react'
import PropTypes from 'prop-types'

function getButtonSizeClasses(size) {
  if (size === 'lg') {
    return 'h-16 w-16 p-2'
  }
  if (size === 'md') {
    return 'h-12 w-12 p-2'
  }
  return 'h-8 w-8 p-1'
}

export function ControlButton({
  onClick,
  size = 'sm',
  title,
  disabled,
  children,
  className,
}) {
  const buttonSizeClasses = getButtonSizeClasses(size)
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`flex shrink-0 items-center justify-center rounded-full bg-gray-100 outline-none ring-color1-100 ring-offset-2 transition-colors dark:bg-white/20 dark:ring-white/80 ${buttonSizeClasses} ${
        disabled
          ? 'cursor-default text-gray-400'
          : 'hover:bg-gray-200 focus:ring dark:hover:bg-white/10'
      } ${className}`}
      title={title}
    >
      {children}
    </button>
  )
}

ControlButton.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  title: PropTypes.string,
  disabled: PropTypes.bool,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  onClick: PropTypes.func,
}
