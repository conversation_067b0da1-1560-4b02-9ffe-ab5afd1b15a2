import React from 'react'

import dynamic from 'next/dynamic'
import clsx from 'clsx'

const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Icon = dynamic(() => import('ui/icons/Icon'))

/**
 * Component to render a navigation button for a scroller
 * @param {Object} props Component props
 * @param {String} props.direction The direction of the button, either 'next' or 'prev'
 * @param {Boolean} props.disabled Whether the button is disabled
 * @param {Function} props.onClick The click handler for the button
 * @returns {React.ReactElement} The scroller navigation button component
 */
export default function ScrollerNavButton({ direction, disabled, onClick }) {
  if (disabled) return null

  const wrapperClass = direction === 'next' ? 'right-0' : 'left-0'
  const faderClass =
    direction === 'next'
      ? 'right-0 bg-gradient-to-l'
      : 'left-0 bg-gradient-to-r'

  return (
    <Clickable
      onClick={() => onClick(direction)}
      className={`absolute bottom-0 top-0 z-30 flex items-center justify-center p-0 lg:p-6 duration-500 transition-all ${wrapperClass}`}
      disabled={disabled}
    >
      <div className={`absolute w-10 md:w-16 h-full top-0 ${faderClass}`} />
      <div
        className={clsx(
          'opacity-0 md:group-hover/scroller:opacity-100',
          'h-16 w-16 p-4 xl:p-8 z-10 rounded-full flex items-center justify-center',
          'transition-all duration-300 ease-in-out',
          'text-2xl lg:text-4xl text-white text-opacity-80 hover:text-opacity-90',
          'bg-gray-600 bg-opacity-30 hover:bg-opacity-50',
          'dark:bg-white dark:bg-opacity-0 dark:hover:bg-opacity-25'
        )}
      >
        <Icon
          name={direction === 'next' ? 'chevron-right' : 'chevron-left'}
          className="drop-shadow-md"
        />
      </div>
    </Clickable>
  )
}
