import React, { useCallback, useEffect, useState } from 'react'

import dynamic from 'next/dynamic'

import clsx from 'clsx'

import { useMatchMedia } from 'hooks/useMatchMedia'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from 'ui/data-display/EmblaCarousel'
import useFlex from 'ui/helpers/useFlex'
import { media } from 'utils/media'

const ScrollerNavButton = dynamic(
  () => import('./components/ScrollerNavButton')
)

/**
 * Scroller component to render a horizontal scroller with navigation buttons
 * @param {Object} props Component props
 * @param {React.ReactNode[]} props.children The items to render in the scroller
 * @param {Object} props.maxVisibleItems The maximum number of items visible per breakpoint
 * @param {Object} props.align The alignment configuration for the scroller
 * @returns {React.ReactElement} The scroller component
 */
export default function Scroller({
  children,
  maxVisibleItems = {
    xs: '1',
    sm: '2',
    md: '3',
    lg: '4',
  },
  align = { xs: 'start' },
}) {
  const [api, setApi] = useState()
  const [canScrollNext, setCanScrollNext] = useState(false)
  const [canScrollPrev, setCanScrollPrev] = useState(false)

  const isSm = useMatchMedia(media.sm)
  const isMd = useMatchMedia(media.md)
  const isLg = useMatchMedia(media.lg)
  const isXl = useMatchMedia(media.xl)

  let slidesCount

  switch (true) {
    case Boolean(isXl && maxVisibleItems.xl):
      slidesCount = maxVisibleItems.xl
      break
    case Boolean(isLg && maxVisibleItems.lg):
      slidesCount = maxVisibleItems.lg
      break
    case Boolean(isMd && maxVisibleItems.md):
      slidesCount = maxVisibleItems.md
      break
    case Boolean(isSm && maxVisibleItems.sm):
      slidesCount = maxVisibleItems.sm
      break
    default:
      slidesCount = maxVisibleItems.xs || 1
  }

  const flexClasses = useFlex('x', align, {})

  const onScroll = useCallback(() => {
    setCanScrollPrev(api?.canScrollPrev())
    setCanScrollNext(api?.canScrollNext())
  }, [api])

  useEffect(() => {
    api?.on('scroll', onScroll)
    onScroll()
  }, [api, onScroll])

  const gradientEnabledBoth = canScrollPrev && canScrollNext
  const gradientEnabledLeft = canScrollPrev && !canScrollNext
  const gradientEnabledRight = !canScrollPrev && canScrollNext

  return (
    <Carousel
      setApi={setApi}
      opts={{
        loop: false,
        align: 'start',
        duration: 30,
        slidesToScroll: Math.max(slidesCount - 1, 1),
      }}
      className="group/scroller w-screen overflow-hidden md:w-full -my-6"
    >
      <div
        className={clsx(
          gradientEnabledBoth &&
            'opacity-gradient-x-4-4 lg:opacity-gradient-x-3-3',
          gradientEnabledLeft &&
            'opacity-gradient-x-4-0 lg:opacity-gradient-x-3-0',
          gradientEnabledRight &&
            'opacity-gradient-x-0-4 lg:opacity-gradient-x-0-3'
        )}
      >
        <CarouselContent className={`${flexClasses} py-6 -mr-4 lg:-mr-8`}>
          {React.Children.map(children, (child, index) => (
            <CarouselItem
              className="pr-4 lg:pr-8"
              style={{
                flexBasis: `${
                  (1 / slidesCount) * (children.length > slidesCount ? 94 : 100)
                }%`,
              }}
            >
              {/* The child here is a block that's wrapped in a Content component */}
              {React.cloneElement(child, {
                animationOrigin:
                  index === 0
                    ? 'left'
                    : index === children.length - 1
                      ? 'right'
                      : 'center',
                className: 'min-h-px shrink-0', // This prevents a bug in firefox with lazy loading images
              })}
            </CarouselItem>
          ))}
        </CarouselContent>
      </div>
      <ScrollerNavButton
        direction="prev"
        onClick={api?.scrollPrev}
        disabled={!canScrollPrev}
      />
      <ScrollerNavButton
        direction="next"
        onClick={api?.scrollNext}
        disabled={!canScrollNext}
      />
    </Carousel>
  )
}
