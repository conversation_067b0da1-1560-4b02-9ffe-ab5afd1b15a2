import React, { useEffect, useRef, useState } from 'react'
import clsx from 'clsx'

import { useMatchMedia } from 'hooks/useMatchMedia'
import { media } from 'utils/media'
import { isEmpty } from 'utils/arrays'

import dynamic from 'next/dynamic'

import useScrollPosition from 'hooks/useScrollPosition'
import useInView from 'hooks/useInView'

import BackgroundVideo from 'ui/data-display/BackgroundVideo'

const Badge = dynamic(() => import('ui/feedback/Badge'))
const Button = dynamic(() => import('ui/buttons/Button'))
const GradientLayer = dynamic(() => import('./components/GradientLayer'))
const Image = dynamic(() => import('ui/data-display/Image'))
const Player = dynamic(() => import('ui/data-display/Player'))
const Text = dynamic(() => import('ui/typography/Text'))

import { useImageUrl } from 'ui/data-display/Image/hooks'

const HERO_IMAGE_QUALITY = 80

// Get styles to add fade-out for the hero image or video
function useMediaStyle(ref, { fadeOutPoint = 200, hidden } = {}) {
  const scrollPosition = useScrollPosition(ref)
  return hidden
    ? {
        opacity: 0,
      }
    : {
        transitionDuration: '800ms',
        opacity: scrollPosition > fadeOutPoint ? 0.2 : 1,
      }
}

/**
 * MediaHero component to render a hero section with an image and an optional video background
 * @param {Object} props
 * @param {Object} props.logo The logo image to display (replaces the title)
 * @param {Object} props.image The image to display in the background
 * @param {Object} props.mobileImage The image to display in the background on mobile
 * @param {String} props.title The title to display in the hero (not displayed if logo is provided)
 * @param {Object} props.titleTextSize The text size configuration for the title
 * @param {String} props.description The description to display in the hero
 * @param {Object} props.descriptionTextSize The text size configuration for the description
 * @param {Boolean} props.showDescriptionMobile Whether to show the description on mobile
 * @param {String} props.videoAccountId The account ID for the video provider
 * @param {String} props.videoProvider The video provider (e.g. 'cloudflare')
 * @param {String} props.videoId The video ID to display
 * @param {Object} props.videoPoster The video poster image
 * @param {Number} props.videoDelay The delay in milliseconds before the video starts playing
 * @param {String[]} props.metaData The metadata to display under the title
 * @param {String[]} props.tags The tags to display under the metadata
 * @param {Object} props.ctaIcon The icon to display on the primary CTA button
 * @param {String} props.ctaLabel The label to display on the primary CTA button
 * @param {String} props.ctaUrl The URL to navigate to on the primary CTA button click
 * @param {String} props.ctaVariant The variant for the primary CTA button
 * @param {Object} props.ctaVideoIcon The icon to display on the video CTA button
 * @param {String} props.ctaVideoId The video ID for the video CTA button
 * @param {String} props.ctaVideoLabel The label to display on the video CTA button
 * @param {Function} props.ctaVideoOnOpen The function to call when the video CTA button is clicked
 * @param {Function} props.ctaVideoOnClose The function to call when the video CTA button is closed
 * @param {String} props.ctaVideoProvider The video provider for the video CTA button
 * @param {String} props.gradientBaseColor The base color for the gradient overlay
 * @returns {React.ReactElement} The MediaHero component
 */
export default function MediaHero({
  logo,
  image,
  mobileImage,
  title,
  titleTextSize = { xs: '2xl', md: '4xl' },
  description,
  descriptionTextSize = { xs: 'md', md: 'xl' },
  showDescriptionMobile,
  videoAccountId,
  videoProvider,
  videoId,
  videoPoster,
  videoDelay = 3000,
  metaData = [],
  tags = [],
  ctaIcon,
  ctaLabel,
  ctaUrl,
  ctaVariant = 'primary',
  ctaVideoIcon,
  ctaVideoId,
  ctaVideoLabel,
  ctaVideoOnOpen,
  ctaVideoOnClose,
  ctaVideoProvider,
  gradientBaseColor,
}) {
  const imageLoader = useImageUrl(image)
  const mobileImageLoader = useImageUrl(mobileImage)
  const isMd = useMatchMedia(media.md)
  const isLg = useMatchMedia(media.lg)

  const _imageFile = isMd ? image : mobileImage || image
  const _imageLoader = isMd
    ? imageLoader
    : mobileImage
      ? mobileImageLoader
      : imageLoader

  const hasMetaData = !isEmpty(metaData)
  const hasTags = !isEmpty(tags)
  const hasVideo = videoAccountId && videoProvider && videoId

  const heroRef = useRef()
  const isInView = useInView(heroRef, { threshold: 0.5 })

  const [videoPlaying, setVideoPlaying] = useState(false)
  useEffect(() => {
    if (hasVideo && isInView) {
      setTimeout(() => setVideoPlaying(true), videoDelay)
    }
    return () => setVideoPlaying(false)
  }, [hasVideo, isInView, videoDelay])

  const videoStyle = useMediaStyle(heroRef, { hidden: !videoPlaying })
  const imageStyle = useMediaStyle(heroRef, { hidden: videoPlaying })

  return (
    <div
      className="relative w-full min-h-[380px] md:min-h-[600px] lg:min-h-[700px] 2xl:min-h-[900px] overflow-hidden bg-gray-800"
      ref={heroRef}
    >
      <Image
        className="absolute w-full h-full inset-0 object-cover object-top"
        file={_imageFile}
        alt={title}
        loader={_imageLoader}
        quality={HERO_IMAGE_QUALITY}
        priority={true}
        lazy={false}
        sizes="xl:1920px lg:1180px md:768px 384px"
        style={imageStyle}
      />
      <BackgroundVideo
        accountId={videoAccountId}
        poster={videoPoster}
        provider={videoProvider}
        videoId={videoId}
        style={videoStyle}
      />
      <GradientLayer gradientBaseColor={gradientBaseColor} />
      <div
        className={clsx(
          'absolute inset-x-0 h-full px-8 py-8 md:py-24 md:pl-16 lg:pl-28 lg:pr-0 lg:py-24 md:max-w-[40rem]',
          'flex flex-col gap-6 md:gap-8 justify-end'
        )}
      >
        {(title || description || logo) && (
          <div
            className={clsx(
              'flex flex-col items-center md:items-start px-4 md:px-0 group/content',
              { 'gap-1 md:gap-4': !logo, 'gap-4': logo }
            )}
          >
            {logo && (
              <Image
                className="max-h-14 md:max-h-16 lg:max-h-28 px-12 md:px-0 -mb-2 md:mb-4"
                file={logo}
                alt={title}
                objectFit="contain"
                objectPosition={isMd ? 'left' : 'center'}
                loader={imageLoader}
                sizes="lg:500 400px"
              />
            )}
            {title && !logo && (
              <Text
                as="h2"
                textSize={titleTextSize}
                fontWeight="bold"
                className="leading-tight md:leading-normal"
                color="white"
              >
                {title}
              </Text>
            )}
            {description && (isMd || showDescriptionMobile) && (
              <Text
                as="h2"
                textSize={descriptionTextSize}
                color="gray-300"
                className="line-clamp-3 md:line-clamp-3"
              >
                {description}
              </Text>
            )}
          </div>
        )}
        {(hasMetaData || hasTags) && isLg && (
          <div className="space-y-2">
            {hasMetaData && (
              <div className="text-white font-semibold text-lg">
                {metaData.join(' • ')}
              </div>
            )}
            {hasTags && (
              <div className="flex gap-1">
                {tags.map((tag, key) => (
                  <Badge key={key} className="text-white border" label={tag} />
                ))}
              </div>
            )}
          </div>
        )}
        <div className="w-auto flex flex-row gap-4 justify-center md:justify-start">
          {ctaLabel && ctaUrl && (
            <Button
              url={ctaUrl}
              icon={ctaIcon}
              iconClass="scale-75"
              variant={ctaVariant}
              className="rounded-full"
              label={ctaLabel}
            />
          )}
          {ctaVideoProvider && ctaVideoId && (
            <Player
              provider={ctaVideoProvider}
              id={ctaVideoId}
              ctaIcon={ctaVideoIcon}
              ctaLabel={ctaVideoLabel}
              ctaVariant="secondary"
              variant="lightboxButton"
              ctaClass="rounded-full"
              onLightboxOpen={ctaVideoOnOpen}
              onLightboxClose={ctaVideoOnClose}
            />
          )}
        </div>
      </div>
    </div>
  )
}
