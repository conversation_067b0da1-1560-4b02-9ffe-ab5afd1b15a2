import React from 'react'

import clsx from 'clsx'

export default function <PERSON><PERSON><PERSON><PERSON><PERSON>er({ gradientBaseColor }) {
  return (
    <>
      {/* Left gradient (linear), below are classnames that should be kept, because of dynamic classnames */}
      {/* from-gray-700/85 from-gray-800/85 from-gray-900/85 from-gray-950/85 */}
      {/* via-gray-700/60 via-gray-800/60 via-gray-900/60 via-gray-950/60 */}
      <div
        className={clsx(
          'hidden md:block',
          'absolute left-0 w-2/3 h-full',
          `bg-gradient-to-r from-${gradientBaseColor}/85 via-${gradientBaseColor}/60 via-40% to-transparent`
        )}
      />
      {/* Bottom gradient (linear), below are classnames that should be kept, because of dynamic classnames */}
      {/* from-gray-700 from-gray-800 from-gray-900 from-gray-950 */}
      <div
        className={clsx(
          'absolute bottom-0 w-full',
          `bg-gradient-to-t from-${gradientBaseColor} to-transparent`,
          'h-full md:h-96 lg:h-96 xl:h-52'
        )}
      />
    </>
  )
}
