import { getImageUrl } from 'utils/images'
import { DEFAULT_QUALITY } from '../constants'

/**
 * Simple Image component to render an image tag (non-Next.js Image component)
 * @param {Object} props Component props
 * @param {String} props.alt Image alt text
 * @param {Object} props.file Image file object
 * @param {String} props.src Image source URL
 * @param {Number} props.srcWidth Image source width
 * @param {Number} props.quality Image quality
 * @returns {React.ReactElement}
 */
export function Img({
  alt,
  file,
  src,
  srcWidth = 500,
  quality = DEFAULT_QUALITY,
  ...rest
}) {
  const imgSrc = file ? getImageUrl(file, `w:${srcWidth},q:${quality}`) : src

  return <img alt={alt} src={imgSrc} {...rest} /> // eslint-disable-line @next/next/no-img-element
}
