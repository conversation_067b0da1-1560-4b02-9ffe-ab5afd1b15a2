import { getImageUrl } from 'utils/images'
import { breakpoints } from 'utils/media'
import { DEFAULT_QUALITY, DEFAULT_WIDTH } from './constants'
import { useCallback, useMemo } from 'react'

const deviceSizes = {
  'xs': `${breakpoints.xs}px`,
  'sm': `${breakpoints.sm}px`,
  'md': `${breakpoints.md}px`,
  'lg': `${breakpoints.lg}px`,
  'xl': `${breakpoints.xl}px`,
  '2xl': `${breakpoints['2xl']}px`,
}

/**
 * Given a formatted string returns another one with media queries like `img` [sizes]
 * @param {string} sizes String with responsive sizes. Example: `'md:192px sm:128px 350px'`
 * @returns `string` - Output: `'(min-width: 768px) 192px, (min-width: 640px) 128px, 350px'`
 */
export function useResponsiveSizes(sizes = '') {
  return useMemo(
    () =>
      sizes
        .split(' ')
        .map(size => {
          const [mediaQuery, width] = size.split(':')
          return deviceSizes[mediaQuery]
            ? `(min-width: ${deviceSizes[mediaQuery]}) ${width}`
            : mediaQuery
        })
        .join(', '),
    [sizes]
  )
}

/**
 * @param {object} param0
 * @param {number} param0.width
 * @param {number} param0.quality
 * @returns
 * @example
 * getDefaultFormat({ width: 1920, quality: 100 })
 * // => 'w:1920,q:100'
 */
export function getDefaultFormat({
  width = DEFAULT_WIDTH,
  quality = DEFAULT_QUALITY,
}) {
  return `w:${width},q:${quality}`
}

/**
 * Next's Image loader for our CDN images
 * @param {Object} file A file object from the backend
 * @param {Function} getFormat Function to get the image format
 * @returns {(params: { src: String, width: Number, quality: Number }) => String}
 */
export function useImageLoader(file, getFormat = getDefaultFormat) {
  return useCallback(
    ({ src, width, quality }) => {
      return getImageUrl({ ...file, name: src }, getFormat({ width, quality }))
    },
    [file, getFormat]
  )
}
