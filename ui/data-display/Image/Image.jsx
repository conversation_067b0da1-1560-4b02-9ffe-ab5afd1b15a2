import NextImage from 'next/image'

import { DEFAULT_QUALITY } from './constants'
import { useImageLoader, useResponsiveSizes } from './utils'
import useAspectRatio from 'ui/helpers/useAspectRatio'

/**
 * @typedef {'fill' | 'contain' | 'cover' | 'none' | 'scale-down'} ImageObjectFit A CSS object-fit property value
 * @typedef {'bottom' | 'center' | 'left' | 'left-bottom' | 'left-top' | 'right' | 'right-bottom' | 'right-top' | 'top'} ImageObjectPosition A CSS object-position property value
 * @typedef {'16/9' | '1/1' | '3/4' | '4/3' | '2/3' | '9/16' | 'auto'} ImageAspectRatio An aspect ratio value
 */

/**
 * Object fit styles
 * @type {Object.<ImageObjectFit, string>}
 */
const objectFitClasses = {
  'contain': 'object-contain',
  'cover': 'object-cover',
  'fill': 'object-fill',
  'none': 'object-none',
  'scale-down': 'object-scale-down',
}

/**
 * Object position styles
 * @type {Object.<ImageObjectPosition, string>}
 */
const objectPositionClasses = {
  'bottom': 'object-bottom',
  'center': 'object-center',
  'left': 'object-left',
  'left-bottom': 'object-left-bottom',
  'left-top': 'object-left-top',
  'right': 'object-right',
  'right-bottom': 'object-right-bottom',
  'right-top': 'object-right-top',
  'top': 'object-top',
}

/**
 * Image component to render images with lazy loading and responsive sizes
 * // TODO: Add support for file.blurhash
 * @param {Object} props Component props
 * @param {String} props.alt Image alt text
 * @param {ImageAspectRatio} props.aspectRatio Aspect ratio of the image
 * @param {String} props.className Additional classes to apply to the image
 * @param {Object} props.file Image file object
 * @param {Function} props.getImageFormat Function to get the image format
 * @param {Number} props.height Image height
 * @param {Boolean} props.lazy Lazy load the image
 * @param {ImageObjectFit} props.objectFit CSS object-fit property
 * @param {ImageObjectPosition} props.objectPosition CSS object-position property
 * @param {Function} props.onLoadingComplete Callback function when the image has loaded
 * @param {Boolean} props.priority Priority load the image
 * @param {Number} props.quality Image quality (0-100)
 * @param {String} props.sizes Image sizes for responsive images (e.g. 'md:192px sm:128px 350px')
 * @param {String} props.src Image source
 * @param {Number} props.width Image width
 * @returns {React.ReactElement} Image component
 */
export function Image({
  alt,
  aspectRatio,
  className = '',
  file,
  getImageFormat,
  height = 600,
  lazy = true,
  objectFit,
  objectPosition,
  onLoadingComplete,
  priority,
  quality = DEFAULT_QUALITY,
  sizes = '',
  src,
  width = 800,
}) {
  const imageLoader = useImageLoader(file, getImageFormat)

  const responsiveSizes = useResponsiveSizes(sizes)

  // sets objectFit to 'contain' for SVGs to avoid stretching, otherwise use the prop value
  objectFit = file?.mime === 'image/svg+xml' ? 'contain' : objectFit

  const fitClass =
    objectFitClasses[objectFit] || objectFitClasses['cover'] || ''
  const positionClass =
    objectPositionClasses[objectPosition] ||
    objectPositionClasses['center'] ||
    ''
  const ratio = aspectRatio || 'auto'
  const aspectRatioClass = useAspectRatio({ xs: ratio }) || ''

  return (
    <NextImage
      className={`${fitClass} ${positionClass} ${aspectRatioClass} ${className}`}
      loader={file ? imageLoader : undefined}
      alt={alt || file?.originalFilename}
      src={file ? file.name : src}
      width={file?.width || width}
      height={file?.height || height}
      priority={lazy ? undefined : priority}
      sizes={responsiveSizes}
      quality={quality}
      loading={lazy ? 'lazy' : 'eager'}
      onLoadingComplete={onLoadingComplete}
    />
  )
}
