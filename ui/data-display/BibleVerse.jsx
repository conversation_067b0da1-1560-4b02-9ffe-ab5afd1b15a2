import React from 'react'

/**
 * Bible verse component
 * @param {Object} props Component props
 * @param {string} props.text Passage of the Bible verse
 * @param {string} props.translation Translation of the Bible verse
 * @param {string} props.verses Verses of the Bible verse
 * @returns {React.ReactElement} Bible verse component
 */
export default function BibleVerse({ passage, bible, text }) {
  return (
    <div className="px-0 my-16 text-center md:px-8 xl:px-16">
      <div className="pb-6 font-serif text-lg italic md:text-xl">{text}</div>
      <div className="text-lg font-semi-bold">{passage}</div>
      {bible && <div className="text-sm text-gray-600">{bible}</div>}
    </div>
  )
}
