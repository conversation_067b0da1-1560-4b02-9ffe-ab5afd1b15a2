import React from 'react'

import Field from '../Field'
import { CheckboxGroup } from './CheckboxGroup'

export function CheckboxGroupField({
  className,
  disabled,
  error,
  extrasClass,
  help,
  id,
  label,
  labelClass,
  labelPrefix,
  labelVariant,
  name,
  onChange,
  options = [],
  required,
  style,
  value,
  variant,
}) {
  if (options.length === 0) {
    return null
  }

  return (
    <Field
      className={className}
      disabled={disabled}
      error={error}
      extrasClass={extrasClass ?? 'ml-5'}
      help={help}
      label={label}
      labelClass={labelClass}
      labelPrefix={labelPrefix}
      labelVariant={labelVariant}
      name={name}
      required={required}
      style={style}
      variant={variant}
    >
      <CheckboxGroup
        disabled={disabled}
        hasError={error !== undefined}
        id={id}
        name={name}
        onChange={onChange}
        options={options}
        value={value}
      />
    </Field>
  )
}
