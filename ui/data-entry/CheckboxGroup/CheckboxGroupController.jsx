import React, { useCallback } from 'react'

import { useController } from 'react-hook-form'

import useRules from '../useRules'
import { CheckboxGroupField } from './CheckboxGroupField'

export function CheckboxGroupController({
  className,
  defaultValue,
  disabled,
  error,
  help,
  id,
  label,
  labelClass,
  labelPrefix,
  labelVariant,
  name,
  onChange,
  options,
  required,
  shouldUnregister,
  validate,
  variant,
}) {
  const rules = useRules({ required, validate })

  const { field, fieldState } = useController({
    defaultValue,
    disabled,
    name,
    rules,
    shouldUnregister,
  })

  const onFieldChange = useCallback(
    value => {
      field.onChange(value)
      if (typeof onChange === 'function') {
        onChange(value, field)
      }
    },
    [field, onChange]
  )

  return (
    <CheckboxGroupField
      name={name}
      id={id}
      className={className}
      disabled={disabled}
      required={required}
      options={options}
      label={label}
      labelClass={labelClass}
      labelPrefix={labelPrefix}
      labelVariant={labelVariant}
      help={help}
      error={error || fieldState.error}
      onChange={onFieldChange}
      variant={variant}
    />
  )
}
