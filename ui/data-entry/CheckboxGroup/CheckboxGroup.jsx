import React, { useCallback } from 'react'

import { Checkbox } from '../Checkbox'

export function CheckboxGroup({
  disabled,
  hasError,
  id,
  name,
  onChange,
  options = [],
  value = [],
  variant,
}) {
  const [groupValue, setGroupValue] = React.useState(() => value || [])

  const idVal = id || name

  const handleChange = useCallback(
    event => {
      const newValue = event.target.checked
        ? [...groupValue, event.target.value]
        : groupValue.filter(v => v !== event.target.value)

      setGroupValue(newValue)
      onChange?.(newValue)
    },
    [groupValue, onChange]
  )

  if (options.length === 0) {
    return null
  }

  return (
    <div className="flex flex-col gap-2">
      {options.map(({ label, value: optionValue }, index) => (
        <Checkbox
          checked={groupValue.includes(optionValue)}
          disabled={disabled}
          hasError={hasError}
          id={`${idVal}-${optionValue}`}
          key={`${idVal}-option-${optionValue}-${index}`}
          label={label}
          name={name}
          onChange={handleChange}
          value={optionValue}
          variant={variant}
        />
      ))}
    </div>
  )
}
