import React, { useCallback } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useForm<PERSON>ontext, Controller } from 'react-hook-form'

import useRules from './useRules'

const Field = dynamic(() => import('./Field'))

export function Input({ className = '', hasError, type = 'text', ...props }) {
  const borderClass = hasError
    ? 'border-danger-600 focus:border-danger-400'
    : 'focus:border-color1-400'
  return (
    <input
      className={`form-input w-full rounded border border-gray-400 px-3 py-2 text-gray-800 placeholder-gray-400 outline-none ${borderClass} ${className}`}
      type={type}
      {...props}
    />
  )
}
Input.propTypes = {
  className: PropTypes.string,
  hasError: PropTypes.bool,
  type: PropTypes.oneOf(['password', 'text', 'email', 'phone', 'number']),
}

export function InputField({
  autoComplete,
  className = '',
  disabled,
  error,
  help,
  inputClass = '',
  label,
  labelClass = '',
  labelPrefix,
  name,
  onBlur,
  onKeyPress,
  onChange,
  placeholder,
  required,
  accept,
  type = 'text',
  value,
  errorMessages,
}) {
  return (
    <Field
      className={className}
      error={error}
      help={help}
      label={label}
      labelClass={labelClass}
      labelPrefix={labelPrefix}
      name={name}
      required={required}
      errorMessages={errorMessages}
    >
      <Input
        autoComplete={autoComplete ? autoComplete : null}
        className={inputClass}
        disabled={disabled}
        hasError={error !== undefined}
        name={name}
        id={name}
        onBlur={onBlur}
        onChange={onChange}
        type={type}
        onKeyPress={onKeyPress}
        placeholder={placeholder}
        value={value}
        accept={type === 'file' ? accept : undefined} //https://html.spec.whatwg.org/multipage/input.html#attr-input-accept
      />
    </Field>
  )
}

InputField.propTypes = {
  autoComplete: PropTypes.oneOf([
    'off',
    'name',
    'email',
    'current-password',
    'new-password',
  ]),
  className: PropTypes.string,
  defaultValue: PropTypes.string,
  disabled: PropTypes.bool,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  help: PropTypes.string,
  inputClass: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.node,
  name: PropTypes.string.isRequired,
  onBlur: PropTypes.func,
  onChange: PropTypes.func,
  onKeyPress: PropTypes.func,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  rules: PropTypes.object,
  type: PropTypes.oneOf([
    'password',
    'text',
    'email',
    'phone',
    'number',
    'file',
  ]),
  value: PropTypes.string,
  accept: PropTypes.string,
}

export default function InputController({
  autocomplete,
  accept,
  className = '',
  defaultValue = '',
  disabled,
  error,
  help,
  inputClass = '',
  label,
  labelClass = '',
  labelPrefix,
  max,
  maxLength,
  min,
  minLength,
  name,
  onBlur,
  onChange,
  pattern,
  placeholder,
  required,
  type = 'text',
  validate,
  errorMessages,
}) {
  const { control } = useFormContext()

  const rules = useRules({
    max,
    maxLength,
    min,
    minLength,
    pattern,
    required,
    validate,
  })

  const onFieldChange = useCallback(
    field => event => {
      // Handles file input
      if (type === 'file') {
        const file = event.target.files[0]
        if (file) {
          field.onChange(file)
        } else {
          field.onChange(event)
        }
      } else {
        field.onChange(event)
      }

      if (typeof onChange === 'function') {
        const currentValue = event.currentTarget.value
        onChange(currentValue, field)
      }
    },
    [onChange, type]
  )

  const onFieldBlur = useCallback(
    field => event => {
      field.onBlur(event)

      if (typeof onBlur === 'function') {
        onBlur(event.currentTarget.value, field)
      }
    },
    [onBlur]
  )

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field, fieldState }) => (
        <InputField
          className={className}
          disabled={disabled}
          error={error || fieldState.error}
          help={help}
          inputClass={inputClass}
          label={label}
          labelClass={labelClass}
          labelPrefix={labelPrefix}
          name={name}
          required={required}
          autoComplete={autocomplete}
          id={name}
          onBlur={onFieldBlur(field)}
          onChange={onFieldChange(field)}
          type={type}
          placeholder={placeholder}
          value={type === 'file' ? field.value?.fileName : field.value}
          accept={accept}
          errorMessages={errorMessages}
        />
      )}
    />
  )
}
InputController.propTypes = {
  autocomplete: PropTypes.oneOf([
    'off',
    'name',
    'email',
    'current-password',
    'one-time-code',
    'new-password',
  ]),
  className: PropTypes.string,
  defaultValue: PropTypes.string,
  disabled: PropTypes.bool,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  help: PropTypes.string,
  inputClass: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.node,
  max: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.shape({ value: PropTypes.number, message: PropTypes.string }),
  ]),
  maxLength: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.shape({ value: PropTypes.number, message: PropTypes.string }),
  ]),
  min: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.shape({ value: PropTypes.number, message: PropTypes.string }),
  ]),
  minLength: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.shape({ value: PropTypes.number, message: PropTypes.string }),
  ]),
  name: PropTypes.string.isRequired,
  onBlur: PropTypes.func,
  onChange: PropTypes.func,
  pattern: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.shape({ value: PropTypes.object, message: PropTypes.string }),
  ]),
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  rules: PropTypes.object,
  type: PropTypes.oneOf(['password', 'text', 'email', 'phone', 'file']),
  validate: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  accept: PropTypes.string,
}
