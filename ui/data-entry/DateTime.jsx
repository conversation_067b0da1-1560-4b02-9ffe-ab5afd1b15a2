import React, { useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import addDays from 'date-fns/addDays'
import addHours from 'date-fns/addHours'
import addMonths from 'date-fns/addMonths'
import eachDayOfInterval from 'date-fns/eachDayOfInterval'
import endOfMonth from 'date-fns/endOfMonth'
import format from 'date-fns/format'
import formatISO from 'date-fns/formatISO'
import getDay from 'date-fns/getDay'
import getHours from 'date-fns/getHours'
import getMinutes from 'date-fns/getMinutes'
import getMonth from 'date-fns/getMonth'
import getYear from 'date-fns/getYear'
import isToday from 'date-fns/isToday'
import isSameDay from 'date-fns/isSameDay'
import parseISO from 'date-fns/parseISO'
import startOfMonth from 'date-fns/startOfMonth'
import startOfWeek from 'date-fns/startOfWeek'
import setHours from 'date-fns/setHours'
import setMinutes from 'date-fns/setMinutes'
import setMonth from 'date-fns/setMonth'
import setYear from 'date-fns/setYear'
import subHours from 'date-fns/subHours'
import subMonths from 'date-fns/subMonths'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'
import { Controller, useFormContext } from 'react-hook-form'

import { Input } from 'ui/data-entry/Input'
import useClickOutside from 'ui/helpers/useClickOutside'
import { range } from 'utils/arrays'
import { useDatetimeLocale } from 'utils/datetime'
import useRules from './useRules'

const Button = dynamic(() => import('ui/buttons/Button'))
const Icon = dynamic(() => import('ui/icons/Icon'))
const Field = dynamic(() => import('./Field'))

const weekStarts = [
  'col-start-1',
  'col-start-2',
  'col-start-3',
  'col-start-4',
  'col-start-5',
  'col-start-6',
  'col-start-7',
]

const now = new Date()
const firstDOW = startOfWeek(now)

function checkDateRange(date, min, max) {
  return min && max
    ? date >= min && date <= max
    : min
      ? date >= min
      : max
        ? date <= max
        : true
}

function Day({ date, onClick, className = '', selected, disabled }) {
  return (
    <button
      disabled={disabled}
      className={`flex items-center justify-center rounded border py-1 outline-none ring-color1-400 focus:ring-1 ${
        selected
          ? 'border-transparent bg-color1-700 font-semibold text-color1-100'
          : isToday(date)
            ? 'border-color1-300 bg-color1-200 text-color1-800'
            : 'border-transparent bg-gray-200 text-gray-800'
      } ${disabled ? 'cursor-not-allowed opacity-50' : ''} ${className}`}
      onClick={e => {
        e.preventDefault()
        onClick(date)
      }}
    >
      {format(date, 'd')}
    </button>
  )
}
Day.propTypes = {
  className: PropTypes.string,
  date: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
  disabled: PropTypes.bool,
  onClick: PropTypes.func,
  selected: PropTypes.bool,
}

export function DatePicker({ date, onChange, min, max }) {
  // This prevents a bug if the date is passed as null
  // (for example, opening the picker and closing it without choosing a date)
  if (!date) {
    date = new Date()
  }

  const monthFirstDay = startOfMonth(date)
  const monthLastDay = endOfMonth(date)
  const monthFirstDayOfWeek = getDay(monthFirstDay)
  const locale = useDatetimeLocale()

  const shortWeekDaysArray = range(0, 6).map(i =>
    format(addDays(firstDOW, i), 'EEEEEE', { locale })
  )

  const monthDays = eachDayOfInterval({
    start: monthFirstDay,
    end: monthLastDay,
  })

  const months = range(0, 11)

  return (
    <div className="flex select-none flex-col space-y-4">
      <div className="flex items-center justify-between space-x-4 rtl:space-x-reverse">
        <button className="p-1" onClick={() => onChange(subMonths(date, 1))}>
          <Icon name="chevron-left" />
        </button>
        <div className="flex grow flex-row items-center justify-center space-x-2 text-xl rtl:space-x-reverse">
          <select
            className="font-bold focus:border-color1-400"
            value={getMonth(date)}
            onChange={({ currentTarget }) =>
              onChange(setMonth(date, currentTarget.value))
            }
            onBlur={({ currentTarget }) =>
              onChange(setMonth(date, currentTarget.value))
            }
          >
            {months.map(month => (
              <option key={`month-${month}`} value={month}>
                {format(setMonth(date, month), 'LLLL', { locale })}
              </option>
            ))}
          </select>
          <input
            className="w-16 focus:border-color1-400 focus:ring-0"
            onChange={({ currentTarget }) => {
              const newYear = currentTarget.value
              if (newYear >= -6000 && newYear <= 3000) {
                onChange(setYear(date, newYear))
              }
            }}
            type="number"
            value={getYear(date).toString()}
          />
        </div>
        <button onClick={() => onChange(addMonths(date, 1))} className="p-1">
          <Icon name="chevron-right" />
        </button>
      </div>
      <div className="grid min-w-full grid-cols-7 gap-1">
        {shortWeekDaysArray.map(d => (
          <div
            className="flex items-center justify-center p-1 font-semibold"
            key={`weekday-${d}`}
          >
            {d}
          </div>
        ))}
        {monthDays.map((day, i) => (
          <Day
            className={`${i === 0 ? weekStarts[monthFirstDayOfWeek] : ''}`}
            selected={isSameDay(day, date)}
            disabled={!checkDateRange(date, min, max)}
            date={day}
            key={`day-${i + 1}`}
            onClick={() =>
              onChange(
                setHours(setMinutes(day, getMinutes(date)), getHours(date))
              )
            }
          />
        ))}
      </div>
    </div>
  )
}
DatePicker.propTypes = {
  date: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
  min: PropTypes.object,
  max: PropTypes.object,
  onChange: PropTypes.func,
}

function TimeInput(props) {
  return (
    <input
      className="w-20 rounded border-gray-300 py-2 pl-3 text-center focus:border-color1-700 focus:outline-none focus:ring focus:ring-color1-200"
      type="number"
      {...props}
    />
  )
}

function AmPmSelect(props) {
  return (
    <select
      {...props}
      className="w-20 rounded border-gray-300 py-2 pl-3 text-center focus:border-color1-700 focus:outline-none focus:ring focus:ring-color1-200"
    >
      <option value="am" label="AM" />
      <option value="pm" label="PM" />
    </select>
  )
}

export function TimePicker({ date, onChange }) {
  const isTimeAMPM = format(date, 'p').match(/([ap][m])/i) !== null
  const maxHour = isTimeAMPM ? 12 : 23
  const minHour = isTimeAMPM ? 1 : 0

  return (
    <div className="flex flex-row items-center justify-center space-x-2 p-2 rtl:space-x-reverse">
      <TimeInput
        value={format(date, isTimeAMPM ? 'hh' : 'HH')}
        onChange={({ target }) => {
          const hours = target.valueAsNumber
          onChange(
            setHours(
              date,
              hours > maxHour ? minHour : hours < minHour ? maxHour : hours
            )
          )
        }}
      />
      <span className="pb-1 font-bold text-gray-500 text-xl">:</span>
      <TimeInput
        value={format(date, 'mm')}
        onChange={({ target }) => {
          const minutes = target.valueAsNumber
          onChange(
            setMinutes(date, minutes > 59 ? 0 : minutes < 0 ? 59 : minutes)
          )
        }}
      />
      {isTimeAMPM && (
        <AmPmSelect
          value={format(date, 'aaa')}
          onChange={e => {
            e.preventDefault()
            onChange(
              e.currentTarget.value == 'am'
                ? subHours(date, 12)
                : addHours(date, 12)
            )
          }}
        />
      )}
    </div>
  )
}
TimePicker.propTypes = {
  date: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
  onChange: PropTypes.func,
}

export function DateTime({
  asString,
  className = '',
  clearLabel,
  onChange,
  id,
  max,
  min,
  name,
  selectLabel,
  pickerTitle,
  type,
  value,
}) {
  const { t } = useTranslation()
  const { nodeRef, open, setOpen } = useClickOutside()
  const _value = typeof value === 'string' ? parseISO(value) : value
  const [date, setDate] = useState(_value || new Date())

  const formattedValue = _value
    ? format(_value, type === 'date' ? 'P' : type === 'time' ? 'p' : 'Pp')
    : ''

  const onClose = useCallback(() => {
    setDate(_value)
    setOpen(false)
  }, [_value, setOpen])

  const onUpdate = useCallback(() => {
    onChange(
      asString
        ? formatISO(date, {
            representation:
              typeof asString === 'string'
                ? asString
                : ['date', 'time'].includes(type)
                  ? type
                  : 'complete',
          })
        : date
    )
    setOpen(false)
  }, [asString, date, onChange, setOpen, type])

  const onClear = useCallback(() => {
    onChange(null)
    setOpen(false)
  }, [onChange, setOpen])

  return (
    <div className="relative flex w-full flex-col">
      <div
        className="relative flex flex-row"
        onClick={() => setOpen(!open)}
        onKeyDown={e => {
          if (['Space', 'Enter'].includes(e.code)) {
            e.preventDefault()
            setOpen(!open)
          }
        }}
        aria-keyshortcuts="Space"
        role="link"
        tabIndex={0}
      >
        <Input
          className={className}
          disabled
          id={id}
          name={name}
          type="text"
          value={formattedValue}
        />
        <Button
          className="absolute bottom-0 right-0 top-0"
          icon={type === 'time' ? 'clock' : 'calendar-alt'}
          size="sm"
          variant="flat"
        />
      </div>
      <div
        className={`fixed inset-0 flex items-center justify-center bg-black transition-opacity duration-300 ease-in-out ${
          open
            ? 'z-max bg-opacity-40  opacity-100'
            : 'z-[-99] bg-opacity-0 opacity-0'
        }`}
      >
        <div
          className="flex h-screen w-screen flex-col justify-between space-y-4 overflow-y-auto border-gray-200 bg-white p-4 transition-all duration-300 ease-in-out sm:h-auto sm:w-auto sm:rounded-md sm:border sm:shadow-xl"
          ref={nodeRef}
        >
          <div className="space-y-4">
            <div className="flex flex-row items-center justify-between">
              {pickerTitle && (
                <h3 className="ml-1 font-semibold text-lg">
                  {t(pickerTitle)}:
                </h3>
              )}
              <span className="w-2" />
              <button onClick={onClose} className="p-1">
                <Icon name="close" />
              </button>
            </div>
            {type !== 'time' && (
              <DatePicker date={date} onChange={setDate} min={min} max={max} />
            )}
            {type !== 'date' && (
              <TimePicker date={date} onChange={setDate} min={min} max={max} />
            )}
          </div>
          <div className="flex flex-row justify-between">
            <Button
              label={selectLabel || t('selectDate')}
              icon="check"
              onClick={onUpdate}
              variant="primary"
              disabled={!checkDateRange(date, min, max)}
            />
            <Button
              label={clearLabel || t('clearDate')}
              icon="trash"
              onClick={onClear}
              variant="flat"
            />
          </div>
        </div>
      </div>
    </div>
  )
}
DateTime.propTypes = {
  asString: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  className: PropTypes.string,
  clearLabel: PropTypes.string,
  id: PropTypes.string,
  max: PropTypes.object,
  min: PropTypes.object,
  name: PropTypes.string,
  onChange: PropTypes.func,
  pickerTitle: PropTypes.string,
  selectLabel: PropTypes.string,
  type: PropTypes.oneOf(['date', 'datetime', 'time']),
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}

export default function DateTimeField({
  asString,
  className = '',
  clearLabel = 'clear',
  help,
  label,
  labelClass = '',
  labelPrefix,
  max,
  min,
  name,
  required,
  selectLabel = 'select',
  pickerTitle,
  type = 'datetime',
  validate,
  value = '',
  // onChange,
}) {
  const { control } = useFormContext()

  const rules = useRules({ required, validate })

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={value}
      rules={rules}
      render={({ field, fieldState }) => (
        <Field
          className={className}
          name={name}
          label={label}
          labelClass={labelClass}
          labelPrefix={labelPrefix}
          error={fieldState.error}
          help={help}
          required={rules?.required}
        >
          <DateTime
            asString={asString}
            clearLabel={clearLabel}
            id={name}
            max={max ? parseISO(max) : undefined}
            min={min ? parseISO(min) : undefined}
            name={name}
            onChange={field.onChange}
            pickerTitle={pickerTitle}
            selectLabel={selectLabel}
            type={type}
            value={field.value}
          />
        </Field>
      )}
    />
  )
}
DateTimeField.propTypes = {
  asString: PropTypes.oneOfType([
    PropTypes.oneOf(['complete', 'date', 'time']),
    PropTypes.bool,
  ]),
  className: PropTypes.string,
  clearLabel: PropTypes.string,
  help: PropTypes.string,
  max: PropTypes.string,
  min: PropTypes.string,
  name: PropTypes.string.isRequired,
  label: PropTypes.string,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.string,
  onChange: PropTypes.func,
  required: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape({ value: PropTypes.bool, message: PropTypes.string }),
  ]),
  selectLabel: PropTypes.string,
  pickerTitle: PropTypes.string,
  type: PropTypes.oneOf(['date', 'datetime', 'time']),
  validate: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}
DateTimeField.displayName = 'DateTime'
