import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useF<PERSON><PERSON>ontex<PERSON>, Controller } from 'react-hook-form'

import useRules from './useRules'

const Field = dynamic(() => import('./Field'))

export function Select({
  className = '',
  disabled,
  name,
  onBlur,
  onChange,
  options = [],
  placeholder,
  value,
  selectClass,
}) {
  const hasValue = value && value !== 'none'

  return (
    <div className={`relative ${className}`}>
      <select
        className={`form-select block w-full ${
          selectClass || 'rounded border py-2 pl-4 pr-8' //TODO: Refactor defaults
        } ${
          disabled
            ? 'bg-gray-100 text-gray-600'
            : 'border-gray-400 text-gray-800'
        } ${hasValue ? '' : 'text-gray-400'} `}
        id={name}
        name={name}
        onBlur={onBlur}
        onChange={onChange}
        disabled={disabled}
        value={value || 'none'}
      >
        {placeholder && (
          <option disabled value="none">
            {placeholder}
          </option>
        )}
        {options.map(({ label, value, disabled }, i) => (
          <option key={`select-${name}-${i}`} value={value} disabled={disabled}>
            {label}
          </option>
        ))}
      </select>
    </div>
  )
}

Select.propTypes = {
  className: PropTypes.string,
  name: PropTypes.string,
  disabled: PropTypes.bool,
  onBlur: PropTypes.func,
  onChange: PropTypes.func,
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.bool,
    PropTypes.number,
  ]),
  placeholder: PropTypes.node,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.bool,
        PropTypes.number,
      ]),
      placeholder: PropTypes.node,
    })
  ),
}

export function SelectField({
  className,
  disabled,
  error,
  help,
  label,
  labelClass,
  labelPrefix,
  name,
  onBlur,
  onChange,
  options,
  placeholder,
  required,
  value,
  selectClass,
}) {
  return (
    <Field
      className={className}
      error={error}
      help={help}
      label={label}
      labelClass={labelClass}
      labelPrefix={labelPrefix}
      name={name}
      required={required}
    >
      <Select
        className="inline-block w-full"
        disabled={disabled}
        name={name}
        value={value}
        options={options}
        placeholder={placeholder}
        onBlur={onBlur}
        onChange={onChange}
        selectClass={selectClass}
      />
    </Field>
  )
}
SelectField.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  error: PropTypes.string,
  help: PropTypes.node,
  label: PropTypes.node,
  labelPrefix: PropTypes.node,
  labelClass: PropTypes.string,
  name: PropTypes.string,
  onBlur: PropTypes.func,
  onChange: PropTypes.func,
  options: PropTypes.array,
  placeholder: PropTypes.node,
  required: PropTypes.bool,
  value: PropTypes.any,
}

export default function SelectController({
  className,
  defaultValue = '',
  disabled,
  help,
  label,
  labelClass,
  labelPrefix,
  name,
  options,
  placeholder,
  required,
  selectClass,
  ...props
}) {
  const { control } = useFormContext()

  const rules = useRules({ required })

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field, fieldState }) => (
        <SelectField
          className={className}
          error={fieldState.error}
          help={help}
          label={label}
          labelClass={labelClass}
          labelPrefix={labelPrefix}
          name={name}
          required={required}
          disabled={disabled}
          value={field.value}
          options={options}
          placeholder={placeholder}
          onBlur={field.onBlur}
          selectClass={selectClass}
          onChange={value => {
            field.onChange(value)
            typeof props.onChange === 'function' && props.onChange(value)
          }}
        />
      )}
    />
  )
}

SelectController.propTypes = {
  className: PropTypes.string,
  defaultValue: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.bool,
    PropTypes.node,
  ]),
  disabled: PropTypes.bool,
  help: PropTypes.node,
  label: PropTypes.node,
  labelPrefix: PropTypes.node,
  labelClass: PropTypes.string,
  name: PropTypes.string,
  onChange: PropTypes.func,
  options: PropTypes.array,
  placeholder: PropTypes.node,
  required: PropTypes.bool,
}
