import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { Controller, useFormContext } from 'react-hook-form'

const Field = dynamic(() => import('./Field'))
const Radio = dynamic(() =>
  import('./Radio').then(m => ({ default: m.RadioField }))
)

export default function Scale({
  className = '',
  disabled,
  defaultValue = '',
  help,
  label,
  labelClass = '',
  labelPrefix,
  legendItems = [],
  name,
  options = [],
  required,
  rules,
}) {
  const { control, formState } = useFormContext()
  const error = formState.errors[name]

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field }) => (
        <Field
          className={className}
          error={error}
          help={help}
          label={label}
          labelClass={labelClass}
          labelPrefix={labelPrefix}
          name={name}
          required={required}
        >
          <div className="space-y-2">
            {legendItems?.length > 0 && (
              <div className="flex flex-col justify-between sm:flex-row">
                {legendItems.map((label, key) => (
                  <div className="px-4 text-lg text-gray-600" key={key}>
                    {label}
                  </div>
                ))}
              </div>
            )}
            <div className="flex flex-col justify-between space-y-4 rounded-md border border-gray-400 p-4 sm:flex-row sm:space-y-0">
              {options?.map(({ label, value }, key) => (
                <Radio
                  onChange={field.onChange}
                  checked={value === field.value}
                  id={`${name}-${key}`}
                  className="text-left rtl:text-right sm:px-2 sm:text-center"
                  disabled={disabled}
                  key={key}
                  label={label}
                  labelClass="text-gray-800"
                  labelPosition="top"
                  name={name}
                  value={value}
                />
              ))}
            </div>
          </div>
        </Field>
      )}
    />
  )
}

Scale.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  defaultValue: PropTypes.string,
  help: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.string,
  legendItems: PropTypes.array,
  name: PropTypes.string.isRequired,
  options: PropTypes.array,
  required: PropTypes.bool,
  rules: PropTypes.object,
}
