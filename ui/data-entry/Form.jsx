import React, { useEffect, useRef, useState } from 'react'
import PropTypes from 'prop-types'

import { useForm, FormProvider } from 'react-hook-form'

export default function Form({
  children,
  className = '',
  data,
  id,
  noSpacing,
  onSubmit,
  resetOnSubmit = false,
  validationMode = 'onSubmit',
}) {
  const [submitting, setSubmitting] = useState(false)
  const idRef = useRef(id)
  const methods = useForm({ defaultValues: data, mode: validationMode })
  const spacingClass = noSpacing ? '' : 'gap-8'

  useEffect(() => {
    const prevId = idRef.current

    if (!(id && prevId)) return undefined

    if (id !== prevId) {
      methods.reset({ ...data })
      idRef.current = id
    }
  }, [data, id, methods])

  const handleOnSubmit = formData => {
    if (!submitting) {
      setSubmitting(true)
      if (typeof onSubmit === 'function') {
        onSubmit(formData, setSubmitting)
      }
      if (resetOnSubmit) methods.reset({ ...data })
    }
  }

  return (
    <FormProvider
      {...methods}
      validationMode={validationMode}
      onSubmit={methods.handleSubmit(handleOnSubmit)}
    >
      <form
        className={`flex flex-col ${spacingClass} ${className}`}
        onSubmit={methods.handleSubmit(handleOnSubmit)}
        encType="multipart/form-data"
      >
        {children}
      </form>
    </FormProvider>
  )
}
Form.propTypes = {
  cancelLabel: PropTypes.string,
  children: PropTypes.node,
  className: PropTypes.string,
  data: PropTypes.object,
  disabled: PropTypes.bool,
  noSpacing: PropTypes.bool,
  id: PropTypes.string,
  onCancel: PropTypes.func,
  onSave: PropTypes.func,
  onSubmit: PropTypes.func,
  resetOnSubmit: PropTypes.bool,
  saveLabel: PropTypes.string,
  submitLabel: PropTypes.string,
  submitHidden: PropTypes.bool,
  validationMode: PropTypes.oneOf([
    'onChange',
    'onBlur',
    'onSubmit',
    'onTouched',
    'all',
  ]),
}
