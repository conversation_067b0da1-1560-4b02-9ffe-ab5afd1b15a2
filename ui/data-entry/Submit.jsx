import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useFormContext } from 'react-hook-form'

const Button = dynamic(() => import('ui/buttons/Button'))

const disabledModes = ['onBlur', 'onChange', 'onTouched']

export default function Submit({
  disabled,
  icon,
  iconClass = '',
  label = 'Submit',
  variant = 'primary',
}) {
  const { formState, onSubmit, validationMode } = useFormContext()
  const { isValid } = formState

  const isSubmitDisabled = disabled
    ? true
    : disabledModes.includes(validationMode)
      ? !isValid
      : false

  return (
    <Button
      disabled={isSubmitDisabled}
      label={label}
      onClick={onSubmit}
      icon={icon}
      iconClass={iconClass}
      type="submit"
      variant={variant}
    />
  )
}
Submit.propTypes = {
  disabled: PropTypes.bool,
  icon: PropTypes.string,
  iconClass: PropTypes.string,
  label: PropTypes.string,
  variant: PropTypes.oneOf([
    'base',
    'primary',
    'secondary',
    'tertiary',
    'flat',
    'danger',
  ]),
}
