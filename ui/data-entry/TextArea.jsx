import React, { useCallback } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { Controller, useFormContext } from 'react-hook-form'

import useRules from './useRules'

const Field = dynamic(() => import('./Field'))

export function TextArea({
  disabled,
  hasError,
  name,
  onChange,
  onBlur,
  rows = 5,
  placeholder,
  value,
  inputClass = 'p-4',
}) {
  const borderClass = hasError
    ? 'border-danger-600 focus:border-danger-400'
    : 'border-gray-400 focus:border-color1-400'

  return (
    <textarea
      className={`form-textarea w-full rounded border text-gray-800 placeholder-gray-400 focus:outline-none ${inputClass} ${borderClass}`}
      disabled={disabled}
      id={name}
      name={name}
      onBlur={onBlur}
      onChange={onChange}
      placeholder={placeholder}
      rows={rows}
      value={value}
    />
  )
}
TextArea.propTypes = {
  disabled: PropTypes.bool,
  hasError: PropTypes.bool,
  inputClass: PropTypes.string,
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  rows: PropTypes.number,
  rules: PropTypes.object,
  value: PropTypes.string,
}

export function TextAreaField({
  className,
  error,
  disabled,
  help,
  label,
  labelClass,
  labelPrefix,
  name,
  onChange,
  onBlur,
  required,
  rows,
  placeholder,
  value,
  errorMessages,
  inputClass,
}) {
  return (
    <Field
      className={className}
      error={error}
      help={help}
      label={label}
      labelClass={labelClass}
      labelPrefix={labelPrefix}
      name={name}
      required={required}
      errorMessages={errorMessages}
    >
      <TextArea
        inputClass={inputClass}
        disabled={disabled}
        name={name}
        onBlur={onBlur}
        hasError={error !== undefined}
        onChange={onChange}
        placeholder={placeholder}
        rows={rows}
        value={value}
      />
    </Field>
  )
}
TextAreaField.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  error: PropTypes.object,
  help: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.node,
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  rows: PropTypes.number,
  rules: PropTypes.object,
  value: PropTypes.string,
  errorMessages: PropTypes.object,
}

export default function TextAreaController({
  className,
  defaultValue = '',
  disabled,
  help,
  label,
  labelClass,
  labelPrefix,
  name,
  placeholder,
  onChange,
  required,
  rows,
  errorMessages,
  inputClass,
}) {
  const { control } = useFormContext()

  const rules = useRules({
    required,
  })

  const onFieldChange = useCallback(
    field => event => {
      const currentValue = event.currentTarget.value
      field.onChange(event)

      if (typeof onChange === 'function') {
        onChange(currentValue, field)
      }
    },
    [onChange]
  )

  const onFieldBlur = useCallback(
    field => event => {
      field.onBlur(event)
    },
    []
  )

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field, fieldState }) => (
        <TextAreaField
          className={className}
          error={fieldState.error}
          help={help}
          label={label}
          labelClass={labelClass}
          labelPrefix={labelPrefix}
          name={name}
          required={required}
          disabled={disabled}
          onChange={onFieldChange(field)}
          onBlur={onFieldBlur(field)}
          placeholder={placeholder}
          rows={rows}
          value={field.value}
          errorMessages={errorMessages}
          inputClass={inputClass}
        />
      )}
    />
  )
}

TextAreaController.propTypes = {
  className: PropTypes.string,
  defaultValue: PropTypes.string,
  disabled: PropTypes.bool,
  error: PropTypes.object,
  help: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.node,
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  rows: PropTypes.number,
  errorMessages: PropTypes.object,
}
