import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function Label({
  className = '',
  colon,
  htmlFor,
  prefix,
  required,
  text,
}) {
  if (!text) return null

  return (
    <label
      className={`flex cursor-pointer flex-row items-start space-x-2 leading-6 rtl:space-x-reverse ${className}`}
      htmlFor={htmlFor}
    >
      <span>
        {prefix && <span className="mr-1">{prefix}</span>}
        <span className="font-semibold">{text}</span>
        {colon && <span>:</span>}
      </span>

      {required && (
        <span
          className="flex items-center justify-center pt-1"
          title="required"
        >
          <Icon className="text-danger-600 text-xs" name="asterisk" />
        </span>
      )}
    </label>
  )
}
Label.propTypes = {
  className: PropTypes.string,
  colon: PropTypes.bool,
  htmlFor: PropTypes.string,
  prefix: PropTypes.node,
  required: PropTypes.bool,
  text: PropTypes.node,
}
