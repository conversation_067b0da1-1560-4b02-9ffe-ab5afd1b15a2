import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Label = dynamic(() => import('./Label'))

const errorMessages = {
  required: 'errorFieldRequired', // indicates that the input must have a value before the form can be submitted (true)
  maxLength: 'errorToLong', // The maximum length of the value to accept for this input (6).
  minLength: 'errorToShort', // The minimum length of the value to accept for this input (2).
  max: 'errorToBig', // The maximum value to accept for this input (3).
  min: 'errorToSmall', // The minimum value to accept for this input (3).
  // pattern: '', // The regex pattern for the input ('/[A-Za-z]{3}/').
  // validate: '', // A callback function as the argument to validate ((value) => value === 'test')
  error: 'error',
  emailNotValid: 'errorEmailNotValid',
}

function getErrorMessage({ message, type }, customErrorMessages) {
  return message ?? type
    ? customErrorMessages?.[type] ?? errorMessages[type]
    : errorMessages['error']
}

export default function Field({
  children,
  className = '',
  error,
  extrasClass = '',
  help,
  innerClass = '',
  label,
  labelClass = '',
  labelPrefix,
  name,
  required,
  errorMessages: customErrorMessages,
}) {
  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label
          className={labelClass}
          htmlFor={name}
          prefix={labelPrefix}
          required={required}
          text={label}
        />
      )}
      <div className="space-y-1">
        <div className={innerClass}>{children}</div>
        {(help || error) && (
          <div
            className={`flex flex-col space-y-1 px-2 text-sm ${extrasClass}`}
          >
            {help && (
              <p className="italic text-gray-600 dark:text-white">{help}</p>
            )}
            {error && (
              <p className="font-semibold text-danger-600">
                {typeof error === 'object'
                  ? getErrorMessage(error, customErrorMessages)
                  : error}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
Field.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  extrasClass: PropTypes.string,
  help: PropTypes.string,
  innerClass: PropTypes.string,
  name: PropTypes.string,
  label: PropTypes.string,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.node,
  required: PropTypes.bool,
}
