import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { Controller, useFormContext } from 'react-hook-form'

import useRules from './useRules'

const Label = dynamic(() => import('ui/data-entry/Label'))
const Field = dynamic(() => import('./Field'))

export function Checkbox({
  checked,
  className = '',
  id,
  name,
  required,
  label,
  labelClass = '',
  ...props
}) {
  const _id = id || name
  return (
    <div
      className={`flex cursor-pointer flex-row items-start space-x-3 rtl:space-x-reverse ${className}`}
    >
      <input
        id={_id}
        name={name}
        type="checkbox"
        className="form-checkbox mt-1 border-gray-500 text-color1-700"
        checked={checked}
        {...props}
      />
      <Label
        className={`leading-tight ${labelClass}`}
        htmlFor={_id}
        text={label}
        required={required}
      />
    </div>
  )
}
Checkbox.propTypes = {
  checked: PropTypes.bool,
  className: PropTypes.string,
  id: PropTypes.string,
  label: PropTypes.node,
  name: PropTypes.string,
  onChange: PropTypes.func,
  required: PropTypes.bool,
}

export function CheckboxField({
  className,
  disabled,
  error,
  help,
  id,
  label,
  labelClass,
  labelPrefix,
  name,
  onChange,
  required,
  value,
  inputClass,
}) {
  return (
    <Field
      className={className}
      error={error}
      extrasClass="ml-4"
      help={help}
      labelClass={labelClass}
      labelPrefix={labelPrefix}
      name={name}
      required={required}
    >
      <Checkbox
        id={id}
        name={name}
        label={label}
        labelClass={labelClass}
        onChange={onChange}
        value={value}
        checked={!!value}
        disabled={disabled}
        required={required}
        className={inputClass}
      />
    </Field>
  )
}
CheckboxField.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  error: PropTypes.object,
  help: PropTypes.string,
  id: PropTypes.string,
  inputClass: PropTypes.string,
  label: PropTypes.string,
  labelPrefix: PropTypes.node,
  labelClass: PropTypes.string,
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  required: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
}

export default function CheckboxController({
  className,
  defaultValue = '',
  disabled,
  help,
  id,
  inputClass,
  label,
  labelClass,
  labelPrefix,
  name,
  onChange,
  required,
}) {
  const { control } = useFormContext()

  const rules = useRules({ required })

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field, fieldState }) => (
        <CheckboxField
          className={className}
          inputClass={inputClass}
          error={fieldState.error}
          extrasClass="ml-4"
          help={help}
          labelClass={labelClass}
          labelPrefix={labelPrefix}
          id={id}
          name={name}
          label={label}
          required={required}
          onChange={() => {
            field.onChange(!field.value)
            if (typeof onChange === 'function') onChange(!field.value)
          }}
          value={field.value}
          disabled={disabled}
        />
      )}
    />
  )
}
CheckboxController.propTypes = {
  className: PropTypes.string,
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  disabled: PropTypes.bool,
  help: PropTypes.string,
  id: PropTypes.string,
  inputClass: PropTypes.string,
  label: PropTypes.string,
  labelPrefix: PropTypes.node,
  labelClass: PropTypes.string,
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  required: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
}
