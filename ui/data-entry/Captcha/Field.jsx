import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import CaptchaInput from './Input'

const Field = dynamic(() => import('ui/data-entry/Field'))

export default function CaptchaField({
  className = '',
  error,
  help = 'Write the characters you see in the image to prove you are human',
  label = 'Captcha',
  labelClass = '',
  labelPrefix,
  name,
  onChange,
  placeholder,
  required,
  retryLabel,
  errorMessages,
}) {
  return (
    <Field
      className={className}
      error={error}
      errorMessages={errorMessages}
      label={label}
      labelClass={labelClass}
      labelPrefix={labelPrefix}
      name={name}
      required={required}
      help={help}
    >
      <CaptchaInput
        onChange={onChange}
        placeholder={placeholder}
        retryLabel={retryLabel}
      />
    </Field>
  )
}
CaptchaField.propTypes = {
  className: PropTypes.string,
  error: PropTypes.object,
  help: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.node,
  name: PropTypes.string,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  retryLabel: PropTypes.string,
  required: PropTypes.bool,
}
