import React, { useCallback } from 'react'
import PropTypes from 'prop-types'

import { Controller, useFormContext } from 'react-hook-form'

import useRules from '../useRules'
import CaptchaField from './Field'

export default function CaptchaController({
  className = '',
  help,
  label,
  labelClass = '',
  labelPrefix,
  name,
  placeholder,
  onChange,
  required,
  errorMessages,
}) {
  const { control } = useFormContext()

  const rules = useRules({
    required,
  })

  const onFieldChange = useCallback(
    field => valid => {
      field.onChange(valid)

      if (typeof onChange === 'function') {
        onChange(valid, field)
      }
    },
    [onChange]
  )

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={false}
      render={({ field, fieldState }) => (
        <CaptchaField
          className={className}
          error={fieldState.error}
          help={help}
          label={label}
          labelClass={labelClass}
          labelPrefix={labelPrefix}
          name={name}
          required={required}
          onChange={onFieldChange(field)}
          placeholder={placeholder}
          value={field.value}
          errorMessages={errorMessages}
        />
      )}
    />
  )
}
CaptchaController.propTypes = {
  className: PropTypes.string,
  error: PropTypes.object,
  errorMessages: PropTypes.object,
  help: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.node,
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
}
