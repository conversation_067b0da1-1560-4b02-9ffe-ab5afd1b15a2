import getRandomFloat from './getRandomFloat'

/**
 * Calculate luminance of a color
 * @param {string} color - Color in rgb or rgba format
 * @returns {number} - Luminance value
 * @see https://www.w3.org/TR/WCAG20/#relativeluminancedef
 */
function luminance(color) {
  // Extract rgb values from string
  const rgb = color
    .replace(/^(rgb|rgba)\(/, '')
    .replace(/\)$/, '')
    .split(',')
  const lum = []

  // Convert rgb values to 0-1 range
  rgb.forEach((c, i) => {
    // Normalize to 0-1 range
    c = parseFloat(c) / 255

    // Apply sRGB gamma correction to rgb values
    lum[i] = c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
  })
  // Calculate luminance and return
  return lum[0] * 0.2126 + lum[1] * 0.7152 + lum[2] * 0.0722
}

/**
 * Calculate contrast between two colors
 * @param {string} backgroundColor - Background color in rgb or rgba format
 * @param {string} foregroundColor - Foreground color in rgb or rgba format
 * @returns {number} - Contrast value
 * @see https://www.w3.org/TR/WCAG20/#contrast-ratiodef
 */
function contrast(backgroundColor, foregroundColor) {
  const lumA = luminance(backgroundColor) + 0.05
  const lumB = luminance(foregroundColor) + 0.05

  return lumA > lumB ? lumA / lumB : lumB / lumA
}

const defaultRandomColorOptions = {
  backgroundColor: 'rgb(255,255,255)',
  minOpacity: 0.7,
  maxOpacity: 1,
}
/**
 * Returns a random color in rgba format with random opacity, but keep contrast with background getRandomColor
 * @param {object} options - Options object
 * @param {string} options.backgroundColor - Background color to contrast against
 * @param {number} options.minOpacity - Minimum opacity value
 * @param {number} options.maxOpacity - Maximum opacity value
 * @returns {string} - Random color in rgba format
 */
export default function getRandomColor(options = defaultRandomColorOptions) {
  const { backgroundColor, minOpacity, maxOpacity } = {
    ...defaultRandomColorOptions,
    ...options,
  }
  // Generate random color
  const r = Math.floor(getRandomFloat(0, 255))
  const g = Math.floor(getRandomFloat(0, 255))
  const b = Math.floor(getRandomFloat(0, 255))

  // If contrast is too low, generate new color
  if (contrast(backgroundColor, `rgb(${r},${g},${b})`) < 1.5) {
    return getRandomColor({
      backgroundColor,
      minOpacity,
      maxOpacity,
    })
  }

  // Generate random opacity
  const a = getRandomFloat(minOpacity, maxOpacity)

  // Return color in rgba format
  return `rgba(${r},${g},${b},${a})`
}
