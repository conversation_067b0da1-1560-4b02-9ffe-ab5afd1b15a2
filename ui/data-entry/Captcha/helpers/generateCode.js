// Characters to use in captcha
const CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'

/**
 * Generate random code with given characters and length
 * @param {string} chars - Characters to use in code generation
 * @param {number} charsCount - Length of code to generate
 * @returns {string} - Generated code
 */
export default function generateCode(charsCount) {
  const captcha = []
  for (let i = 0; i < charsCount; i += 1) {
    const index = Math.ceil(Math.random() * CHARS.length)
    if (CHARS[index] && captcha.indexOf(CHARS[index]) === -1)
      captcha.push(CHARS[index])
    else i -= 1
  }
  return captcha.join('').toUpperCase()
}
