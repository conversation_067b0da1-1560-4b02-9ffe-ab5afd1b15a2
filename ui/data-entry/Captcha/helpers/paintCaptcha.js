import getRandomColor from './getRandomColor'

const MAX_ANGLE = 30

const FONTS = [
  'Arial',
  'Arial Black',
  'Georgia',
  'Impact',
  'Lucida Console',
  '<PERSON><PERSON><PERSON>',
  'Times New Roman',
  'Trebuchet MS',
  'Verdana',
]

export default function paintCaptcha(canvas, code, width, height) {
  const ctx = canvas.getContext('2d')
  ctx.reset()

  // Draw background color
  ctx.fillStyle = 'white'
  ctx.fillRect(0, 0, width, height)

  const totalLines = 5 + Math.random() * 5 //  Random number of lines between 5 and 10

  // Draw lines
  for (var i = 0; i < totalLines; i++) {
    ctx.beginPath()
    ctx.moveTo(canvas.width * Math.random(), canvas.height * Math.random())
    ctx.lineTo(canvas.width * Math.random(), canvas.height * Math.random())
    ctx.strokeStyle = getRandomColor({
      backgroundColor: 'rgb(255,255,255)',
      minOpacity: 0.2,
      maxOpacity: 0.4,
    })
    ctx.lineWidth = 1 + Math.random() * 3 // Random width between 1 and 4
    ctx.stroke()
  }
  // Draw text characters
  code.split('').forEach((char, i) => {
    const x = 10 + i * 25 // x coordinate of character position in canvas (10px padding) (25px between characters)
    const y = 30 + Math.random() * 12 // y coordinate of character position in canvas (30px padding) (12px random variation between characters)
    const direction = Math.random() > 0.5 ? -1 : 1 // indicate clockwise (1) or counterclockwise (-1) rotation
    const angle = (Math.random() * MAX_ANGLE * direction * Math.PI) / 180 // Random rotation angle between -35 and 35 degrees
    const fontSize = 24 + Math.random() * 12 // Random font size between 24 and 36px
    const fontFamily = FONTS[Math.floor(Math.random() * FONTS.length)] // Random font family from array

    ctx.font = `${fontSize}px ${fontFamily}` // Set font size and family
    ctx.translate(x, y) // Translate to starting point
    ctx.rotate(angle) // Rotate canvas

    ctx.fillStyle = getRandomColor({
      backgroundColor: 'rgb(255,255,255)',
      minOpacity: 0.9,
      maxOpacity: 1,
    }) // Set color
    ctx.fillText(char, 0, 0) // Draw text

    ctx.rotate(-angle) // Rotate back
    ctx.translate(-x, -y) // Translate back
  })
}
