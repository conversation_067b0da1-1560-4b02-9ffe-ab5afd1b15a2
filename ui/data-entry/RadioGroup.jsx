import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { Controller, useFormContext } from 'react-hook-form'

const Field = dynamic(() => import('./Field'))
const Radio = dynamic(() =>
  import('./Radio').then(m => ({ default: m.RadioField }))
)

export default function RadioGroup({
  className,
  disabled,
  defaultValue = '',
  help,
  label,
  labelClass,
  labelPrefix,
  name,
  options = [],
  rules,
}) {
  const { control, formState } = useFormContext()
  const error = formState.errors[name]

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field }) => (
        <Field
          className={className}
          error={error}
          help={help}
          label={label}
          labelClass={labelClass}
          labelPrefix={labelPrefix}
        >
          <div className="space-y-2">
            {options?.map((option, key) => (
              <Radio
                disabled={disabled}
                onChange={field.onChange}
                checked={field.value === option.value}
                {...option}
                key={`${name}-${key}`}
                id={`${name}-${key}`}
                name={name}
              />
            ))}
          </div>
        </Field>
      )}
    />
  )
}

RadioGroup.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  help: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.node,
  name: PropTypes.string.isRequired,
  options: PropTypes.array,
  rules: PropTypes.object,
}
