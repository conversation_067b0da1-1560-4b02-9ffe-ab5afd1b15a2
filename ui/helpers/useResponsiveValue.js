import { useMemo } from 'react'

import clsx from 'clsx'

export function useResponsiveValue(responsiveMap, responsiveValue) {
  return useMemo(() => {
    if (!responsiveValue) {
      return null
    }
    if (typeof responsiveValue !== 'object') {
      return responsiveMap.xs[responsiveValue]
    }

    const classes = Object.keys(responsiveValue).reduce((acc, breakpoint) => {
      const value = responsiveValue[breakpoint]
      return [...acc, responsiveMap[breakpoint][value]]
    }, [])

    return clsx(classes)
  }, [responsiveMap, responsiveValue])
}
