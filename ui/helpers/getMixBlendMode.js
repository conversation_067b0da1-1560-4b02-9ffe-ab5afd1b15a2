const blendModes = [
  'normal', // mix-blend-normal'
  'multiply', // mix-blend-multiply'
  'screen', // mix-blend-screen'
  'overlay', // mix-blend-overlay'
  'darken', // mix-blend-darken'
  'lighten', // mix-blend-lighten'
  'color-dodge', // mix-blend-color-dodge'
  'color-burn', // mix-blend-color-burn'
  'hard-light', // mix-blend-hard-light'
  'soft-light', // mix-blend-soft-light'
  'difference', // mix-blend-difference'
  'exclusion', // mix-blend-exclusion'
  'hue', // mix-blend-hue'
  'saturation', // mix-blend-saturation'
  'color', // mix-blend-color'
  'luminos', // mix-blend-luminos'
]

export function getMixBlendMode(blendMode = 'normal') {
  if (!blendModes.includes(blendMode)) {
    throw new Error(
      `Invalid blend mode: ${blendMode}. Valid blend modes are: ${blendModes.join(
        ', '
      )}`
    )
  }

  return `mix-blend-${blendMode}`
}
