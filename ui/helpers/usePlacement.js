import {
  aligns as baseAligns,
  placements as basePlacements,
} from './getPlacement'
import { useResponsiveValue } from './useResponsiveValue'

export const placementMap = {
  'xs': basePlacements,
  'sm': {
    'top left': 'sm:items-start sm:justify-start',
    'top': 'sm:items-start sm:justify-center',
    'top right': 'sm:items-start sm:justify-end',
    'center left': 'sm:items-center sm:justify-start',
    'center': 'sm:items-center sm:justify-center',
    'center right': 'sm:items-center sm:justify-end',
    'bottom left': 'sm:items-end sm:justify-start',
    'bottom': 'sm:items-end sm:justify-center',
    'bottom right': 'sm:items-end sm:justify-end',
  },
  'md': {
    'top left': 'md:items-start md:justify-start',
    'top': 'md:items-start md:justify-center',
    'top right': 'md:items-start md:justify-end',
    'center left': 'md:items-center md:justify-start',
    'center': 'md:items-center md:justify-center',
    'center right': 'md:items-center md:justify-end',
    'bottom left': 'md:items-end md:justify-start',
    'bottom': 'md:items-end md:justify-center',
    'bottom right': 'md:items-end md:justify-end',
  },
  'lg': {
    'top left': 'lg:items-start lg:justify-start',
    'top': 'lg:items-start lg:justify-center',
    'top right': 'lg:items-start lg:justify-end',
    'center left': 'lg:items-center lg:justify-start',
    'center': 'lg:items-center lg:justify-center',
    'center right': 'lg:items-center lg:justify-end',
    'bottom left': 'lg:items-end lg:justify-start',
    'bottom': 'lg:items-end lg:justify-center',
    'bottom right': 'lg:items-end lg:justify-end',
  },
  'xl': {
    'top left': 'xl:items-start xl:justify-start',
    'top': 'xl:items-start xl:justify-center',
    'top right': 'xl:items-start xl:justify-end',
    'center left': 'xl:items-center xl:justify-start',
    'center': 'xl:items-center xl:justify-center',
    'center right': 'xl:items-center xl:justify-end',
    'bottom left': 'xl:items-end xl:justify-start',
    'bottom': 'xl:items-end xl:justify-center',
    'bottom right': 'xl:items-end xl:justify-end',
  },
  '2xl': {
    'top left': '2xl:items-start 2xl:justify-start',
    'top': '2xl:items-start 2xl:justify-center',
    'top right': '2xl:items-start 2xl:justify-end',
    'center left': '2xl:items-center 2xl:justify-start',
    'center': '2xl:items-center 2xl:justify-center',
    'center right': '2xl:items-center 2xl:justify-end',
    'bottom left': '2xl:items-end 2xl:justify-start',
    'bottom': '2xl:items-end 2xl:justify-center',
    'bottom right': '2xl:items-end 2xl:justify-end',
  },
}

export const aligns = {
  'xs': baseAligns,
  'sm': {
    'top left': 'sm:text-start',
    'top': 'sm:text-center',
    'top right': 'sm:text-end',
    'center left': 'sm:text-start',
    'center': 'sm:text-center',
    'center right': 'sm:text-end',
    'bottom left': 'sm:text-start',
    'bottom': 'sm:text-center',
    'bottom right': 'sm:text-end',
  },
  'md': {
    'top left': 'md:text-start',
    'top': 'md:text-center',
    'top right': 'md:text-end',
    'center left': 'md:text-start',
    'center': 'md:text-center',
    'center right': 'md:text-end',
    'bottom left': 'md:text-start',
    'bottom': 'md:text-center',
    'bottom right': 'md:text-end',
  },
  'lg': {
    'top left': 'lg:text-start',
    'top': 'lg:text-center',
    'top right': 'lg:text-end',
    'center left': 'lg:text-start',
    'center': 'lg:text-center',
    'center right': 'lg:text-end',
    'bottom left': 'lg:text-start',
    'bottom': 'lg:text-center',
    'bottom right': 'lg:text-end',
  },
  'xl': {
    'top left': 'xl:text-start',
    'top': 'xl:text-center',
    'top right': 'xl:text-end',
    'center left': 'xl:text-start',
    'center': 'xl:text-center',
    'center right': 'xl:text-end',
    'bottom left': 'xl:text-start',
    'bottom': 'xl:text-center',
    'bottom right': 'xl:text-end',
  },
  '2xl': {
    'top left': '2xl:text-start',
    'top': '2xl:text-center',
    'top right': '2xl:text-end',
    'center left': '2xl:text-start',
    'center': '2xl:text-center',
    'center right': '2xl:text-end',
    'bottom left': '2xl:text-start',
    'bottom': '2xl:text-center',
    'bottom right': '2xl:text-end',
  },
}

export function usePlacement(placement = { xs: 'center' }) {
  return useResponsiveValue(placementMap, placement)
}

export function useAlignFromPlacement(placement = { xs: 'center' }) {
  return useResponsiveValue(aligns, placement)
}
