import { useMemo } from 'react'

const dividerWidthMap = {
  x: {
    'xs': {
      none: 'divide-none',
      px: 'divide-x',
      sm: 'divide-x-2',
      md: 'divide-x-4',
      lg: 'divide-x-8',
    },
    'sm': {
      none: 'sm:divide-none',
      px: 'sm:divide-x',
      sm: 'sm:divide-x-2',
      md: 'sm:divide-x-4',
      lg: 'sm:divide-x-8',
    },
    'md': {
      none: 'md:divide-none',
      px: 'md:divide-x',
      sm: 'md:divide-x-2',
      md: 'md:divide-x-4',
      lg: 'md:divide-x-8',
    },
    'lg': {
      none: 'lg:divide-none',
      px: 'lg:divide-x',
      sm: 'lg:divide-x-2',
      md: 'lg:divide-x-4',
      lg: 'lg:divide-x-8',
    },
    'xl': {
      none: 'xl:divide-none',
      px: 'xl:divide-x',
      sm: 'xl:divide-x-2',
      md: 'xl:divide-x-4',
      lg: 'xl:divide-x-8',
    },
    '2xl': {
      none: '2xl:divide-none',
      px: '2xl:divide-x',
      sm: '2xl:divide-x-2',
      md: '2xl:divide-x-4',
      lg: '2xl:divide-x-8',
    },
  },
  y: {
    'xs': {
      none: 'divide-none',
      px: 'divide-y',
      sm: 'divide-y-2',
      md: 'divide-y-4',
      lg: 'divide-y-8',
    },
    'sm': {
      none: 'sm:divide-none',
      px: 'sm:divide-y',
      sm: 'sm:divide-y-2',
      md: 'sm:divide-y-4',
      lg: 'sm:divide-y-8',
    },
    'md': {
      none: 'md:divide-none',
      px: 'md:divide-y',
      sm: 'md:divide-y-2',
      md: 'md:divide-y-4',
      lg: 'md:divide-y-8',
    },
    'lg': {
      none: 'lg:divide-none',
      px: 'lg:divide-y',
      sm: 'lg:divide-y-2',
      md: 'lg:divide-y-4',
      lg: 'lg:divide-y-8',
    },
    'xl': {
      none: 'xl:divide-none',
      px: 'xl:divide-y',
      sm: 'xl:divide-y-2',
      md: 'xl:divide-y-4',
      lg: 'xl:divide-y-8',
    },
    '2xl': {
      none: '2xl:divide-none',
      px: '2xl:divide-y',
      sm: '2xl:divide-y-2',
      md: '2xl:divide-y-4',
      lg: '2xl:divide-y-8',
    },
  },
}

const dividerStyleMap = {
  'xs': {
    solid: 'divide-solid',
    dashed: 'divide-dashed',
    dotted: 'divide-dotted',
    double: 'divide-double',
  },
  'sm': {
    solid: 'sm:divide-solid',
    dashed: 'sm:divide-dashed',
    dotted: 'sm:divide-dotted',
    double: 'sm:divide-double',
  },
  'md': {
    solid: 'md:divide-solid',
    dashed: 'md:divide-dashed',
    dotted: 'md:divide-dotted',
    double: 'md:divide-double',
  },
  'lg': {
    solid: 'lg:divide-solid',
    dashed: 'lg:divide-dashed',
    dotted: 'lg:divide-dotted',
    double: 'lg:divide-double',
  },
  'xl': {
    solid: 'xl:divide-solid',
    dashed: 'xl:divide-dashed',
    dotted: 'xl:divide-dotted',
    double: 'xl:divide-double',
  },
  '2xl': {
    solid: '2xl:divide-solid',
    dashed: '2xl:divide-dashed',
    dotted: '2xl:divide-dotted',
    double: '2xl:divide-double',
  },
}

const dividerColorsMap = {
  'xs': {
    'transparent': 'divider-transparent',
    'current': 'divider-current',
    'black': 'divider-black',
    'white': 'divider-white',

    'gray-50': 'divider-gray-50',
    'gray-100': 'divider-gray-100',
    'gray-200': 'divider-gray-200',
    'gray-300': 'divider-gray-300',
    'gray-400': 'divider-gray-400',
    'gray-500': 'divider-gray-500',
    'gray-600': 'divider-gray-600',
    'gray-700': 'divider-gray-700',
    'gray-800': 'divider-gray-800',
    'gray-900': 'divider-gray-900',

    'color1-50': 'divider-color1-50',
    'color1-100': 'divider-color1-100',
    'color1-200': 'divider-color1-200',
    'color1-300': 'divider-color1-300',
    'color1-400': 'divider-color1-400',
    'color1-500': 'divider-color1-500',
    'color1-600': 'divider-color1-600',
    'color1-700': 'divider-color1-700',
    'color1-800': 'divider-color1-800',
    'color1-900': 'divider-color1-900',

    'color2-50': 'divider-color2-50',
    'color2-100': 'divider-color2-100',
    'color2-200': 'divider-color2-200',
    'color2-300': 'divider-color2-300',
    'color2-400': 'divider-color2-400',
    'color2-500': 'divider-color2-500',
    'color2-600': 'divider-color2-600',
    'color2-700': 'divider-color2-700',
    'color2-800': 'divider-color2-800',
    'color2-900': 'divider-color2-900',

    'color3-50': 'divider-color3-50',
    'color3-100': 'divider-color3-100',
    'color3-200': 'divider-color3-200',
    'color3-300': 'divider-color3-300',
    'color3-400': 'divider-color3-400',
    'color3-500': 'divider-color3-500',
    'color3-600': 'divider-color3-600',
    'color3-700': 'divider-color3-700',
    'color3-800': 'divider-color3-800',
    'color3-900': 'divider-color3-900',

    'color4-50': 'divider-color4-50',
    'color4-100': 'divider-color4-100',
    'color4-200': 'divider-color4-200',
    'color4-300': 'divider-color4-300',
    'color4-400': 'divider-color4-400',
    'color4-500': 'divider-color4-500',
    'color4-600': 'divider-color4-600',
    'color4-700': 'divider-color4-700',
    'color4-800': 'divider-color4-800',
    'color4-900': 'divider-color4-900',

    'color5-50': 'divider-color5-50',
    'color5-100': 'divider-color5-100',
    'color5-200': 'divider-color5-200',
    'color5-300': 'divider-color5-300',
    'color5-400': 'divider-color5-400',
    'color5-500': 'divider-color5-500',
    'color5-600': 'divider-color5-600',
    'color5-700': 'divider-color5-700',
    'color5-800': 'divider-color5-800',
    'color5-900': 'divider-color5-900',

    'color6-50': 'divider-color6-50',
    'color6-100': 'divider-color6-100',
    'color6-200': 'divider-color6-200',
    'color6-300': 'divider-color6-300',
    'color6-400': 'divider-color6-400',
    'color6-500': 'divider-color6-500',
    'color6-600': 'divider-color6-600',
    'color6-700': 'divider-color6-700',
    'color6-800': 'divider-color6-800',
    'color6-900': 'divider-color6-900',
  },
  'sm': {
    'transparent': 'sm:divider-transparent',
    'current': 'sm:divider-current',
    'black': 'sm:divider-black',
    'white': 'sm:divider-white',

    'gray-50': 'sm:divider-gray-50',
    'gray-100': 'sm:divider-gray-100',
    'gray-200': 'sm:divider-gray-200',
    'gray-300': 'sm:divider-gray-300',
    'gray-400': 'sm:divider-gray-400',
    'gray-500': 'sm:divider-gray-500',
    'gray-600': 'sm:divider-gray-600',
    'gray-700': 'sm:divider-gray-700',
    'gray-800': 'sm:divider-gray-800',
    'gray-900': 'sm:divider-gray-900',

    'color1-50': 'sm:divider-color1-50',
    'color1-100': 'sm:divider-color1-100',
    'color1-200': 'sm:divider-color1-200',
    'color1-300': 'sm:divider-color1-300',
    'color1-400': 'sm:divider-color1-400',
    'color1-500': 'sm:divider-color1-500',
    'color1-600': 'sm:divider-color1-600',
    'color1-700': 'sm:divider-color1-700',
    'color1-800': 'sm:divider-color1-800',
    'color1-900': 'sm:divider-color1-900',

    'color2-50': 'sm:divider-color2-50',
    'color2-100': 'sm:divider-color2-100',
    'color2-200': 'sm:divider-color2-200',
    'color2-300': 'sm:divider-color2-300',
    'color2-400': 'sm:divider-color2-400',
    'color2-500': 'sm:divider-color2-500',
    'color2-600': 'sm:divider-color2-600',
    'color2-700': 'sm:divider-color2-700',
    'color2-800': 'sm:divider-color2-800',
    'color2-900': 'sm:divider-color2-900',

    'color3-50': 'sm:divider-color3-50',
    'color3-100': 'sm:divider-color3-100',
    'color3-200': 'sm:divider-color3-200',
    'color3-300': 'sm:divider-color3-300',
    'color3-400': 'sm:divider-color3-400',
    'color3-500': 'sm:divider-color3-500',
    'color3-600': 'sm:divider-color3-600',
    'color3-700': 'sm:divider-color3-700',
    'color3-800': 'sm:divider-color3-800',
    'color3-900': 'sm:divider-color3-900',

    'color4-50': 'sm:divider-color4-50',
    'color4-100': 'sm:divider-color4-100',
    'color4-200': 'sm:divider-color4-200',
    'color4-300': 'sm:divider-color4-300',
    'color4-400': 'sm:divider-color4-400',
    'color4-500': 'sm:divider-color4-500',
    'color4-600': 'sm:divider-color4-600',
    'color4-700': 'sm:divider-color4-700',
    'color4-800': 'sm:divider-color4-800',
    'color4-900': 'sm:divider-color4-900',

    'color5-50': 'sm:divider-color5-50',
    'color5-100': 'sm:divider-color5-100',
    'color5-200': 'sm:divider-color5-200',
    'color5-300': 'sm:divider-color5-300',
    'color5-400': 'sm:divider-color5-400',
    'color5-500': 'sm:divider-color5-500',
    'color5-600': 'sm:divider-color5-600',
    'color5-700': 'sm:divider-color5-700',
    'color5-800': 'sm:divider-color5-800',
    'color5-900': 'sm:divider-color5-900',

    'color6-50': 'sm:divider-color6-50',
    'color6-100': 'sm:divider-color6-100',
    'color6-200': 'sm:divider-color6-200',
    'color6-300': 'sm:divider-color6-300',
    'color6-400': 'sm:divider-color6-400',
    'color6-500': 'sm:divider-color6-500',
    'color6-600': 'sm:divider-color6-600',
    'color6-700': 'sm:divider-color6-700',
    'color6-800': 'sm:divider-color6-800',
    'color6-900': 'sm:divider-color6-900',
  },
  'md': {
    'transparent': 'md:divider-transparent',
    'current': 'md:divider-current',
    'black': 'md:divider-black',
    'white': 'md:divider-white',

    'gray-50': 'md:divider-gray-50',
    'gray-100': 'md:divider-gray-100',
    'gray-200': 'md:divider-gray-200',
    'gray-300': 'md:divider-gray-300',
    'gray-400': 'md:divider-gray-400',
    'gray-500': 'md:divider-gray-500',
    'gray-600': 'md:divider-gray-600',
    'gray-700': 'md:divider-gray-700',
    'gray-800': 'md:divider-gray-800',
    'gray-900': 'md:divider-gray-900',

    'color1-50': 'md:divider-color1-50',
    'color1-100': 'md:divider-color1-100',
    'color1-200': 'md:divider-color1-200',
    'color1-300': 'md:divider-color1-300',
    'color1-400': 'md:divider-color1-400',
    'color1-500': 'md:divider-color1-500',
    'color1-600': 'md:divider-color1-600',
    'color1-700': 'md:divider-color1-700',
    'color1-800': 'md:divider-color1-800',
    'color1-900': 'md:divider-color1-900',

    'color2-50': 'md:divider-color2-50',
    'color2-100': 'md:divider-color2-100',
    'color2-200': 'md:divider-color2-200',
    'color2-300': 'md:divider-color2-300',
    'color2-400': 'md:divider-color2-400',
    'color2-500': 'md:divider-color2-500',
    'color2-600': 'md:divider-color2-600',
    'color2-700': 'md:divider-color2-700',
    'color2-800': 'md:divider-color2-800',
    'color2-900': 'md:divider-color2-900',

    'color3-50': 'md:divider-color3-50',
    'color3-100': 'md:divider-color3-100',
    'color3-200': 'md:divider-color3-200',
    'color3-300': 'md:divider-color3-300',
    'color3-400': 'md:divider-color3-400',
    'color3-500': 'md:divider-color3-500',
    'color3-600': 'md:divider-color3-600',
    'color3-700': 'md:divider-color3-700',
    'color3-800': 'md:divider-color3-800',
    'color3-900': 'md:divider-color3-900',

    'color4-50': 'md:divider-color4-50',
    'color4-100': 'md:divider-color4-100',
    'color4-200': 'md:divider-color4-200',
    'color4-300': 'md:divider-color4-300',
    'color4-400': 'md:divider-color4-400',
    'color4-500': 'md:divider-color4-500',
    'color4-600': 'md:divider-color4-600',
    'color4-700': 'md:divider-color4-700',
    'color4-800': 'md:divider-color4-800',
    'color4-900': 'md:divider-color4-900',

    'color5-50': 'md:divider-color5-50',
    'color5-100': 'md:divider-color5-100',
    'color5-200': 'md:divider-color5-200',
    'color5-300': 'md:divider-color5-300',
    'color5-400': 'md:divider-color5-400',
    'color5-500': 'md:divider-color5-500',
    'color5-600': 'md:divider-color5-600',
    'color5-700': 'md:divider-color5-700',
    'color5-800': 'md:divider-color5-800',
    'color5-900': 'md:divider-color5-900',

    'color6-50': 'md:divider-color6-50',
    'color6-100': 'md:divider-color6-100',
    'color6-200': 'md:divider-color6-200',
    'color6-300': 'md:divider-color6-300',
    'color6-400': 'md:divider-color6-400',
    'color6-500': 'md:divider-color6-500',
    'color6-600': 'md:divider-color6-600',
    'color6-700': 'md:divider-color6-700',
    'color6-800': 'md:divider-color6-800',
    'color6-900': 'md:divider-color6-900',
  },
  'lg': {
    'transparent': 'lg:divider-transparent',
    'current': 'lg:divider-current',
    'black': 'lg:divider-black',
    'white': 'lg:divider-white',

    'gray-50': 'lg:divider-gray-50',
    'gray-100': 'lg:divider-gray-100',
    'gray-200': 'lg:divider-gray-200',
    'gray-300': 'lg:divider-gray-300',
    'gray-400': 'lg:divider-gray-400',
    'gray-500': 'lg:divider-gray-500',
    'gray-600': 'lg:divider-gray-600',
    'gray-700': 'lg:divider-gray-700',
    'gray-800': 'lg:divider-gray-800',
    'gray-900': 'lg:divider-gray-900',

    'color1-50': 'lg:divider-color1-50',
    'color1-100': 'lg:divider-color1-100',
    'color1-200': 'lg:divider-color1-200',
    'color1-300': 'lg:divider-color1-300',
    'color1-400': 'lg:divider-color1-400',
    'color1-500': 'lg:divider-color1-500',
    'color1-600': 'lg:divider-color1-600',
    'color1-700': 'lg:divider-color1-700',
    'color1-800': 'lg:divider-color1-800',
    'color1-900': 'lg:divider-color1-900',

    'color2-50': 'lg:divider-color2-50',
    'color2-100': 'lg:divider-color2-100',
    'color2-200': 'lg:divider-color2-200',
    'color2-300': 'lg:divider-color2-300',
    'color2-400': 'lg:divider-color2-400',
    'color2-500': 'lg:divider-color2-500',
    'color2-600': 'lg:divider-color2-600',
    'color2-700': 'lg:divider-color2-700',
    'color2-800': 'lg:divider-color2-800',
    'color2-900': 'lg:divider-color2-900',

    'color3-50': 'lg:divider-color3-50',
    'color3-100': 'lg:divider-color3-100',
    'color3-200': 'lg:divider-color3-200',
    'color3-300': 'lg:divider-color3-300',
    'color3-400': 'lg:divider-color3-400',
    'color3-500': 'lg:divider-color3-500',
    'color3-600': 'lg:divider-color3-600',
    'color3-700': 'lg:divider-color3-700',
    'color3-800': 'lg:divider-color3-800',
    'color3-900': 'lg:divider-color3-900',

    'color4-50': 'lg:divider-color4-50',
    'color4-100': 'lg:divider-color4-100',
    'color4-200': 'lg:divider-color4-200',
    'color4-300': 'lg:divider-color4-300',
    'color4-400': 'lg:divider-color4-400',
    'color4-500': 'lg:divider-color4-500',
    'color4-600': 'lg:divider-color4-600',
    'color4-700': 'lg:divider-color4-700',
    'color4-800': 'lg:divider-color4-800',
    'color4-900': 'lg:divider-color4-900',

    'color5-50': 'lg:divider-color5-50',
    'color5-100': 'lg:divider-color5-100',
    'color5-200': 'lg:divider-color5-200',
    'color5-300': 'lg:divider-color5-300',
    'color5-400': 'lg:divider-color5-400',
    'color5-500': 'lg:divider-color5-500',
    'color5-600': 'lg:divider-color5-600',
    'color5-700': 'lg:divider-color5-700',
    'color5-800': 'lg:divider-color5-800',
    'color5-900': 'lg:divider-color5-900',

    'color6-50': 'lg:divider-color6-50',
    'color6-100': 'lg:divider-color6-100',
    'color6-200': 'lg:divider-color6-200',
    'color6-300': 'lg:divider-color6-300',
    'color6-400': 'lg:divider-color6-400',
    'color6-500': 'lg:divider-color6-500',
    'color6-600': 'lg:divider-color6-600',
    'color6-700': 'lg:divider-color6-700',
    'color6-800': 'lg:divider-color6-800',
    'color6-900': 'lg:divider-color6-900',
  },
  'xl': {
    'transparent': 'xl:divider-transparent',
    'current': 'xl:divider-current',
    'black': 'xl:divider-black',
    'white': 'xl:divider-white',

    'gray-50': 'xl:divider-gray-50',
    'gray-100': 'xl:divider-gray-100',
    'gray-200': 'xl:divider-gray-200',
    'gray-300': 'xl:divider-gray-300',
    'gray-400': 'xl:divider-gray-400',
    'gray-500': 'xl:divider-gray-500',
    'gray-600': 'xl:divider-gray-600',
    'gray-700': 'xl:divider-gray-700',
    'gray-800': 'xl:divider-gray-800',
    'gray-900': 'xl:divider-gray-900',

    'color1-50': 'xl:divider-color1-50',
    'color1-100': 'xl:divider-color1-100',
    'color1-200': 'xl:divider-color1-200',
    'color1-300': 'xl:divider-color1-300',
    'color1-400': 'xl:divider-color1-400',
    'color1-500': 'xl:divider-color1-500',
    'color1-600': 'xl:divider-color1-600',
    'color1-700': 'xl:divider-color1-700',
    'color1-800': 'xl:divider-color1-800',
    'color1-900': 'xl:divider-color1-900',

    'color2-50': 'xl:divider-color2-50',
    'color2-100': 'xl:divider-color2-100',
    'color2-200': 'xl:divider-color2-200',
    'color2-300': 'xl:divider-color2-300',
    'color2-400': 'xl:divider-color2-400',
    'color2-500': 'xl:divider-color2-500',
    'color2-600': 'xl:divider-color2-600',
    'color2-700': 'xl:divider-color2-700',
    'color2-800': 'xl:divider-color2-800',
    'color2-900': 'xl:divider-color2-900',

    'color3-50': 'xl:divider-color3-50',
    'color3-100': 'xl:divider-color3-100',
    'color3-200': 'xl:divider-color3-200',
    'color3-300': 'xl:divider-color3-300',
    'color3-400': 'xl:divider-color3-400',
    'color3-500': 'xl:divider-color3-500',
    'color3-600': 'xl:divider-color3-600',
    'color3-700': 'xl:divider-color3-700',
    'color3-800': 'xl:divider-color3-800',
    'color3-900': 'xl:divider-color3-900',

    'color4-50': 'xl:divider-color4-50',
    'color4-100': 'xl:divider-color4-100',
    'color4-200': 'xl:divider-color4-200',
    'color4-300': 'xl:divider-color4-300',
    'color4-400': 'xl:divider-color4-400',
    'color4-500': 'xl:divider-color4-500',
    'color4-600': 'xl:divider-color4-600',
    'color4-700': 'xl:divider-color4-700',
    'color4-800': 'xl:divider-color4-800',
    'color4-900': 'xl:divider-color4-900',

    'color5-50': 'xl:divider-color5-50',
    'color5-100': 'xl:divider-color5-100',
    'color5-200': 'xl:divider-color5-200',
    'color5-300': 'xl:divider-color5-300',
    'color5-400': 'xl:divider-color5-400',
    'color5-500': 'xl:divider-color5-500',
    'color5-600': 'xl:divider-color5-600',
    'color5-700': 'xl:divider-color5-700',
    'color5-800': 'xl:divider-color5-800',
    'color5-900': 'xl:divider-color5-900',

    'color6-50': 'xl:divider-color6-50',
    'color6-100': 'xl:divider-color6-100',
    'color6-200': 'xl:divider-color6-200',
    'color6-300': 'xl:divider-color6-300',
    'color6-400': 'xl:divider-color6-400',
    'color6-500': 'xl:divider-color6-500',
    'color6-600': 'xl:divider-color6-600',
    'color6-700': 'xl:divider-color6-700',
    'color6-800': 'xl:divider-color6-800',
    'color6-900': 'xl:divider-color6-900',
  },
  '2xl': {
    'transparent': '2xl:divider-transparent',
    'current': '2xl:divider-current',
    'black': '2xl:divider-black',
    'white': '2xl:divider-white',

    'gray-50': '2xl:divider-gray-50',
    'gray-100': '2xl:divider-gray-100',
    'gray-200': '2xl:divider-gray-200',
    'gray-300': '2xl:divider-gray-300',
    'gray-400': '2xl:divider-gray-400',
    'gray-500': '2xl:divider-gray-500',
    'gray-600': '2xl:divider-gray-600',
    'gray-700': '2xl:divider-gray-700',
    'gray-800': '2xl:divider-gray-800',
    'gray-900': '2xl:divider-gray-900',

    'color1-50': '2xl:divider-color1-50',
    'color1-100': '2xl:divider-color1-100',
    'color1-200': '2xl:divider-color1-200',
    'color1-300': '2xl:divider-color1-300',
    'color1-400': '2xl:divider-color1-400',
    'color1-500': '2xl:divider-color1-500',
    'color1-600': '2xl:divider-color1-600',
    'color1-700': '2xl:divider-color1-700',
    'color1-800': '2xl:divider-color1-800',
    'color1-900': '2xl:divider-color1-900',

    'color2-50': '2xl:divider-color2-50',
    'color2-100': '2xl:divider-color2-100',
    'color2-200': '2xl:divider-color2-200',
    'color2-300': '2xl:divider-color2-300',
    'color2-400': '2xl:divider-color2-400',
    'color2-500': '2xl:divider-color2-500',
    'color2-600': '2xl:divider-color2-600',
    'color2-700': '2xl:divider-color2-700',
    'color2-800': '2xl:divider-color2-800',
    'color2-900': '2xl:divider-color2-900',

    'color3-50': '2xl:divider-color3-50',
    'color3-100': '2xl:divider-color3-100',
    'color3-200': '2xl:divider-color3-200',
    'color3-300': '2xl:divider-color3-300',
    'color3-400': '2xl:divider-color3-400',
    'color3-500': '2xl:divider-color3-500',
    'color3-600': '2xl:divider-color3-600',
    'color3-700': '2xl:divider-color3-700',
    'color3-800': '2xl:divider-color3-800',
    'color3-900': '2xl:divider-color3-900',

    'color4-50': '2xl:divider-color4-50',
    'color4-100': '2xl:divider-color4-100',
    'color4-200': '2xl:divider-color4-200',
    'color4-300': '2xl:divider-color4-300',
    'color4-400': '2xl:divider-color4-400',
    'color4-500': '2xl:divider-color4-500',
    'color4-600': '2xl:divider-color4-600',
    'color4-700': '2xl:divider-color4-700',
    'color4-800': '2xl:divider-color4-800',
    'color4-900': '2xl:divider-color4-900',

    'color5-50': '2xl:divider-color5-50',
    'color5-100': '2xl:divider-color5-100',
    'color5-200': '2xl:divider-color5-200',
    'color5-300': '2xl:divider-color5-300',
    'color5-400': '2xl:divider-color5-400',
    'color5-500': '2xl:divider-color5-500',
    'color5-600': '2xl:divider-color5-600',
    'color5-700': '2xl:divider-color5-700',
    'color5-800': '2xl:divider-color5-800',
    'color5-900': '2xl:divider-color5-900',

    'color6-50': '2xl:divider-color6-50',
    'color6-100': '2xl:divider-color6-100',
    'color6-200': '2xl:divider-color6-200',
    'color6-300': '2xl:divider-color6-300',
    'color6-400': '2xl:divider-color6-400',
    'color6-500': '2xl:divider-color6-500',
    'color6-600': '2xl:divider-color6-600',
    'color6-700': '2xl:divider-color6-700',
    'color6-800': '2xl:divider-color6-800',
    'color6-900': '2xl:divider-color6-900',
  },
}

export function useDivider(divider, direction = 'x') {
  return useMemo(() => {
    if (!divider) {
      return
    }
    const classes = []

    // Width
    if (divider.width) {
      for (const [breakpoint, value] of Object.entries(divider.width)) {
        if (dividerWidthMap[direction]?.[breakpoint]?.[value]) {
          classes.push(dividerWidthMap[direction][breakpoint][value])
        }
      }
    }
    // Style
    if (divider.style) {
      for (const [breakpoint, value] of Object.entries(divider.style)) {
        if (dividerStyleMap[breakpoint]?.[value]) {
          classes.push(dividerStyleMap[breakpoint][value])
        }
      }
    }
    // Colour
    if (divider.color) {
      for (const [breakpoint, value] of Object.entries(divider.color)) {
        if (dividerColorsMap[breakpoint]?.[value]) {
          classes.push(dividerColorsMap[breakpoint][value])
        }
      }
    }

    if (classes.length === 0) {
      return
    }

    return classes.join(' ')
  }, [direction, divider])
}

export default useDivider
