import { breakpointKeys } from 'utils/media'
import { isFunction, isObject, isSet } from 'utils/types'

/**
 * @typedef {Record<'xs'|'sm'|'md'|'lg'|'xl'|'2xl', string>} ResponsiveValue a responsive value
 */

/**
 * Returns a responsive class string for a given property, value, and breakpoint.
 * If the value is not defined, returns an empty string.
 * @param {string} property The property to use in the class string (e.g. 'font', 'text', 'gap', etc.)
 * @param {ResponsiveValue|string} value The value to use in the class string (e.g. 'bold', 'xl', 'md', etc.)
 * @param {string[]} valueOptions The possible values for the property (e.g. ['extralight', 'light', 'normal', 'semibold', 'bold', 'black'])
 * @param {string} defaultKey The default value to use if the value is not defined (e.g. 'md')
 * @returns {string} The responsive class string
 */
export function getResponsiveClasses(
  property,
  value,
  valueOptions = [],
  defaultKey
) {
  if (!value) return property && defaultKey ? `${property}-${defaultKey}` : ''

  const classes = []

  // Ensures that the value is an object with breakpoint keys
  const responsiveValue = typeof value === 'string' ? { xs: value } : value

  // Iterates over the breakpoint keys of the responsive value
  for (const [breakpoint, value] of Object.entries(responsiveValue)) {
    const valueOption = Array.isArray(valueOptions)
      ? valueOptions.find(v => v === value)
      : valueOptions[value]

    // Ensures that the breakpoint and value are valid
    if (breakpointKeys.includes(breakpoint) && valueOption) {
      // Adds the responsive class to the array
      classes.push(getResponsiveClass(property, valueOption, breakpoint))
    }
  }

  // Returns the responsive classes as a string separated by spaces
  return classes.join(' ')
}

/**
 * Returns a responsive class string for a given property, value, and breakpoint.
 *
 * @param {string} property The property to use in the class string (e.g. 'font', 'text', 'gap', etc.)
 * @param {string} value The value to use in the class string (e.g. 'bold', 'xl', 'md', etc.)
 * @param {string} breakpoint The breakpoint to use in the class string (e.g. 'sm', 'md', 'lg', etc.)
 * @returns {string} The responsive class string
 * @example
 * getResponsiveClass('font', 'bold', 'md') // => 'md:font-bold'
 */
export function getResponsiveClass(property, value, breakpoint = 'xs') {
  // get the breakpoint prefix (e.g. "sm:"), unless breakpoint is "xs" (moblie-first)
  const breakpointPrefix =
    !breakpoint || breakpoint === 'xs' ? '' : `${breakpoint}:`

  // return the responsive class string
  return `${breakpointPrefix}${property}${isSet(value) ? `-${value}` : ''}`
}

/**
 * Returns a responsive class string for a responsive settings object and a callback function.
 * @param {string} settings The settings to read from
 * @param {function} callback The callback function to use for each breakpoint ( e.g. (value, breakpoint) => `md:font-${value}`)
 * @returns {string} The responsive class string
 */
export function getSideClassesAtBreakpoint(settings = {}, callback = () => {}) {
  if (!isObject(settings) || !isFunction(callback)) return ''

  const classes = []

  for (const breakpoint of breakpointKeys) {
    // Get value at side for the current breakpoint
    const valueAtSide = Object.entries(settings).reduce(
      (acc, [side, responsiveValue]) => {
        if (responsiveValue[breakpoint]) {
          acc[side] = responsiveValue[breakpoint]
        }
        return acc
      },
      {}
    )

    classes.push(callback(valueAtSide, breakpoint))
  }

  return classes.join(' ')
}
