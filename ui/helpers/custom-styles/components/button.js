import { useMemo } from 'react'
import { useSiteDesign } from '../useSiteDesign'
import { isObject } from 'utils/types'
import { getBorderClasses } from 'ui/helpers/useBorder'
import { getPaddingClasses } from 'ui/helpers/usePadding'
import { getTextClasses } from 'ui/helpers/useTextClasses'
import { merge } from 'utils/objects'

/**
 * Hook to get button styles from site design settings
 * @returns {Object} button settings
 * @returns {string} button settings.roundness
 * @returns {string} button settings.shadow
 */
export function useButtonStyles({
  variant = 'base',
  // color = 'gray',
  // size = 'md',
}) {
  const { variants } = useSiteDesign('components.button')

  return useMemo(() => {
    const settings = merge(
      { ...(variants?.base ?? {}) },
      { ...(variants?.[variant] ?? {}) }
    )

    if (!isObject(settings)) return {}

    const classNames = []
    const styles = {}

    const { background, border, text, padding, shadow } = settings

    // Ensure that buttons have at least semibold as weight
    if (text && !text?.weight) {
      text.weight = 'semibold'
    }

    // Get text styles
    if (text) {
      classNames.push(getTextClasses(text))
    }

    // Get shadow styles
    if (shadow) {
      classNames.push(`shadow-${shadow}`)
    }

    // Get background styles
    if (background?.bgColor) {
      classNames.push(`bg-${background.bgColor}`)
    }

    // Get border styles
    classNames.push(getBorderClasses(border))

    // Get padding styles
    classNames.push(getPaddingClasses(padding))

    return {
      className: classNames.join(' '),
      style: styles,
    }
  }, [variants, variant])
}
