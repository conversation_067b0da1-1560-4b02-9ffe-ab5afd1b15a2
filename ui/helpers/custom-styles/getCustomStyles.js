import { getComponentVariables } from './components/getComponentVariables'

export function useSiteStyles({ site }) {
  return getCustomStyles(site)
}

export function getCustomStyles(site) {
  const { design } = site || {}
  if (!design) return {}

  return {
    ...getColorsVariables(design.colors),
    ...getFontsFamilies(design.fonts),
    ...getComponentVariables(design.components),
  }
}

const templateColors = [
  'color1',
  'color2',
  'color3',
  'color4',
  'color5',
  'color6',
  'gray',
  'danger',
  'success',
  'info',
  'warning',
]

export function getColorsVariables(colors = {}) {
  if (Object.keys(colors || {}).length === 0) return {}

  return templateColors.reduce((acc, templateColor) => {
    if (colors?.[templateColor]) {
      acc = {
        ...acc,
        ...setColorsVariables(templateColor, colors?.[templateColor]),
      }
    }
    return acc
  }, {})
}

export function getFontsFamilies(fonts = {}) {
  if (Object.keys(fonts || {}).length === 0) return {}

  return Object.entries(fonts).reduce((acc, [key, font]) => {
    if (key && font?.family) {
      acc[`--font-${key}`] = font.family
    }
    return acc
  }, {})
}

function hexToRgb(hex, separator = ' ') {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)

  if (!result) {
    return null
  }

  const r = parseInt(result[1], 16)
  const g = parseInt(result[2], 16)
  const b = parseInt(result[3], 16)

  return [r, g, b].join(separator)
}

function setColorsVariables(colorName = '', colors = {}) {
  if (Object.keys(colors || {}).length === 0) return {}

  return Object.entries(colors).reduce((acc, [key, color]) => {
    if (key && color) {
      acc[`--${colorName}-${key}`] = hexToRgb(color)
    }
    return acc
  }, {})
}
