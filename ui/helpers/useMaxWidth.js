import { useResponsiveClasses } from './useResponsiveClasses'

const maxWidthOptions = [
  '0', // max-w-0 sm:max-w-0 md:max-w-0 lg:max-w-0 xl:max-w-0 2xl:max-w-0
  'none', // max-w-none sm:max-w-none md:max-w-none lg:max-w-none xl:max-w-none 2xl:max-w-none
  'xs', // max-w-xs sm:max-w-xs md:max-w-xs lg:max-w-xs xl:max-w-xs 2xl:max-w-xs
  'sm', // max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm 2xl:max-w-sm
  'md', // max-w-md sm:max-w-md md:max-w-md lg:max-w-md xl:max-w-md 2xl:max-w-md
  'lg', // max-w-lg sm:max-w-lg md:max-w-lg lg:max-w-lg xl:max-w-lg 2xl:max-w-lg
  'xl', // max-w-xl sm:max-w-xl md:max-w-xl lg:max-w-xl xl:max-w-xl 2xl:max-w-xl
  '2xl', // max-w-2xl sm:max-w-2xl md:max-w-2xl lg:max-w-2xl xl:max-w-2xl 2xl:max-w-2xl
  '3xl', // max-w-3xl sm:max-w-3xl md:max-w-3xl lg:max-w-3xl xl:max-w-3xl 2xl:max-w-3xl
  '4xl', // max-w-4xl sm:max-w-4xl md:max-w-4xl lg:max-w-4xl xl:max-w-4xl 2xl:max-w-4xl
  '5xl', // max-w-5xl sm:max-w-5xl md:max-w-5xl lg:max-w-5xl xl:max-w-5xl 2xl:max-w-5xl
  '6xl', // max-w-6xl sm:max-w-6xl md:max-w-6xl lg:max-w-6xl xl:max-w-6xl 2xl:max-w-6xl
  '7xl', // max-w-7xl sm:max-w-7xl md:max-w-7xl lg:max-w-7xl xl:max-w-7xl 2xl:max-w-7xl
  'full', // max-w-full sm:max-w-full md:max-w-full lg:max-w-full xl:max-w-full 2xl:max-w-full
  'min', // max-w-min sm:max-w-min md:max-w-min lg:max-w-min xl:max-w-min 2xl:max-w-min
  'max', // max-w-max sm:max-w-max md:max-w-max lg:max-w-max xl:max-w-max 2xl:max-w-max
  'fit', // max-w-fit sm:max-w-fit md:max-w-fit lg:max-w-fit xl:max-w-fit 2xl:max-w-fit
  'prose', // max-w-prose sm:max-w-prose md:max-w-prose lg:max-w-prose xl:max-w-prose 2xl:max-w-prose
  'screen-sm', // max-w-screen-sm sm:max-w-screen-sm md:max-w-screen-sm lg:max-w-screen-sm xl:max-w-screen-sm 2xl:max-w-screen-sm
  'screen-md', // max-w-screen-md sm:max-w-screen-md md:max-w-screen-md lg:max-w-screen-md xl:max-w-screen-md 2xl:max-w-screen-md
  'screen-lg', // max-w-screen-lg sm:max-w-screen-lg md:max-w-screen-lg lg:max-w-screen-lg xl:max-w-screen-lg 2xl:max-w-screen-lg
  'screen-xl', // max-w-screen-xl sm:max-w-screen-xl md:max-w-screen-xl lg:max-w-screen-xl xl:max-w-screen-xl 2xl:max-w-screen-xl
  'screen-2xl', // max-w-screen-2xl sm:max-w-screen-2xl md:max-w-screen-2xl lg:max-w-screen-2xl xl:max-w-screen-2xl 2xl:max-w-screen-2xl
]

export function useMaxWidth(maxWidth) {
  return useResponsiveClasses('max-w', maxWidthOptions, maxWidth)
}

export default useMaxWidth
