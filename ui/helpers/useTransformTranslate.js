import { useMemo } from 'react'
import { useValueAtBreakpoint } from './useValueAtBreakpoint'

/**
 * @typedef {Object} TransformTranslate The translation of the element.
 * @property {Object} x The x translation.
 * @property {Object} y The y translation.
 */

/**
 * Get the translation of the element.
 * @param {TransformTranslate} transformTranslate The translation of the element.
 * @returns {Object} The translation of the element.
 */
export function useTransformTranslate(transformTranslate = {}) {
  const translateX = useValueAtBreakpoint(transformTranslate.x, undefined)
  const translateY = useValueAtBreakpoint(transformTranslate.y, undefined)

  return useMemo(() => {
    if (!translateX && !translateY) return {}
    return {
      transform: `translate(${translateX || 0}, ${translateY || 0})`,
    }
  }, [translateX, translateY])
}
