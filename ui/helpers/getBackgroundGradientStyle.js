import { hexToRGB } from 'utils/color'

/**
 *
 * @typedef {Object} ColorStop The color stop of the gradient.
 * @property {String} id The id of the color stop.
 * @property {String} color The color of the color stop.
 * @property {Number} opacity The opacity of the color stop.
 * @property {Number|String} stop The stop of the color stop.
 *
 * @typedef {Object} BgGradient The background gradient of the container.
 * @property {'linear'|'radial"} type The type of the gradient.
 * @property {Number} angle The angle of the gradient.
 * @property {Array<ColorStop>} colorStops The color stops of the gradient.
 */

/**
 * Get the background gradient style.
 * @param {BgGradient} gradientValue The gradient value.
 * @returns {Object} The background gradient style.
 */
export function getBackgroundGradientStyle(gradientValue = {}) {
  const { type, angle, colorStops } = gradientValue

  if (colorStops?.length === 0) {
    return null
  }

  const colorStopsToRender = colorStops?.map(({ color, opacity, stop }) => {
    return `${hexToRGB(color, opacity / 100)} ${stop}%`
  })

  if (type === 'linear') {
    return {
      background: `linear-gradient(${angle}deg, ${colorStopsToRender?.join(
        ', '
      )})`,
    }
  }

  if (type === 'radial') {
    return {
      background: `radial-gradient(${colorStopsToRender?.join(', ')})`,
    }
  }

  return {
    background: 'none',
  }
}
