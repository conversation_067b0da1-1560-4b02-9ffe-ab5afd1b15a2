import { useMemo } from 'react'
import { getResponsiveClasses } from './getResponsiveClasses'

/**
 * @typedef {'xs'|'sm'|'md'|'lg'|'xl'|'2xl'|'3xl'|'4xl'|'5xl'|'6xl'|'7xl'|'8xl'|'9xl'} TextSize A text size type
 */

/**
 * Returns a responsive text size class based on the provided text size and default size.
 *
 * @param {import('./getResponsiveClasses').ResponsiveValue} textSize The text size to apply (e.g., 'xs', 'sm', 'md', etc.) on different breakpoints (i.e. { xs: 'xs', sm: 'sm' }).
 * @param {TextSize} [defaultSize='md'] The default text size to fall back to if the provided text size is not valid.
 * @returns {string} The responsive text size class.
 */
export default function useTextSize(textSize, defaultSize = 'md') {
  return useMemo(
    () => getResponsiveClasses('text', textSize, textSizes, defaultSize),
    [textSize, defaultSize]
  )
}

/**
 * A list of text size classes for responsive design.
 * @type {TextSize[]}
 */
const textSizes = [
  'xs', // text-xs sm:text-xs md:text-xs lg:text-xs xl:text-xs 2xl:text-xs
  'sm', // text-sm sm:text-sm md:text-sm lg:text-sm xl:text-sm 2xl:text-sm
  'md', // text-base sm:text-base md:text-base lg:text-base xl:text-base 2xl:text-base
  'lg', // text-lg sm:text-lg md:text-lg lg:text-lg xl:text-lg 2xl:text-lg
  'xl', // text-xl sm:text-xl md:text-xl lg:text-xl xl:text-xl 2xl:text-xl
  '2xl', // text-2xl sm:text-2xl md:text-2xl lg:text-2xl xl:text-2xl 2xl:text-2xl
  '3xl', // text-3xl sm:text-3xl md:text-3xl lg:text-3xl xl:text-3xl 2xl:text-3xl
  '4xl', // text-4xl sm:text-4xl md:text-4xl lg:text-4xl xl:text-4xl 2xl:text-4xl
  '5xl', // text-5xl sm:text-5xl md:text-5xl lg:text-5xl xl:text-5xl 2xl:text-5xl
  '6xl', // text-6xl sm:text-6xl md:text-6xl lg:text-6xl xl:text-6xl 2xl:text-6xl
  '7xl', // text-7xl sm:text-7xl md:text-7xl lg:text-7xl xl:text-7xl 2xl:text-7xl
  '8xl', // text-8xl sm:text-8xl md:text-8xl lg:text-8xl xl:text-8xl 2xl:text-8xl
  '9xl', // text-9xl sm:text-9xl md:text-9xl lg:text-9xl xl:text-9xl 2xl:text-9xl
]
