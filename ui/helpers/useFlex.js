/**
 * Returns a string with flex classes based on direction, align and justify parameters
 *
 * @param {object} direction config object for flex direction at different endpoints
 * @param {object} align config object for flex aling (align-items) at different endpoints
 * @param {object} justify config object for flex aling (align-items) at different endpoints
 * @returns `string`
 */
export default function useFlex(direction = {}, align = {}, justify = {}) {
  const classes = ['flex']

  for (const [breakpoint, value] of Object.entries(direction)) {
    if (breakpointKeys.includes(breakpoint)) {
      classes.push(flexDirectionMap[breakpoint][value])
    }
  }

  for (const [breakpoint, value] of Object.entries(align)) {
    if (breakpointKeys.includes(breakpoint)) {
      classes.push(flexAlignMap[breakpoint][value])
    }
  }

  for (const [breakpoint, value] of Object.entries(justify)) {
    if (breakpointKeys.includes(breakpoint)) {
      classes.push(flexJustifyMap[breakpoint][value])
    }
  }

  return classes.join(' ')
}

/**
 * Returns flex direction at a given breakpoint
 *
 * @param {object} direction config object for flex direction at different endpoints
 * @param {string} breakpoint the specific breakpoint at which from where to get the direction.
 * @param {string} defaultValue default direction returned when no other is available. Either `x` (horizontal) or `y` (vertical)
 * @returns `string` flex direction (`x` or `y`)
 */
export function findDirectionAtBreakpoint(
  direction = {},
  breakpoint = 'xs',
  defaultValue = 'y'
) {
  // Is there is a direct match, just return it
  if (direction[breakpoint]) return direction[breakpoint]

  // Get position of current breakpoint
  const currentBreakpointIndex = breakpointKeys.indexOf(breakpoint)

  const prevBreakpoints = breakpointKeys
    .slice(0, currentBreakpointIndex) // fetching only the previous, smaller breakpoints,
    .reverse() // in reverse order to get the closests ones to the current one first.

  for (const bp of prevBreakpoints) {
    // if direcction has a value for this breakpoint
    if (direction[bp]) {
      return direction[bp] // just get the closes one and stop looking for values here
    }
  }

  return defaultValue // Otherwise, return the default Value
}

const flexDirectionMap = {
  xs: {
    'x': 'flex-row',
    'y': 'flex-col',
    'x-reverse': 'flex-row-reverse',
    'y-reverse': 'flex-col-reverse',
  },
  sm: {
    'x': 'sm:flex-row',
    'y': 'sm:flex-col',
    'x-reverse': 'sm:flex-row-reverse',
    'y-reverse': 'sm:flex-col-reverse',
  },
  md: {
    'x': 'md:flex-row',
    'y': 'md:flex-col',
    'x-reverse': 'md:flex-row-reverse',
    'y-reverse': 'md:flex-col-reverse',
  },
  lg: {
    'x': 'lg:flex-row',
    'y': 'lg:flex-col',
    'x-reverse': 'lg:flex-row-reverse',
    'y-reverse': 'lg:flex-col-reverse',
  },
  xl: {
    'x': 'xl:flex-row',
    'y': 'xl:flex-col',
    'x-reverse': 'xl:flex-row-reverse',
    'y-reverse': 'xl:flex-col-reverse',
  },
}

const flexAlignMap = {
  xs: {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    baseline: 'items-baseline',
    stretch: 'items-stretch',
  },
  sm: {
    start: 'sm:items-start',
    center: 'sm:items-center',
    end: 'sm:items-end',
    baseline: 'sm:items-baseline',
    stretch: 'sm:items-stretch',
  },
  md: {
    start: 'md:items-start',
    center: 'md:items-center',
    end: 'md:items-end',
    baseline: 'md:items-baseline',
    stretch: 'md:items-stretch',
  },
  lg: {
    start: 'lg:items-start',
    center: 'lg:items-center',
    end: 'lg:items-end',
    baseline: 'lg:items-baseline',
    stretch: 'lg:items-stretch',
  },
  xl: {
    start: 'xl:items-start',
    center: 'xl:items-center',
    end: 'xl:items-end',
    baseline: 'xl:items-baseline',
    stretch: 'xl:items-stretch',
  },
}

const flexJustifyMap = {
  xs: {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  },
  sm: {
    start: 'sm:justify-start',
    center: 'sm:justify-center',
    end: 'sm:justify-end',
    between: 'sm:justify-between',
    around: 'sm:justify-around',
    evenly: 'sm:justify-evenly',
  },
  md: {
    start: 'md:justify-start',
    center: 'md:justify-center',
    end: 'md:justify-end',
    between: 'md:justify-between',
    around: 'md:justify-around',
    evenly: 'md:justify-evenly',
  },
  lg: {
    start: 'lg:justify-start',
    center: 'lg:justify-center',
    end: 'lg:justify-end',
    between: 'lg:justify-between',
    around: 'lg:justify-around',
    evenly: 'lg:justify-evenly',
  },
  xl: {
    start: 'xl:justify-start',
    center: 'xl:justify-center',
    end: 'xl:justify-end',
    between: 'xl:justify-between',
    around: 'xl:justify-around',
    evenly: 'xl:justify-evenly',
  },
}

const breakpointKeys = Object.keys(flexDirectionMap)
