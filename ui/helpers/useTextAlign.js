import { useResponsiveClasses } from './useResponsiveClasses'

/**
 * @typedef {'left'|'center'|'right'|'justify'|'start'|'end'} TextAlign A text alignment type
 */

const alignOptions = {
  center: 'center', // text-center sm:text-center md:text-center lg:text-center xl:text-center 2xl:text-center
  justify: 'justify', // text-justify sm:text-justify md:text-justify lg:text-justify xl:text-justify 2xl:text-justify
  start: 'start', // text-start sm:text-start md:text-start lg:text-start xl:text-start 2xl:text-start
  end: 'end', // text-end sm:text-end md:text-end lg:text-end xl:text-end 2xl:text-end
  left: 'start',
  right: 'end',
}

/**
 * Returns a responsive text alignment class based on the provided alignment.
 *
 * @param {import('./getResponsiveClasses').ResponsiveValue} align The text alignment to apply (e.g., 'left', 'center', etc.) on different breakpoints (i.e. { xs: 'left', sm: 'center' }).
 * @returns {string} The responsive text alignment class.
 */
export function useTextAlign(align = { xs: 'start' }) {
  return useResponsiveClasses(
    'text',
    alignOptions,
    typeof align === 'string' ? { xs: align } : align
  )
}

export default useTextAlign
