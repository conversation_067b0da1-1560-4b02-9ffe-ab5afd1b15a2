import { useResponsiveClasses } from './useResponsiveClasses'

export const gapOptions = {
  0: 0, // gap-0 sm:gap-0 md:gap-0 lg:gap-0 xl:gap-0 2xl:gap-0
  0.5: 0.5, // gap-0.5 sm:gap-0.5 md:gap-0.5 lg:gap-0.5 xl:gap-0.5 2xl:gap-0.5
  1: 1, // gap-1 sm:gap-1 md:gap-1 lg:gap-1 xl:gap-1 2xl:gap-1
  2: 2, // gap-2 sm:gap-2 md:gap-2 lg:gap-2 xl:gap-2 2xl:gap-2
  3: 3, // gap-3 sm:gap-3 md:gap-3 lg:gap-3 xl:gap-3 2xl:gap-3
  4: 4, // gap-4 sm:gap-4 md:gap-4 lg:gap-4 xl:gap-4 2xl:gap-4
  5: 5, // gap-5 sm:gap-5 md:gap-5 lg:gap-5 xl:gap-5 2xl:gap-5
  6: 6, // gap-6 sm:gap-6 md:gap-6 lg:gap-6 xl:gap-6 2xl:gap-6
  7: 7, // gap-7 sm:gap-7 md:gap-7 lg:gap-7 xl:gap-7 2xl:gap-7
  8: 8, // gap-8 sm:gap-8 md:gap-8 lg:gap-8 xl:gap-8 2xl:gap-8
  9: 9, // gap-9 sm:gap-9 md:gap-9 lg:gap-9 xl:gap-9 2xl:gap-9
  10: 10, // gap-10 sm:gap-10 md:gap-10 lg:gap-10 xl:gap-10 2xl:gap-10
  11: 11, // gap-11 sm:gap-11 md:gap-11 lg:gap-11 xl:gap-11 2xl:gap-11
  12: 12, // gap-12 sm:gap-12 md:gap-12 lg:gap-12 xl:gap-12 2xl:gap-12
  14: 14, // gap-14 sm:gap-14 md:gap-14 lg:gap-14 xl:gap-14 2xl:gap-14
  16: 16, // gap-16 sm:gap-16 md:gap-16 lg:gap-16 xl:gap-16 2xl:gap-16
  20: 20, // gap-20 sm:gap-20 md:gap-20 lg:gap-20 xl:gap-20 2xl:gap-20
  24: 24, // gap-24 sm:gap-24 md:gap-24 lg:gap-24 xl:gap-24 2xl:gap-24
  28: 28, // gap-28 sm:gap-28 md:gap-28 lg:gap-28 xl:gap-28 2xl:gap-28
  32: 32, // gap-32 sm:gap-32 md:gap-32 lg:gap-32 xl:gap-32 2xl:gap-32

  // Legacy fallbacks
  none: 0,
  xs: 1,
  sm: 2,
  md: 4,
  lg: 6,
  xl: 12,
}

export default function useGap(gap = { xs: 0 }) {
  return useResponsiveClasses('gap', gapOptions, gap)
}
