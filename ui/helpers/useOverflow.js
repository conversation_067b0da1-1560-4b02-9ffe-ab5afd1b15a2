import { useResponsiveValue } from './useResponsiveValue'

const overflowMap = {
  'xs': {
    'visible': 'overflow-visible',
    'hidden': 'overflow-hidden',
    'scroll': 'overflow-scroll',
    'auto': 'overflow-auto',
    'auto-x': 'overflow-x-auto',
    'hidden-x': 'overflow-x-hidden',
    'scroll-x': 'overflow-x-scroll',
    'visible-x': 'overflow-x-visible',
    'auto-y': 'overflow-y-auto',
    'hidden-y': 'overflow-y-hidden',
    'scroll-y': 'overflow-y-scroll',
    'visible-y': 'overflow-y-visible',
  },
  'sm': {
    'visible': 'sm:overflow-visible',
    'hidden': 'sm:overflow-hidden',
    'scroll': 'sm:overflow-scroll',
    'auto': 'sm:overflow-auto',
    'auto-x': 'sm:overflow-x-auto',
    'hidden-x': 'sm:overflow-x-hidden',
    'scroll-x': 'sm:overflow-x-scroll',
    'visible-x': 'sm:overflow-x-visible',
    'auto-y': 'sm:overflow-y-auto',
    'hidden-y': 'sm:overflow-y-hidden',
    'scroll-y': 'sm:overflow-y-scroll',
    'visible-y': 'sm:overflow-y-visible',
  },
  'md': {
    'visible': 'md:overflow-visible',
    'hidden': 'md:overflow-hidden',
    'scroll': 'md:overflow-scroll',
    'auto': 'md:overflow-auto',
    'auto-x': 'md:overflow-x-auto',
    'hidden-x': 'md:overflow-x-hidden',
    'scroll-x': 'md:overflow-x-scroll',
    'visible-x': 'md:overflow-x-visible',
    'auto-y': 'md:overflow-y-auto',
    'hidden-y': 'md:overflow-y-hidden',
    'scroll-y': 'md:overflow-y-scroll',
    'visible-y': 'md:overflow-y-visible',
  },
  'lg': {
    'visible': 'lg:overflow-visible',
    'hidden': 'lg:overflow-hidden',
    'scroll': 'lg:overflow-scroll',
    'auto': 'lg:overflow-auto',
    'auto-x': 'lg:overflow-x-auto',
    'hidden-x': 'lg:overflow-x-hidden',
    'scroll-x': 'lg:overflow-x-scroll',
    'visible-x': 'lg:overflow-x-visible',
    'auto-y': 'lg:overflow-y-auto',
    'hidden-y': 'lg:overflow-y-hidden',
    'scroll-y': 'lg:overflow-y-scroll',
    'visible-y': 'lg:overflow-y-visible',
  },
  'xl': {
    'visible': 'xl:overflow-visible',
    'hidden': 'xl:overflow-hidden',
    'scroll': 'xl:overflow-scroll',
    'auto': 'xl:overflow-auto',
    'auto-x': 'xl:overflow-x-auto',
    'hidden-x': 'xl:overflow-x-hidden',
    'scroll-x': 'xl:overflow-x-scroll',
    'visible-x': 'xl:overflow-x-visible',
    'auto-y': 'xl:overflow-y-auto',
    'hidden-y': 'xl:overflow-y-hidden',
    'scroll-y': 'xl:overflow-y-scroll',
    'visible-y': 'xl:overflow-y-visible',
  },
  '2xl': {
    'visible': '2xl:overflow-visible',
    'hidden': '2xl:overflow-hidden',
    'scroll': '2xl:overflow-scroll',
    'auto': '2xl:overflow-auto',
    'auto-x': '2xl:overflow-x-auto',
    'hidden-x': '2xl:overflow-x-hidden',
    'scroll-x': '2xl:overflow-x-scroll',
    'visible-x': '2xl:overflow-x-visible',
    'auto-y': '2xl:overflow-y-auto',
    'hidden-y': '2xl:overflow-y-hidden',
    'scroll-y': '2xl:overflow-y-scroll',
    'visible-y': '2xl:overflow-y-visible',
  },
}

export function useOverflow(overflow = { xs: 'visible' }) {
  return useResponsiveValue(overflowMap, overflow)
}

export default useOverflow
