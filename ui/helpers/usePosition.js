import { useResponsiveValue } from './useResponsiveValue'

const positionsMap = {
  'xs': {
    static: 'static',
    relative: 'relative',
    absolute: 'absolute',
    fixed: 'fixed',
    sticky: 'sticky',
  },
  'sm': {
    static: 'sm:static',
    relative: 'sm:relative',
    absolute: 'sm:absolute',
    fixed: 'sm:fixed',
    sticky: 'sm:sticky',
  },
  'md': {
    static: 'md:static',
    relative: 'md:relative',
    absolute: 'md:absolute',
    fixed: 'md:fixed',
    sticky: 'md:sticky',
  },
  'lg': {
    static: 'lg:static',
    relative: 'lg:relative',
    absolute: 'lg:absolute',
    fixed: 'lg:fixed',
    sticky: 'lg:sticky',
  },
  'xl': {
    static: 'xl:static',
    relative: 'xl:relative',
    absolute: 'xl:absolute',
    fixed: 'xl:fixed',
    sticky: 'xl:sticky',
  },
  '2xl': {
    static: '2xl:static',
    relative: '2xl:relative',
    absolute: '2xl:absolute',
    fixed: '2xl:fixed',
    sticky: '2xl:sticky',
  },
}

export function usePosition(position = { xs: 'static' }) {
  return useResponsiveValue(positionsMap, position)
}

export default usePosition
