import { getTextColor } from './getColor'
import { useFontFamily } from './useFontFamily'
import useFontWeight from './useFontWeight'
import useTextSize from './useTextSize'

export function useTextFieldWithModifiers(field = {}) {
  const syles = {}
  const classNames = []

  const textSizeClasses = useTextSize(field.textSize)
  const fontFamilyClass = useFontFamily(field.fontFamily)
  const fontWeightClasses = useFontWeight(field.fontWeight)

  if (field.color) {
    classNames.push(getTextColor(field.color))
  }

  if (field.textCase) {
    classNames.push(field.textCase)
  }

  if (field.fontFamily) {
    classNames.push(fontFamilyClass)
  }

  if (field.fontWeight && fontWeightClasses) {
    classNames.push(fontWeightClasses)
  }

  if (field.textSize && textSizeClasses) {
    classNames.push(textSizeClasses)
  }

  return [field.value, classNames.join(' '), syles]
}
