import { useMemo } from 'react'
import { gridOrder } from './useGridItem'

export function useFlexClasses(props = {}, parent) {
  return useMemo(() => {
    const { order } = props

    if (parent?.displayName !== 'Box') {
      return ''
    }

    const displayedOrder = !order || typeof order !== 'object' ? {} : order

    const orderClasses = Object.keys(displayedOrder).reduce(
      (acc, breakpoint) => {
        const value = order[breakpoint]
        return [...acc, gridOrder[breakpoint][value]]
      },
      []
    )

    return orderClasses.join(' ')
  }, [props, parent])
}
