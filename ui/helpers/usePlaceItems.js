import { useResponsiveClasses } from './useResponsiveClasses'

const placeItemsClasses = {
  start: 'start', // place-items-start sm:place-items-start md:place-items-start lg:place-items-start xl:place-items-start 2xl:place-items-start 3xl:place-items-start
  center: 'center', // place-items-center sm:place-items-center md:place-items-center lg:place-items-center xl:place-items-center 2xl:place-items-center 3xl:place-items-center
  end: 'end', // place-items-end sm:place-items-end md:place-items-end lg:place-items-end xl:place-items-end 2xl:place-items-end 3xl:place-items-end
  stretch: 'stretch', // place-items-stretch sm:place-items-stretch md:place-items-stretch lg:place-items-stretch xl:place-items-stretch 2xl:place-items-stretch 3xl:place-items-stretch
}

export default function usePlaceItems(placeItems = { xs: 'stretch' }) {
  return useResponsiveClasses('place-items', placeItemsClasses, placeItems)
}
