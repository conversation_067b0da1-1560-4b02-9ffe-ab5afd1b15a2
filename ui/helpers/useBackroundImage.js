import { useMemo } from 'react'
import { useImageUrl } from 'ui/data-display/Image'

function getBackgroundImageFormat(image) {
  return function ({ width }) {
    if (!image) return

    const aspectRatio = image.width / image.height
    const height = width / aspectRatio

    if (height < image.height && aspectRatio > 1 && image.height < width) {
      return `h:${image.height},q:80`
    }

    return `w:${width},q:80`
  }
}

export default function useBackgroundImage(
  image,
  position = 'center',
  width = 1200,
  quality
) {
  const bgImageUrl = useImageUrl(
    image,
    width,
    quality,
    getBackgroundImageFormat(image)
  )

  if (!positions.includes(position)) position = 'center'

  return useMemo(() => {
    if (!bgImageUrl) return null
    return {
      backgroundImage: `url(${bgImageUrl})`,
      backgroundPosition: position,
    }
  }, [bgImageUrl, position])
}

const positions = [
  'top left',
  'top',
  'top right',
  'center left',
  'center',
  'center right',
  'bottom left',
  'bottom',
  'bottom right',
]
