import { useMemo } from 'react'
import { getBorderRadiusClasses } from './useBorderRadius'

const borderWidthMap = {
  all: {
    'xs': {
      none: 'border-0',
      px: 'border',
      xs: 'border',
      sm: 'border-2',
      md: 'border-4',
      lg: 'border-8',
    },
    'sm': {
      none: 'sm:border-0',
      px: 'sm:border',
      xs: 'sm:border',
      sm: 'sm:border-2',
      md: 'sm:border-4',
      lg: 'sm:border-8',
    },
    'md': {
      none: 'md:border-0',
      px: 'md:border',
      xs: 'md:border',
      sm: 'md:border-2',
      md: 'md:border-4',
      lg: 'md:border-8',
    },
    'lg': {
      none: 'lg:border-0',
      px: 'lg:border',
      xs: 'lg:border',
      sm: 'lg:border-2',
      md: 'lg:border-4',
      lg: 'lg:border-8',
    },
    'xl': {
      none: 'xl:border-0',
      px: 'xl:border',
      xs: 'xl:border',
      sm: 'xl:border-2',
      md: 'xl:border-4',
      lg: 'xl:border-8',
    },
    '2xl': {
      none: '2xl:border-0',
      px: '2xl:border',
      xs: '2xl:border',
      sm: '2xl:border-2',
      md: '2xl:border-4',
      lg: '2xl:border-8',
    },
  },
  t: {
    'xs': {
      none: 'border-t-0',
      px: 'border-t',
      xs: 'border-t',
      sm: 'border-t-2',
      md: 'border-t-4',
      lg: 'border-t-8',
    },
    'sm': {
      none: 'sm:border-t-0',
      px: 'sm:border-t',
      xs: 'sm:border-t',
      sm: 'sm:border-t-2',
      md: 'sm:border-t-4',
      lg: 'sm:border-t-8',
    },
    'md': {
      none: 'md:border-t-0',
      px: 'md:border-t',
      xs: 'md:border-t',
      sm: 'md:border-t-2',
      md: 'md:border-t-4',
      lg: 'md:border-t-8',
    },
    'lg': {
      none: 'lg:border-t-0',
      px: 'lg:border-t',
      xs: 'lg:border-t',
      sm: 'lg:border-t-2',
      md: 'lg:border-t-4',
      lg: 'lg:border-t-8',
    },
    'xl': {
      none: 'xl:border-t-0',
      px: 'xl:border-t',
      xs: 'xl:border-t',
      sm: 'xl:border-t-2',
      md: 'xl:border-t-4',
      lg: 'xl:border-t-8',
    },
    '2xl': {
      none: '2xl:border-t-0',
      px: '2xl:border-t',
      xs: '2xl:border-t',
      sm: '2xl:border-t-2',
      md: '2xl:border-t-4',
      lg: '2xl:border-t-8',
    },
  },
  r: {
    'xs': {
      none: 'border-r-0',
      px: 'border-r',
      xs: 'border-r',
      sm: 'border-r-2',
      md: 'border-r-4',
      lg: 'border-r-8',
    },
    'sm': {
      none: 'sm:border-r-0',
      px: 'sm:border-r',
      xs: 'sm:border-r',
      sm: 'sm:border-r-2',
      md: 'sm:border-r-4',
      lg: 'sm:border-r-8',
    },
    'md': {
      none: 'md:border-r-0',
      px: 'md:border-r',
      xs: 'md:border-r',
      sm: 'md:border-r-2',
      md: 'md:border-r-4',
      lg: 'md:border-r-8',
    },
    'lg': {
      none: 'lg:border-r-0',
      px: 'lg:border-r',
      xs: 'lg:border-r',
      sm: 'lg:border-r-2',
      md: 'lg:border-r-4',
      lg: 'lg:border-r-8',
    },
    'xl': {
      none: 'xl:border-r-0',
      px: 'xl:border-r',
      xs: 'xl:border-r',
      sm: 'xl:border-r-2',
      md: 'xl:border-r-4',
      lg: 'xl:border-r-8',
    },
    '2xl': {
      none: '2xl:border-r-0',
      px: '2xl:border-r',
      xs: '2xl:border-r',
      sm: '2xl:border-r-2',
      md: '2xl:border-r-4',
      lg: '2xl:border-r-8',
    },
  },
  b: {
    'xs': {
      none: 'border-b-0',
      px: 'border-b',
      xs: 'border-b',
      sm: 'border-b-2',
      md: 'border-b-4',
      lg: 'border-b-8',
    },
    'sm': {
      none: 'sm:border-b-0',
      px: 'sm:border-b',
      xs: 'sm:border-b',
      sm: 'sm:border-b-2',
      md: 'sm:border-b-4',
      lg: 'sm:border-b-8',
    },
    'md': {
      none: 'md:border-b-0',
      px: 'md:border-b',
      xs: 'md:border-b',
      sm: 'md:border-b-2',
      md: 'md:border-b-4',
      lg: 'md:border-b-8',
    },
    'lg': {
      none: 'lg:border-b-0',
      px: 'lg:border-b',
      xs: 'lg:border-b',
      sm: 'lg:border-b-2',
      md: 'lg:border-b-4',
      lg: 'lg:border-b-8',
    },
    'xl': {
      none: 'xl:border-b-0',
      px: 'xl:border-b',
      xs: 'xl:border-b',
      sm: 'xl:border-b-2',
      md: 'xl:border-b-4',
      lg: 'xl:border-b-8',
    },
    '2xl': {
      none: '2xl:border-b-0',
      px: '2xl:border-b',
      xs: '2xl:border-b',
      sm: '2xl:border-b-2',
      md: '2xl:border-b-4',
      lg: '2xl:border-b-8',
    },
  },
  l: {
    'xs': {
      none: 'border-l-0',
      px: 'border-l',
      xs: 'border-l',
      sm: 'border-l-2',
      md: 'border-l-4',
      lg: 'border-l-8',
    },
    'sm': {
      none: 'sm:border-l-0',
      px: 'sm:border-l',
      xs: 'sm:border-l',
      sm: 'sm:border-l-2',
      md: 'sm:border-l-4',
      lg: 'sm:border-l-8',
    },
    'md': {
      none: 'md:border-l-0',
      px: 'md:border-l',
      xs: 'md:border-l',
      sm: 'md:border-l-2',
      md: 'md:border-l-4',
      lg: 'md:border-l-8',
    },
    'lg': {
      none: 'lg:border-l-0',
      px: 'lg:border-l',
      xs: 'lg:border-l',
      sm: 'lg:border-l-2',
      md: 'lg:border-l-4',
      lg: 'lg:border-l-8',
    },
    'xl': {
      none: 'xl:border-l-0',
      px: 'xl:border-l',
      xs: 'xl:border-l',
      sm: 'xl:border-l-2',
      md: 'xl:border-l-4',
      lg: 'xl:border-l-8',
    },
    '2xl': {
      none: '2xl:border-l-0',
      px: '2xl:border-l',
      xs: '2xl:border-l',
      sm: '2xl:border-l-2',
      md: '2xl:border-l-4',
      lg: '2xl:border-l-8',
    },
  },
}
const borderStyleMap = {
  'xs': {
    solid: 'border-solid',
    dashed: 'border-dashed',
    dotted: 'border-dotted',
    double: 'border-double',
  },
  'sm': {
    solid: 'sm:border-solid',
    dashed: 'sm:border-dashed',
    dotted: 'sm:border-dotted',
    double: 'sm:border-double',
  },
  'md': {
    solid: 'md:border-solid',
    dashed: 'md:border-dashed',
    dotted: 'md:border-dotted',
    double: 'md:border-double',
  },
  'lg': {
    solid: 'lg:border-solid',
    dashed: 'lg:border-dashed',
    dotted: 'lg:border-dotted',
    double: 'lg:border-double',
  },
  'xl': {
    solid: 'xl:border-solid',
    dashed: 'xl:border-dashed',
    dotted: 'xl:border-dotted',
    double: 'xl:border-double',
  },
  '2xl': {
    solid: '2xl:border-solid',
    dashed: '2xl:border-dashed',
    dotted: '2xl:border-dotted',
    double: '2xl:border-double',
  },
}
const borderColorsMap = {
  'xs': {
    'transparent': 'border-transparent',
    'current': 'border-current',
    'black': 'border-black',
    'white': 'border-white',

    'gray-50': 'border-gray-50',
    'gray-100': 'border-gray-100',
    'gray-200': 'border-gray-200',
    'gray-300': 'border-gray-300',
    'gray-400': 'border-gray-400',
    'gray-500': 'border-gray-500',
    'gray-600': 'border-gray-600',
    'gray-700': 'border-gray-700',
    'gray-800': 'border-gray-800',
    'gray-900': 'border-gray-900',

    'color1-50': 'border-color1-50',
    'color1-100': 'border-color1-100',
    'color1-200': 'border-color1-200',
    'color1-300': 'border-color1-300',
    'color1-400': 'border-color1-400',
    'color1-500': 'border-color1-500',
    'color1-600': 'border-color1-600',
    'color1-700': 'border-color1-700',
    'color1-800': 'border-color1-800',
    'color1-900': 'border-color1-900',

    'color2-50': 'border-color2-50',
    'color2-100': 'border-color2-100',
    'color2-200': 'border-color2-200',
    'color2-300': 'border-color2-300',
    'color2-400': 'border-color2-400',
    'color2-500': 'border-color2-500',
    'color2-600': 'border-color2-600',
    'color2-700': 'border-color2-700',
    'color2-800': 'border-color2-800',
    'color2-900': 'border-color2-900',

    'color3-50': 'border-color3-50',
    'color3-100': 'border-color3-100',
    'color3-200': 'border-color3-200',
    'color3-300': 'border-color3-300',
    'color3-400': 'border-color3-400',
    'color3-500': 'border-color3-500',
    'color3-600': 'border-color3-600',
    'color3-700': 'border-color3-700',
    'color3-800': 'border-color3-800',
    'color3-900': 'border-color3-900',

    'color4-50': 'border-color4-50',
    'color4-100': 'border-color4-100',
    'color4-200': 'border-color4-200',
    'color4-300': 'border-color4-300',
    'color4-400': 'border-color4-400',
    'color4-500': 'border-color4-500',
    'color4-600': 'border-color4-600',
    'color4-700': 'border-color4-700',
    'color4-800': 'border-color4-800',
    'color4-900': 'border-color4-900',

    'color5-50': 'border-color5-50',
    'color5-100': 'border-color5-100',
    'color5-200': 'border-color5-200',
    'color5-300': 'border-color5-300',
    'color5-400': 'border-color5-400',
    'color5-500': 'border-color5-500',
    'color5-600': 'border-color5-600',
    'color5-700': 'border-color5-700',
    'color5-800': 'border-color5-800',
    'color5-900': 'border-color5-900',

    'color6-50': 'border-color6-50',
    'color6-100': 'border-color6-100',
    'color6-200': 'border-color6-200',
    'color6-300': 'border-color6-300',
    'color6-400': 'border-color6-400',
    'color6-500': 'border-color6-500',
    'color6-600': 'border-color6-600',
    'color6-700': 'border-color6-700',
    'color6-800': 'border-color6-800',
    'color6-900': 'border-color6-900',
  },
  'sm': {
    'transparent': 'sm:border-transparent',
    'current': 'sm:border-current',
    'black': 'sm:border-black',
    'white': 'sm:border-white',

    'gray-50': 'sm:border-gray-50',
    'gray-100': 'sm:border-gray-100',
    'gray-200': 'sm:border-gray-200',
    'gray-300': 'sm:border-gray-300',
    'gray-400': 'sm:border-gray-400',
    'gray-500': 'sm:border-gray-500',
    'gray-600': 'sm:border-gray-600',
    'gray-700': 'sm:border-gray-700',
    'gray-800': 'sm:border-gray-800',
    'gray-900': 'sm:border-gray-900',

    'color1-50': 'sm:border-color1-50',
    'color1-100': 'sm:border-color1-100',
    'color1-200': 'sm:border-color1-200',
    'color1-300': 'sm:border-color1-300',
    'color1-400': 'sm:border-color1-400',
    'color1-500': 'sm:border-color1-500',
    'color1-600': 'sm:border-color1-600',
    'color1-700': 'sm:border-color1-700',
    'color1-800': 'sm:border-color1-800',
    'color1-900': 'sm:border-color1-900',

    'color2-50': 'sm:border-color2-50',
    'color2-100': 'sm:border-color2-100',
    'color2-200': 'sm:border-color2-200',
    'color2-300': 'sm:border-color2-300',
    'color2-400': 'sm:border-color2-400',
    'color2-500': 'sm:border-color2-500',
    'color2-600': 'sm:border-color2-600',
    'color2-700': 'sm:border-color2-700',
    'color2-800': 'sm:border-color2-800',
    'color2-900': 'sm:border-color2-900',

    'color3-50': 'sm:border-color3-50',
    'color3-100': 'sm:border-color3-100',
    'color3-200': 'sm:border-color3-200',
    'color3-300': 'sm:border-color3-300',
    'color3-400': 'sm:border-color3-400',
    'color3-500': 'sm:border-color3-500',
    'color3-600': 'sm:border-color3-600',
    'color3-700': 'sm:border-color3-700',
    'color3-800': 'sm:border-color3-800',
    'color3-900': 'sm:border-color3-900',

    'color4-50': 'sm:border-color4-50',
    'color4-100': 'sm:border-color4-100',
    'color4-200': 'sm:border-color4-200',
    'color4-300': 'sm:border-color4-300',
    'color4-400': 'sm:border-color4-400',
    'color4-500': 'sm:border-color4-500',
    'color4-600': 'sm:border-color4-600',
    'color4-700': 'sm:border-color4-700',
    'color4-800': 'sm:border-color4-800',
    'color4-900': 'sm:border-color4-900',

    'color5-50': 'sm:border-color5-50',
    'color5-100': 'sm:border-color5-100',
    'color5-200': 'sm:border-color5-200',
    'color5-300': 'sm:border-color5-300',
    'color5-400': 'sm:border-color5-400',
    'color5-500': 'sm:border-color5-500',
    'color5-600': 'sm:border-color5-600',
    'color5-700': 'sm:border-color5-700',
    'color5-800': 'sm:border-color5-800',
    'color5-900': 'sm:border-color5-900',

    'color6-50': 'sm:border-color6-50',
    'color6-100': 'sm:border-color6-100',
    'color6-200': 'sm:border-color6-200',
    'color6-300': 'sm:border-color6-300',
    'color6-400': 'sm:border-color6-400',
    'color6-500': 'sm:border-color6-500',
    'color6-600': 'sm:border-color6-600',
    'color6-700': 'sm:border-color6-700',
    'color6-800': 'sm:border-color6-800',
    'color6-900': 'sm:border-color6-900',
  },
  'md': {
    'transparent': 'md:border-transparent',
    'current': 'md:border-current',
    'black': 'md:border-black',
    'white': 'md:border-white',

    'gray-50': 'md:border-gray-50',
    'gray-100': 'md:border-gray-100',
    'gray-200': 'md:border-gray-200',
    'gray-300': 'md:border-gray-300',
    'gray-400': 'md:border-gray-400',
    'gray-500': 'md:border-gray-500',
    'gray-600': 'md:border-gray-600',
    'gray-700': 'md:border-gray-700',
    'gray-800': 'md:border-gray-800',
    'gray-900': 'md:border-gray-900',

    'color1-50': 'md:border-color1-50',
    'color1-100': 'md:border-color1-100',
    'color1-200': 'md:border-color1-200',
    'color1-300': 'md:border-color1-300',
    'color1-400': 'md:border-color1-400',
    'color1-500': 'md:border-color1-500',
    'color1-600': 'md:border-color1-600',
    'color1-700': 'md:border-color1-700',
    'color1-800': 'md:border-color1-800',
    'color1-900': 'md:border-color1-900',

    'color2-50': 'md:border-color2-50',
    'color2-100': 'md:border-color2-100',
    'color2-200': 'md:border-color2-200',
    'color2-300': 'md:border-color2-300',
    'color2-400': 'md:border-color2-400',
    'color2-500': 'md:border-color2-500',
    'color2-600': 'md:border-color2-600',
    'color2-700': 'md:border-color2-700',
    'color2-800': 'md:border-color2-800',
    'color2-900': 'md:border-color2-900',

    'color3-50': 'md:border-color3-50',
    'color3-100': 'md:border-color3-100',
    'color3-200': 'md:border-color3-200',
    'color3-300': 'md:border-color3-300',
    'color3-400': 'md:border-color3-400',
    'color3-500': 'md:border-color3-500',
    'color3-600': 'md:border-color3-600',
    'color3-700': 'md:border-color3-700',
    'color3-800': 'md:border-color3-800',
    'color3-900': 'md:border-color3-900',

    'color4-50': 'md:border-color4-50',
    'color4-100': 'md:border-color4-100',
    'color4-200': 'md:border-color4-200',
    'color4-300': 'md:border-color4-300',
    'color4-400': 'md:border-color4-400',
    'color4-500': 'md:border-color4-500',
    'color4-600': 'md:border-color4-600',
    'color4-700': 'md:border-color4-700',
    'color4-800': 'md:border-color4-800',
    'color4-900': 'md:border-color4-900',

    'color5-50': 'md:border-color5-50',
    'color5-100': 'md:border-color5-100',
    'color5-200': 'md:border-color5-200',
    'color5-300': 'md:border-color5-300',
    'color5-400': 'md:border-color5-400',
    'color5-500': 'md:border-color5-500',
    'color5-600': 'md:border-color5-600',
    'color5-700': 'md:border-color5-700',
    'color5-800': 'md:border-color5-800',
    'color5-900': 'md:border-color5-900',

    'color6-50': 'md:border-color6-50',
    'color6-100': 'md:border-color6-100',
    'color6-200': 'md:border-color6-200',
    'color6-300': 'md:border-color6-300',
    'color6-400': 'md:border-color6-400',
    'color6-500': 'md:border-color6-500',
    'color6-600': 'md:border-color6-600',
    'color6-700': 'md:border-color6-700',
    'color6-800': 'md:border-color6-800',
    'color6-900': 'md:border-color6-900',
  },
  'lg': {
    'transparent': 'lg:border-transparent',
    'current': 'lg:border-current',
    'black': 'lg:border-black',
    'white': 'lg:border-white',

    'gray-50': 'lg:border-gray-50',
    'gray-100': 'lg:border-gray-100',
    'gray-200': 'lg:border-gray-200',
    'gray-300': 'lg:border-gray-300',
    'gray-400': 'lg:border-gray-400',
    'gray-500': 'lg:border-gray-500',
    'gray-600': 'lg:border-gray-600',
    'gray-700': 'lg:border-gray-700',
    'gray-800': 'lg:border-gray-800',
    'gray-900': 'lg:border-gray-900',

    'color1-50': 'lg:border-color1-50',
    'color1-100': 'lg:border-color1-100',
    'color1-200': 'lg:border-color1-200',
    'color1-300': 'lg:border-color1-300',
    'color1-400': 'lg:border-color1-400',
    'color1-500': 'lg:border-color1-500',
    'color1-600': 'lg:border-color1-600',
    'color1-700': 'lg:border-color1-700',
    'color1-800': 'lg:border-color1-800',
    'color1-900': 'lg:border-color1-900',

    'color2-50': 'lg:border-color2-50',
    'color2-100': 'lg:border-color2-100',
    'color2-200': 'lg:border-color2-200',
    'color2-300': 'lg:border-color2-300',
    'color2-400': 'lg:border-color2-400',
    'color2-500': 'lg:border-color2-500',
    'color2-600': 'lg:border-color2-600',
    'color2-700': 'lg:border-color2-700',
    'color2-800': 'lg:border-color2-800',
    'color2-900': 'lg:border-color2-900',

    'color3-50': 'lg:border-color3-50',
    'color3-100': 'lg:border-color3-100',
    'color3-200': 'lg:border-color3-200',
    'color3-300': 'lg:border-color3-300',
    'color3-400': 'lg:border-color3-400',
    'color3-500': 'lg:border-color3-500',
    'color3-600': 'lg:border-color3-600',
    'color3-700': 'lg:border-color3-700',
    'color3-800': 'lg:border-color3-800',
    'color3-900': 'lg:border-color3-900',

    'color4-50': 'lg:border-color4-50',
    'color4-100': 'lg:border-color4-100',
    'color4-200': 'lg:border-color4-200',
    'color4-300': 'lg:border-color4-300',
    'color4-400': 'lg:border-color4-400',
    'color4-500': 'lg:border-color4-500',
    'color4-600': 'lg:border-color4-600',
    'color4-700': 'lg:border-color4-700',
    'color4-800': 'lg:border-color4-800',
    'color4-900': 'lg:border-color4-900',

    'color5-50': 'lg:border-color5-50',
    'color5-100': 'lg:border-color5-100',
    'color5-200': 'lg:border-color5-200',
    'color5-300': 'lg:border-color5-300',
    'color5-400': 'lg:border-color5-400',
    'color5-500': 'lg:border-color5-500',
    'color5-600': 'lg:border-color5-600',
    'color5-700': 'lg:border-color5-700',
    'color5-800': 'lg:border-color5-800',
    'color5-900': 'lg:border-color5-900',

    'color6-50': 'lg:border-color6-50',
    'color6-100': 'lg:border-color6-100',
    'color6-200': 'lg:border-color6-200',
    'color6-300': 'lg:border-color6-300',
    'color6-400': 'lg:border-color6-400',
    'color6-500': 'lg:border-color6-500',
    'color6-600': 'lg:border-color6-600',
    'color6-700': 'lg:border-color6-700',
    'color6-800': 'lg:border-color6-800',
    'color6-900': 'lg:border-color6-900',
  },
  'xl': {
    'transparent': 'xl:border-transparent',
    'current': 'xl:border-current',
    'black': 'xl:border-black',
    'white': 'xl:border-white',

    'gray-50': 'xl:border-gray-50',
    'gray-100': 'xl:border-gray-100',
    'gray-200': 'xl:border-gray-200',
    'gray-300': 'xl:border-gray-300',
    'gray-400': 'xl:border-gray-400',
    'gray-500': 'xl:border-gray-500',
    'gray-600': 'xl:border-gray-600',
    'gray-700': 'xl:border-gray-700',
    'gray-800': 'xl:border-gray-800',
    'gray-900': 'xl:border-gray-900',

    'color1-50': 'xl:border-color1-50',
    'color1-100': 'xl:border-color1-100',
    'color1-200': 'xl:border-color1-200',
    'color1-300': 'xl:border-color1-300',
    'color1-400': 'xl:border-color1-400',
    'color1-500': 'xl:border-color1-500',
    'color1-600': 'xl:border-color1-600',
    'color1-700': 'xl:border-color1-700',
    'color1-800': 'xl:border-color1-800',
    'color1-900': 'xl:border-color1-900',

    'color2-50': 'xl:border-color2-50',
    'color2-100': 'xl:border-color2-100',
    'color2-200': 'xl:border-color2-200',
    'color2-300': 'xl:border-color2-300',
    'color2-400': 'xl:border-color2-400',
    'color2-500': 'xl:border-color2-500',
    'color2-600': 'xl:border-color2-600',
    'color2-700': 'xl:border-color2-700',
    'color2-800': 'xl:border-color2-800',
    'color2-900': 'xl:border-color2-900',

    'color3-50': 'xl:border-color3-50',
    'color3-100': 'xl:border-color3-100',
    'color3-200': 'xl:border-color3-200',
    'color3-300': 'xl:border-color3-300',
    'color3-400': 'xl:border-color3-400',
    'color3-500': 'xl:border-color3-500',
    'color3-600': 'xl:border-color3-600',
    'color3-700': 'xl:border-color3-700',
    'color3-800': 'xl:border-color3-800',
    'color3-900': 'xl:border-color3-900',

    'color4-50': 'xl:border-color4-50',
    'color4-100': 'xl:border-color4-100',
    'color4-200': 'xl:border-color4-200',
    'color4-300': 'xl:border-color4-300',
    'color4-400': 'xl:border-color4-400',
    'color4-500': 'xl:border-color4-500',
    'color4-600': 'xl:border-color4-600',
    'color4-700': 'xl:border-color4-700',
    'color4-800': 'xl:border-color4-800',
    'color4-900': 'xl:border-color4-900',

    'color5-50': 'xl:border-color5-50',
    'color5-100': 'xl:border-color5-100',
    'color5-200': 'xl:border-color5-200',
    'color5-300': 'xl:border-color5-300',
    'color5-400': 'xl:border-color5-400',
    'color5-500': 'xl:border-color5-500',
    'color5-600': 'xl:border-color5-600',
    'color5-700': 'xl:border-color5-700',
    'color5-800': 'xl:border-color5-800',
    'color5-900': 'xl:border-color5-900',

    'color6-50': 'xl:border-color6-50',
    'color6-100': 'xl:border-color6-100',
    'color6-200': 'xl:border-color6-200',
    'color6-300': 'xl:border-color6-300',
    'color6-400': 'xl:border-color6-400',
    'color6-500': 'xl:border-color6-500',
    'color6-600': 'xl:border-color6-600',
    'color6-700': 'xl:border-color6-700',
    'color6-800': 'xl:border-color6-800',
    'color6-900': 'xl:border-color6-900',
  },
  '2xl': {
    'transparent': '2xl:border-transparent',
    'current': '2xl:border-current',
    'black': '2xl:border-black',
    'white': '2xl:border-white',

    'gray-50': '2xl:border-gray-50',
    'gray-100': '2xl:border-gray-100',
    'gray-200': '2xl:border-gray-200',
    'gray-300': '2xl:border-gray-300',
    'gray-400': '2xl:border-gray-400',
    'gray-500': '2xl:border-gray-500',
    'gray-600': '2xl:border-gray-600',
    'gray-700': '2xl:border-gray-700',
    'gray-800': '2xl:border-gray-800',
    'gray-900': '2xl:border-gray-900',

    'color1-50': '2xl:border-color1-50',
    'color1-100': '2xl:border-color1-100',
    'color1-200': '2xl:border-color1-200',
    'color1-300': '2xl:border-color1-300',
    'color1-400': '2xl:border-color1-400',
    'color1-500': '2xl:border-color1-500',
    'color1-600': '2xl:border-color1-600',
    'color1-700': '2xl:border-color1-700',
    'color1-800': '2xl:border-color1-800',
    'color1-900': '2xl:border-color1-900',

    'color2-50': '2xl:border-color2-50',
    'color2-100': '2xl:border-color2-100',
    'color2-200': '2xl:border-color2-200',
    'color2-300': '2xl:border-color2-300',
    'color2-400': '2xl:border-color2-400',
    'color2-500': '2xl:border-color2-500',
    'color2-600': '2xl:border-color2-600',
    'color2-700': '2xl:border-color2-700',
    'color2-800': '2xl:border-color2-800',
    'color2-900': '2xl:border-color2-900',

    'color3-50': '2xl:border-color3-50',
    'color3-100': '2xl:border-color3-100',
    'color3-200': '2xl:border-color3-200',
    'color3-300': '2xl:border-color3-300',
    'color3-400': '2xl:border-color3-400',
    'color3-500': '2xl:border-color3-500',
    'color3-600': '2xl:border-color3-600',
    'color3-700': '2xl:border-color3-700',
    'color3-800': '2xl:border-color3-800',
    'color3-900': '2xl:border-color3-900',

    'color4-50': '2xl:border-color4-50',
    'color4-100': '2xl:border-color4-100',
    'color4-200': '2xl:border-color4-200',
    'color4-300': '2xl:border-color4-300',
    'color4-400': '2xl:border-color4-400',
    'color4-500': '2xl:border-color4-500',
    'color4-600': '2xl:border-color4-600',
    'color4-700': '2xl:border-color4-700',
    'color4-800': '2xl:border-color4-800',
    'color4-900': '2xl:border-color4-900',

    'color5-50': '2xl:border-color5-50',
    'color5-100': '2xl:border-color5-100',
    'color5-200': '2xl:border-color5-200',
    'color5-300': '2xl:border-color5-300',
    'color5-400': '2xl:border-color5-400',
    'color5-500': '2xl:border-color5-500',
    'color5-600': '2xl:border-color5-600',
    'color5-700': '2xl:border-color5-700',
    'color5-800': '2xl:border-color5-800',
    'color5-900': '2xl:border-color5-900',

    'color6-50': '2xl:border-color6-50',
    'color6-100': '2xl:border-color6-100',
    'color6-200': '2xl:border-color6-200',
    'color6-300': '2xl:border-color6-300',
    'color6-400': '2xl:border-color6-400',
    'color6-500': '2xl:border-color6-500',
    'color6-600': '2xl:border-color6-600',
    'color6-700': '2xl:border-color6-700',
    'color6-800': '2xl:border-color6-800',
    'color6-900': '2xl:border-color6-900',
  },
}

export default function useBorder(border) {
  return useMemo(() => getResponsiveBorderClasses(border), [border])
}

export function getResponsiveBorderClasses(border) {
  // Stop here if no border is defined or if it has no width
  if (!border?.width) return ''

  const classes = []

  // Width
  if (border.width) {
    for (const [side, responsiveValue] of Object.entries(border.width)) {
      for (const [breakpoint, value] of Object.entries(responsiveValue)) {
        const equalSidesAtBreakpoint = Object.entries(border.width).every(
          ([, v]) => {
            return v[breakpoint] === value
          }
        )

        if (equalSidesAtBreakpoint && borderWidthMap.all[breakpoint]?.[value]) {
          classes.push(borderWidthMap.all[breakpoint][value])
        } else if (borderWidthMap[side]?.[breakpoint]?.[value]) {
          classes.push(borderWidthMap[side][breakpoint][value])
        }
      }
    }
  }

  // Style
  if (border.style) {
    for (const [breakpoint, value] of Object.entries(border.style)) {
      if (borderStyleMap[breakpoint]?.[value]) {
        classes.push(borderStyleMap[breakpoint][value])
      }
    }
  }

  // Color
  if (border.color) {
    for (const [breakpoint, value] of Object.entries(border.color)) {
      if (borderColorsMap[breakpoint]?.[value]) {
        classes.push(borderColorsMap[breakpoint][value])
      }
    }
  }

  if (classes.length === 0) return ''

  return classes.join(' ')
}

/**
 * Returns a string of border classes based on the provided border prop
 * @param {object} border - The border props
 * @returns {string} - A string of border classes
 */
export function getBorderClasses(border) {
  if (!border) return ''

  const { width, style = '', color = '', radius = {} } = border

  const classes = []

  // Only set border props if width is defined
  if (width) {
    // Width
    classes.push(
      Object.entries(width)
        .reduce((acc, [side, value]) => {
          const equalSides = Object.values(width).every(v => v === value)

          if (equalSides && borderWidthMap.all.xs[value]) {
            acc.push(borderWidthMap.all.xs[value])
          } else if (borderWidthMap[side]?.xs[value]) {
            acc.push(borderWidthMap[side]?.xs[value])
          }
          return acc
        }, [])
        .join(' ')
    )

    // Style
    classes.push(borderStyleMap['xs'][style] || '')

    // Color
    if (color) {
      classes.push(borderColorsMap['xs'][color] || '')
    }
  }

  // Radius (can be handled separately from other border props)
  if (radius) {
    classes.push(getBorderRadiusClasses(radius))
  }

  return classes.join(' ')
}
