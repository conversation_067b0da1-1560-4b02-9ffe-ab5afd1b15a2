import { useResponsiveValue } from './useResponsiveValue'

const zIndexMap = {
  'xs': {
    0: 'z-0',
    10: 'z-10',
    20: 'z-20',
    30: 'z-30',
    40: 'z-40',
    50: 'z-50',
    auto: 'z-auto',
  },
  'sm': {
    0: 'sm:z-0',
    10: 'sm:z-10',
    20: 'sm:z-20',
    30: 'sm:z-30',
    40: 'sm:z-40',
    50: 'sm:z-50',
    auto: 'sm:z-auto',
  },
  'md': {
    0: 'md:z-0',
    10: 'md:z-10',
    20: 'md:z-20',
    30: 'md:z-30',
    40: 'md:z-40',
    50: 'md:z-50',
    auto: 'md:z-auto',
  },
  'lg': {
    0: 'lg:z-0',
    10: 'lg:z-10',
    20: 'lg:z-20',
    30: 'lg:z-30',
    40: 'lg:z-40',
    50: 'lg:z-50',
    auto: 'lg:z-auto',
  },
  'xl': {
    0: 'xl:z-0',
    10: 'xl:z-10',
    20: 'xl:z-20',
    30: 'xl:z-30',
    40: 'xl:z-40',
    50: 'xl:z-50',
    auto: 'xl:z-auto',
  },
  '2xl': {
    0: '2xl:z-0',
    10: '2xl:z-10',
    20: '2xl:z-20',
    30: '2xl:z-30',
    40: '2xl:z-40',
    50: '2xl:z-50',
    auto: '2xl:z-auto',
  },
}

export function useZIndex(zIndex = { xs: 'static' }) {
  return useResponsiveValue(zIndexMap, zIndex)
}

export default useZIndex
