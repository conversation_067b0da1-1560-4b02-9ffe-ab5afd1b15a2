/**
 * Returns the CSS class for the background color.
 * @param {string} colorKey - The color key, e.g. 'color1'
 * @param {string} fallbackColor  - The fallback color, e.g. 'bg-gray-500'
 * @returns {string} - The CSS class for the background color.
 */
export function getBackgroundColor(colorKey = '', fallbackColor = null) {
  return getAttributeColorClass('bg', colorKey) ?? fallbackColor ?? ''
}

/**
 * Returns the CSS class for the text color.
 * @param {string} colorKey - The color key, e.g. 'color1'
 * @param {string} fallbackColor  - The fallback color, e.g. 'text-gray-500'
 * @returns {string} - The CSS class for the text color.
 */
export function getTextColor(color, fallbackColor = null) {
  return getAttributeColorClass('text', color) ?? fallbackColor ?? ''
}

/**
 * Returns the CSS class for the hover text color.
 * @param {string} colorKey - The color key, e.g. 'color1'
 * @param {string} fallbackColor  - The fallback color, e.g. 'hover:text-gray-500'
 * @returns {string} - The CSS class for the hover text color.
 */
export function getHoverTextColor(color, fallbackColor = null) {
  return getAttributeColorClass('hover:text', color) ?? fallbackColor ?? ''
}

/**
 * Returns the CSS class for the hover background color
 * @param {string} colorKey - The color key, e.g. 'color1'
 * @param {string} fallbackColor  - The fallback color, e.g. 'hover:bg-gray-500'
 * @returns {string} - The CSS class for the hover background color
 */
export function getHoverBackgroundColor(color, fallbackColor = null) {
  return getAttributeColorClass('hover:bg', color) ?? fallbackColor ?? ''
}

/**
 * Returns the CSS class for the fill color.
 * @param {string} colorKey - The color key, e.g. 'color1'
 * @param {string} fallbackColor  - The fallback color, e.g. 'fill-gray-500'
 * @returns {string} - The CSS class for the fill color.
 */
export function getFillColor(color, fallbackColor = null) {
  return getAttributeColorClass('fill', color) ?? fallbackColor ?? ''
}

/**
 * Returns the CSS class for the divide color.
 * @param {string} colorKey - The color key, e.g. 'color1'
 * @param {string} fallbackColor  - The fallback color, e.g. 'divide-gray-500'
 * @returns {string} - The CSS class for the divide color.
 */
export function getDivideColor(color, fallbackColor = null) {
  return getAttributeColorClass('divide', color) ?? fallbackColor ?? ''
}

/**
 * Returns a class for the color attribute
 * @param {string} attribute - The attribute, e.g. 'bg'
 * @param {string} color - The color key, e.g. 'color1'
 * @param {string} fallbackColor  - The fallback color, e.g. 'bg-gray-500'
 * @returns {string} - The CSS class for the color attribute.
 */
export function getAttributeColorClass(attribute, colorKey) {
  if (!colorKey) return ''

  const [color, scale] = colorKey.split('-')
  const isPaletteColor = paletteColors.includes(color)
  const isPaletteScale = paletteScale.includes(scale)
  const isSingleColor = singleColors.includes(color)

  if (!colorAttributes.includes(attribute)) return ''

  if (isPaletteColor && isPaletteScale) {
    return `${attribute}-${color}-${scale}`
  }

  if (isSingleColor) {
    return `${attribute}-${color}`
  }

  return ''
}

// Available color attributes (border and divide are set in their own hooks/helpers)
const colorAttributes = [
  'bg',
  'border',
  'fill',
  'text',
  'hover:text',
  'hover:bg',
]

// Available colors with a palette scale
const paletteColors = [
  'color1',
  'color2',
  'color3',
  'color4',
  'color5',
  'color6',
  'gray',
]

// Possible values for the scale
const paletteScale = [
  '50',
  '100',
  '200',
  '300',
  '400',
  '500',
  '600',
  '700',
  '800',
  '900',
  '950',
]

// Colors that have no scale
const singleColors = ['black', 'white', 'transparent', 'current']

export const colors = [
  'color1', // bg-color1-500 text-color1-500 fill-color1-500 divider-color1-500 hover:bg-color1-500 hover:text-color1-500 hover:border-color1-500
  'color1-50', // bg-color1-50 text-color1-50 fill-color1-50 divider-color1-50 hover:bg-color1-50 hover:text-color1-50 hover:border-color1-50
  'color1-100', // bg-color1-100 text-color1-100 fill-color1-100 divider-color1-100 hover:bg-color1-100 hover:text-color1-100 hover:border-color1-100
  'color1-200', // bg-color1-200 text-color1-200 fill-color1-200 divider-color1-200 hover:bg-color1-200 hover:text-color1-200 hover:border-color1-200
  'color1-300', // bg-color1-300 text-color1-300 fill-color1-300 divider-color1-300 hover:bg-color1-300 hover:text-color1-300 hover:border-color1-300
  'color1-400', // bg-color1-400 text-color1-400 fill-color1-400 divider-color1-400 hover:bg-color1-400 hover:text-color1-400 hover:border-color1-400
  'color1-500', // bg-color1-500 text-color1-500 fill-color1-500 divider-color1-500 hover:bg-color1-500 hover:text-color1-500 hover:border-color1-500
  'color1-600', // bg-color1-600 text-color1-600 fill-color1-600 divider-color1-600 hover:bg-color1-600 hover:text-color1-600 hover:border-color1-600
  'color1-700', // bg-color1-700 text-color1-700 fill-color1-700 divider-color1-700 hover:bg-color1-700 hover:text-color1-700 hover:border-color1-700
  'color1-800', // bg-color1-800 text-color1-800 fill-color1-800 divider-color1-800 hover:bg-color1-800 hover:text-color1-800 hover:border-color1-800
  'color1-900', // bg-color1-900 text-color1-900 fill-color1-900 divider-color1-900 hover:bg-color1-900 hover:text-color1-900 hover:border-color1-900
  'color1-950', // bg-color1-950 text-color1-950 fill-color1-950 divider-color1-950 hover:bg-color1-950 hover:text-color1-950 hover:border-color1-950

  'color2', // bg-color2-500 text-color2-500 fill-color2-500 divider-color2-500 hover:bg-color2-500 hover:text-color2-500 hover:border-color2-500
  'color2-50', // bg-color2-50 text-color2-50 fill-color2-50 divider-color2-50 hover:bg-color2-50 hover:text-color2-50 hover:border-color2-50
  'color2-100', // bg-color2-100 text-color2-100 fill-color2-100 divider-color2-100 hover:bg-color2-100 hover:text-color2-100 hover:border-color2-100
  'color2-200', // bg-color2-200 text-color2-200 fill-color2-200 divider-color2-200 hover:bg-color2-200 hover:text-color2-200 hover:border-color2-200
  'color2-300', // bg-color2-300 text-color2-300 fill-color2-300 divider-color2-300 hover:bg-color2-300 hover:text-color2-300 hover:border-color2-300
  'color2-400', // bg-color2-400 text-color2-400 fill-color2-400 divider-color2-400 hover:bg-color2-400 hover:text-color2-400 hover:border-color2-400
  'color2-500', // bg-color2-500 text-color2-500 fill-color2-500 divider-color2-500 hover:bg-color2-500 hover:text-color2-500 hover:border-color2-500
  'color2-600', // bg-color2-600 text-color2-600 fill-color2-600 divider-color2-600 hover:bg-color2-600 hover:text-color2-600 hover:border-color2-600
  'color2-700', // bg-color2-700 text-color2-700 fill-color2-700 divider-color2-700 hover:bg-color2-700 hover:text-color2-700 hover:border-color2-700
  'color2-800', // bg-color2-800 text-color2-800 fill-color2-800 divider-color2-800 hover:bg-color2-800 hover:text-color2-800 hover:border-color2-800
  'color2-900', // bg-color2-900 text-color2-900 fill-color2-900 divider-color2-900 hover:bg-color2-900 hover:text-color2-900 hover:border-color2-900
  'color2-950', // bg-color2-950 text-color2-950 fill-color2-950 divider-color2-950 hover:bg-color2-950 hover:text-color2-950 hover:border-color2-950

  'color3', // bg-color3-500 text-color3-500 fill-color3-500 divider-color3-500 hover:bg-color3-500 hover:text-color3-500 hover:border-color3-500
  'color3-50', // bg-color3-50 text-color3-50 fill-color3-50 divider-color3-50 hover:bg-color3-50 hover:text-color3-50 hover:border-color3-50
  'color3-100', // bg-color3-100 text-color3-100 fill-color3-100 divider-color3-100 hover:bg-color3-100 hover:text-color3-100 hover:border-color3-100
  'color3-200', // bg-color3-200 text-color3-200 fill-color3-200 divider-color3-200 hover:bg-color3-200 hover:text-color3-200 hover:border-color3-200
  'color3-300', // bg-color3-300 text-color3-300 fill-color3-300 divider-color3-300 hover:bg-color3-300 hover:text-color3-300 hover:border-color3-300
  'color3-400', // bg-color3-400 text-color3-400 fill-color3-400 divider-color3-400 hover:bg-color3-400 hover:text-color3-400 hover:border-color3-400
  'color3-500', // bg-color3-500 text-color3-500 fill-color3-500 divider-color3-500 hover:bg-color3-500 hover:text-color3-500 hover:border-color3-500
  'color3-600', // bg-color3-600 text-color3-600 fill-color3-600 divider-color3-600 hover:bg-color3-600 hover:text-color3-600 hover:border-color3-600
  'color3-700', // bg-color3-700 text-color3-700 fill-color3-700 divider-color3-700 hover:bg-color3-700 hover:text-color3-700 hover:border-color3-700
  'color3-800', // bg-color3-800 text-color3-800 fill-color3-800 divider-color3-800 hover:bg-color3-800 hover:text-color3-800 hover:border-color3-800
  'color3-900', // bg-color3-900 text-color3-900 fill-color3-900 divider-color3-900 hover:bg-color3-900 hover:text-color3-900 hover:border-color3-900
  'color3-950', // bg-color3-950 text-color3-950 fill-color3-950 divider-color3-950 hover:bg-color3-950 hover:text-color3-950 hover:border-color3-950

  'color4', // bg-color4-500 text-color4-500 fill-color4-500 divider-color4-500 hover:bg-color4-500 hover:text-color4-500 hover:border-color4-500
  'color4-50', // bg-color4-50 text-color4-50 fill-color4-50 divider-color4-50 hover:bg-color4-50 hover:text-color4-50 hover:border-color4-50
  'color4-100', // bg-color4-100 text-color4-100 fill-color4-100 divider-color4-100 hover:bg-color4-100 hover:text-color4-100 hover:border-color4-100
  'color4-200', // bg-color4-200 text-color4-200 fill-color4-200 divider-color4-200 hover:bg-color4-200 hover:text-color4-200 hover:border-color4-200
  'color4-300', // bg-color4-300 text-color4-300 fill-color4-300 divider-color4-300 hover:bg-color4-300 hover:text-color4-300 hover:border-color4-300
  'color4-400', // bg-color4-400 text-color4-400 fill-color4-400 divider-color4-400 hover:bg-color4-400 hover:text-color4-400 hover:border-color4-400
  'color4-500', // bg-color4-500 text-color4-500 fill-color4-500 divider-color4-500 hover:bg-color4-500 hover:text-color4-500 hover:border-color4-500
  'color4-600', // bg-color4-600 text-color4-600 fill-color4-600 divider-color4-600 hover:bg-color4-600 hover:text-color4-600 hover:border-color4-600
  'color4-700', // bg-color4-700 text-color4-700 fill-color4-700 divider-color4-700 hover:bg-color4-700 hover:text-color4-700 hover:border-color4-700
  'color4-800', // bg-color4-800 text-color4-800 fill-color4-800 divider-color4-800 hover:bg-color4-800 hover:text-color4-800 hover:border-color4-800
  'color4-900', // bg-color4-900 text-color4-900 fill-color4-900 divider-color4-900 hover:bg-color4-900 hover:text-color4-900 hover:border-color4-900
  'color4-950', // bg-color4-950 text-color4-950 fill-color4-950 divider-color4-950 hover:bg-color4-950 hover:text-color4-950 hover:border-color4-950

  'color5', // bg-color5-500 text-color5-500 fill-color5-500 divider-color5-500 hover:bg-color5-500 hover:text-color5-500 hover:border-color5-500
  'color5-50', // bg-color5-50 text-color5-50 fill-color5-50 divider-color5-50 hover:bg-color5-50 hover:text-color5-50 hover:border-color5-50
  'color5-100', // bg-color5-100 text-color5-100 fill-color5-100 divider-color5-100 hover:bg-color5-100 hover:text-color5-100 hover:border-color5-100
  'color5-200', // bg-color5-200 text-color5-200 fill-color5-200 divider-color5-200 hover:bg-color5-200 hover:text-color5-200 hover:border-color5-200
  'color5-300', // bg-color5-300 text-color5-300 fill-color5-300 divider-color5-300 hover:bg-color5-300 hover:text-color5-300 hover:border-color5-300
  'color5-400', // bg-color5-400 text-color5-400 fill-color5-400 divider-color5-400 hover:bg-color5-400 hover:text-color5-400 hover:border-color5-400
  'color5-500', // bg-color5-500 text-color5-500 fill-color5-500 divider-color5-500 hover:bg-color5-500 hover:text-color5-500 hover:border-color5-500
  'color5-600', // bg-color5-600 text-color5-600 fill-color5-600 divider-color5-600 hover:bg-color5-600 hover:text-color5-600 hover:border-color5-600
  'color5-700', // bg-color5-700 text-color5-700 fill-color5-700 divider-color5-700 hover:bg-color5-700 hover:text-color5-700 hover:border-color5-700
  'color5-800', // bg-color5-800 text-color5-800 fill-color5-800 divider-color5-800 hover:bg-color5-800 hover:text-color5-800 hover:border-color5-800
  'color5-900', // bg-color5-900 text-color5-900 fill-color5-900 divider-color5-900 hover:bg-color5-900 hover:text-color5-900 hover:border-color5-900
  'color5-950', // bg-color5-950 text-color5-950 fill-color5-950 divider-color5-950 hover:bg-color5-950 hover:text-color5-950 hover:border-color5-950

  'color6', // bg-color6-500 text-color6-500 fill-color6-500 divider-color6-500 hover:bg-color6-500 hover:text-color6-500 hover:border-color6-500
  'color6-50', // bg-color6-50 text-color6-50 fill-color6-50 divider-color6-50 hover:bg-color6-50 hover:text-color6-50 hover:border-color6-50
  'color6-100', // bg-color6-100 text-color6-100 fill-color6-100 divider-color6-100 hover:bg-color6-100 hover:text-color6-100 hover:border-color6-100
  'color6-200', // bg-color6-200 text-color6-200 fill-color6-200 divider-color6-200 hover:bg-color6-200 hover:text-color6-200 hover:border-color6-200
  'color6-300', // bg-color6-300 text-color6-300 fill-color6-300 divider-color6-300 hover:bg-color6-300 hover:text-color6-300 hover:border-color6-300
  'color6-400', // bg-color6-400 text-color6-400 fill-color6-400 divider-color6-400 hover:bg-color6-400 hover:text-color6-400 hover:border-color6-400
  'color6-500', // bg-color6-500 text-color6-500 fill-color6-500 divider-color6-500 hover:bg-color6-500 hover:text-color6-500 hover:border-color6-500
  'color6-600', // bg-color6-600 text-color6-600 fill-color6-600 divider-color6-600 hover:bg-color6-600 hover:text-color6-600 hover:border-color6-600
  'color6-700', // bg-color6-700 text-color6-700 fill-color6-700 divider-color6-700 hover:bg-color6-700 hover:text-color6-700 hover:border-color6-700
  'color6-800', // bg-color6-800 text-color6-800 fill-color6-800 divider-color6-800 hover:bg-color6-800 hover:text-color6-800 hover:border-color6-800
  'color6-900', // bg-color6-900 text-color6-900 fill-color6-900 divider-color6-900 hover:bg-color6-900 hover:text-color6-900 hover:border-color6-900
  'color6-950', // bg-color6-950 text-color6-950 fill-color6-950 divider-color6-950 hover:bg-color6-950 hover:text-color6-950 hover:border-color6-950

  'gray', // bg-gray-500 text-gray-500 fill-gray-500 divider-gray-500 hover:bg-gray-500 hover:text-gray-500 hover:border-gray-500
  'gray-50', // bg-gray-50 text-gray-50 fill-gray-50 divider-gray-50 hover:bg-gray-50 hover:text-gray-50 hover:border-gray-50
  'gray-100', // bg-gray-100 text-gray-100 fill-gray-100 divider-gray-100 hover:bg-gray-100 hover:text-gray-100 hover:border-gray-100
  'gray-200', // bg-gray-200 text-gray-200 fill-gray-200 divider-gray-200 hover:bg-gray-200 hover:text-gray-200 hover:border-gray-200
  'gray-300', // bg-gray-300 text-gray-300 fill-gray-300 divider-gray-300 hover:bg-gray-300 hover:text-gray-300 hover:border-gray-300
  'gray-400', // bg-gray-400 text-gray-400 fill-gray-400 divider-gray-400 hover:bg-gray-400 hover:text-gray-400 hover:border-gray-400
  'gray-500', // bg-gray-500 text-gray-500 fill-gray-500 divider-gray-500 hover:bg-gray-500 hover:text-gray-500 hover:border-gray-500
  'gray-600', // bg-gray-600 text-gray-600 fill-gray-600 divider-gray-600 hover:bg-gray-600 hover:text-gray-600 hover:border-gray-600
  'gray-700', // bg-gray-700 text-gray-700 fill-gray-700 divider-gray-700 hover:bg-gray-700 hover:text-gray-700 hover:border-gray-700
  'gray-800', // bg-gray-800 text-gray-800 fill-gray-800 divider-gray-800 hover:bg-gray-800 hover:text-gray-800 hover:border-gray-800
  'gray-900', // bg-gray-900 text-gray-900 fill-gray-900 divider-gray-900 hover:bg-gray-900 hover:text-gray-900 hover:border-gray-900
  'gray-950', // bg-gray-950 text-gray-950 fill-gray-950 divider-gray-950 hover:bg-gray-950 hover:text-gray-950 hover:border-gray-950

  'black', // bg-black text-black fill-black border-black divider-black hover:bg-black hover:text-black hover:border-black
  'white', // bg-white text-white fill-white border-white divider-white hover:bg-white hover:text-white hover:border-white
  'current', // bg-current text-current fill-current border-current divider-current hover:bg-current hover:text-current hover:border-current
  'transparent', // bg-transparent text-transparent fill-transparent border-transparent divider-transparent hover:bg-transparent hover:text-transparent hover:border-transparent
]
