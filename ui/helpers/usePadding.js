import { useMemo } from 'react'

import { breakpointKeys } from 'utils/media'
import { isObject } from 'utils/types'

import { getResponsiveClass } from './getResponsiveClasses'
import { getValueAtBreakpoint } from './getValueAtBreakpoint'

/**
 * Returns a string of padding classes based on the padding settings object.
 *
 * @param {object} padding - padding settings object
 * @param {string} defaultClass - default class to return if no padding is set
 * @returns {string} - string of padding classes
 */
export default function usePadding(padding = {}, defaultClass = '') {
  return useMemo(
    () => getResponsivePaddingClasses(padding, defaultClass),
    [padding, defaultClass]
  )
}

/**
 * Returns a string of padding classes based on the padding settings and inherited padding objects.
 * @param {object} padding - padding settings object
 * @param {object} inheritedPadding - inherted padding settings object
 * @param {string} breakpointPrefix - breakpoint prefix (e.g. "sm:")
 * @param {string} defaultClass - default class to return if no padding is set
 * @returns {string} - string of padding classes
 */
export function getPaddingClasses(
  padding,
  inheritedPadding,
  breakpoint,
  defaultClass = ''
) {
  // and the padding prefixes at breakpoint
  const prefixes = getPaddingsPrefixes(padding)

  // If there are no prefixes, stop here and return the current classes
  if (!Object.keys(prefixes).length) return defaultClass

  // For every side, get the padding classes at breakpoint
  const paddingClasses = Object.entries(prefixes).reduce(
    (sideClasses, [side, prefix]) => {
      // Get the padding value at breakpoint from the padding map
      const value = getPaddingValue(padding, inheritedPadding, side)

      // If the value is a supported option,
      if (value !== undefined && value !== null) {
        // Add the padding class to the list
        // examples:
        // - prefix: "p", value: 2, breakpoint: undefined => "p-2"
        // - prefix: "px", value: 2, breakpoint: "sm" => "sm:px-2"
        // - prefix: "pl", value: 2, breakpoint: "md" => "md:pl-2"
        sideClasses.push(getResponsiveClass(prefix, value, breakpoint))
      }

      return sideClasses
    },
    []
  )

  return paddingClasses.join(' ') || defaultClass
}

/**
 * Returns the padding value for a given side. If the value is undefined or null, it will return the value
 * of the opposite side, or from top, unless the side already has an inherited value from a previous breakpoint.
 *
 * @param {object} padding - padding settings object
 * @param {object} inheritedPadding - inherted padding settings object
 * @param {string} side - padding side (top, right, bottom, left)
 * @returns {string} - padding value
 */
function getPaddingValue(padding, inheritedPadding, side) {
  const value = padding[side]

  // If the value is undefined or null, return the value of the opposite side, or use opposite side/top value if there is no inherited value
  if (value === undefined || value === null) {
    if (side === 'bottom')
      return inheritedPadding.bottom?.inherit
        ? undefined
        : getPaddingValue(padding, inheritedPadding, 'top')
    if (side === 'right')
      return inheritedPadding.right?.inherit
        ? undefined
        : getPaddingValue(padding, inheritedPadding, 'left')
    if (side === 'left')
      return inheritedPadding.left?.inherit
        ? undefined
        : getPaddingValue(padding, inheritedPadding, 'top')
    return undefined
  }

  return paddingMap[value]
}

/**
 * Returns a string of responsive padding classes based on the padding settings object.
 * @param {object} padding - padding settings object
 * @param {string} defaultClass - default class to return if no padding is set
 * @returns {string} - string of responsive padding classes
 */
export function getResponsivePaddingClasses(padding = {}, defaultClass = '') {
  // Stop here if padding is not an object
  if (!isObject(padding)) return defaultClass || ''
  const { top, right, bottom, left } = padding

  // Stop here if no padding side is set
  if (!(top || right || bottom || left)) return defaultClass || ''

  // For every breakpoint, from the smallest to the largest, get the padding classes
  const paddingClasses = breakpointKeys.reduce(
    (breakpointClasses, breakpoint) => {
      // Get padding settings at breakpoint
      const paddingAtBreakpoint = {
        top: top?.[breakpoint],
        left: left?.[breakpoint],
        bottom: bottom?.[breakpoint],
        right: right?.[breakpoint],
      }

      // Get inherited padding settings at breakpoint
      const inheritedPaddingAtBreakpoint = {
        top: getValueAtBreakpoint(top, breakpoint),
        left: getValueAtBreakpoint(left, breakpoint),
        bottom: getValueAtBreakpoint(bottom, breakpoint),
        right: getValueAtBreakpoint(right, breakpoint),
      }

      const hasValueAtBreakpoint = Object.values(paddingAtBreakpoint).some(
        value => value !== undefined && value !== null
      )

      if (hasValueAtBreakpoint) {
        // Get the padding classes at breakpoint
        breakpointClasses.push(
          getPaddingClasses(
            paddingAtBreakpoint,
            inheritedPaddingAtBreakpoint,
            breakpoint
          )
        )
      }

      return breakpointClasses
    },
    []
  )

  return paddingClasses.join(' ') || defaultClass
}

// Supported padding prefixe
const paddingPrefixes = {
  all: 'p',
  x: 'px',
  y: 'py',
  top: 'pt',
  left: 'pl',
  bottom: 'pb',
  right: 'pr',
}

/**
 * Get padding prefixes ('p', 'px', 'py', 'pt', 'pl', 'pb', 'pr') from a padding settings object.
 *
 * @param {object} padding - padding settings
 * @returns {object} - padding prefixes object (with keys top, right, bottom, left)
 */
function getPaddingsPrefixes(padding = {}) {
  const { top, right, bottom, left } = padding

  if (!(top || right || bottom || left)) return {}

  const prefixes = {}

  if (top) {
    // when other sides than top are undefined, indicate that the top value needs to set all others
    if (!left && !bottom && !right) {
      prefixes.top = paddingPrefixes.all
      return prefixes
    }

    // when left or right are undefined, indicate that the top value needs to set the x sides
    if (!left && !right) {
      prefixes.left = paddingPrefixes.x
    }

    // if both top and bottom are set, set the top prefix to "pt", otherwise to "py"
    prefixes.top = bottom ? paddingPrefixes.top : paddingPrefixes.y
  }

  // if there is a right value, set the right prefix ("pr")
  if (right) prefixes.right = paddingPrefixes.right

  // if both left and right are set, set the left prefix to "pl", otherwise to "px"
  if (left) prefixes.left = right ? paddingPrefixes.left : paddingPrefixes.x

  // if there is a bottom value, set the bottom prefix ("pb")
  if (bottom) prefixes.bottom = paddingPrefixes.bottom

  return prefixes
}

// List of all padding options supported by this desing
// NOTE: the commented classes ARE REQUIRED for the design to work properly. DO NOT REMOVE THEM!
const paddingMap = {
  // DEFAULT SIDES:
  '0': 0,
  //     p-0     px-0     py-0     pt-0     pb-0     pl-0     pr-0
  //  sm:p-0  sm:px-0  sm:py-0  sm:pt-0  sm:pb-0  sm:pl-0  sm:pr-0
  //  md:p-0  md:px-0  md:py-0  md:pt-0  md:pb-0  md:pl-0  md:pr-0
  //  lg:p-0  lg:px-0  lg:py-0  lg:pt-0  lg:pb-0  lg:pl-0  lg:pr-0
  //  xl:p-0  xl:px-0  xl:py-0  xl:pt-0  xl:pb-0  xl:pl-0  xl:pr-0
  // 2xl:p-0 2xl:px-0 2xl:py-0 2xl:pt-0 2xl:pb-0 2xl:pl-0 2xl:pr-0

  'px': 'px',
  //     p-px     px-px     py-px     pt-px     pb-px     pl-px     pr-px
  //  sm:p-px  sm:px-px  sm:py-px  sm:pt-px  sm:pb-px  sm:pl-px  sm:pr-px
  //  md:p-px  md:px-px  md:py-px  md:pt-px  md:pb-px  md:pl-px  md:pr-px
  //  lg:p-px  lg:px-px  lg:py-px  lg:pt-px  lg:pb-px  lg:pl-px  lg:pr-px
  //  xl:p-px  xl:px-px  xl:py-px  xl:pt-px  xl:pb-px  xl:pl-px  xl:pr-px
  // 2xl:p-px 2xl:px-px 2xl:py-px 2xl:pt-px 2xl:pb-px 2xl:pl-px 2xl:pr-px

  '0.5': '0.5',
  //     p-0.5     px-0.5      py-0.5     pt-0.5     pb-0.5     pl-0.5     pr-0.5
  //  sm:p-0.5  sm:px-0.5  sm:p y-0.5  sm:pt-0.5  sm:pb-0.5  sm:pl-0.5  sm:pr-0.5
  //  md:p-0.5  md:px-0.5  md:p y-0.5  md:pt-0.5  md:pb-0.5  md:pl-0.5  md:pr-0.5
  //  lg:p-0.5  lg:px-0.5  lg:p y-0.5  lg:pt-0.5  lg:pb-0.5  lg:pl-0.5  lg:pr-0.5
  //  xl:p-0.5  xl:px-0.5  xl:p y-0.5  xl:pt-0.5  xl:pb-0.5  xl:pl-0.5  xl:pr-0.5
  // 2xl:p-0.5 2xl:px-0.5 2xl:p y-0.5 2xl:pt-0.5 2xl:pb-0.5 2xl:pl-0.5 2xl:pr-0.5

  '1': 1,
  //     p-1     px-1     py-1     pt-1     pb-1     pl-1     pr-1
  //  sm:p-1  sm:px-1  sm:py-1  sm:pt-1  sm:pb-1  sm:pl-1  sm:pr-1
  //  md:p-1  md:px-1  md:py-1  md:pt-1  md:pb-1  md:pl-1  md:pr-1
  //  lg:p-1  lg:px-1  lg:py-1  lg:pt-1  lg:pb-1  lg:pl-1  lg:pr-1
  //  xl:p-1  xl:px-1  xl:py-1  xl:pt-1  xl:pb-1  xl:pl-1  xl:pr-1
  // 2xl:p-1 2xl:px-1 2xl:py-1 2xl:pt-1 2xl:pb-1 2xl:pl-1 2xl:pr-1

  '2': 2,
  //     p-2     px-2     py-2     pt-2     pb-2     pl-2     pr-2
  //  sm:p-2  sm:px-2  sm:py-2  sm:pt-2  sm:pb-2  sm:pl-2  sm:pr-2
  //  md:p-2  md:px-2  md:py-2  md:pt-2  md:pb-2  md:pl-2  md:pr-2
  //  lg:p-2  lg:px-2  lg:py-2  lg:pt-2  lg:pb-2  lg:pl-2  lg:pr-2
  //  xl:p-2  xl:px-2  xl:py-2  xl:pt-2  xl:pb-2  xl:pl-2  xl:pr-2
  // 2xl:p-2 2xl:px-2 2xl:py-2 2xl:pt-2 2xl:pb-2 2xl:pl-2 2xl:pr-2

  '3': 3,
  //     p-3     px-3     py-3     pt-3     pb-3    p l-3    pr -3
  //  sm:p-3  sm:px-3  sm:py-3  sm:pt-3  sm:pb-3  sm:pl-3  sm:pr-3
  //  md:p-3  md:px-3  md:py-3  md:pt-3  md:pb-3  md:pl-3  md:pr-3
  //  lg:p-3  lg:px-3  lg:py-3  lg:pt-3  lg:pb-3  lg:pl-3  lg:pr-3
  //  xl:p-3  xl:px-3  xl:py-3  xl:pt-3  xl:pb-3  xl:pl-3  xl:pr-3
  // 2xl:p-3 2xl:px-3 2xl:py-3 2xl:pt-3 2xl:pb-3 2xl:pl-3 2xl:pr-3

  '4': 4,
  //     p-4     px-4     py-4     pt-4     pb-4    p l-4    pr -4
  //  sm:p-4  sm:px-4  sm:py-4  sm:pt-4  sm:pb-4  sm:pl-4  sm:pr-4
  //  md:p-4  md:px-4  md:py-4  md:pt-4  md:pb-4  md:pl-4  md:pr-4
  //  lg:p-4  lg:px-4  lg:py-4  lg:pt-4  lg:pb-4  lg:pl-4  lg:pr-4
  //  xl:p-4  xl:px-4  xl:py-4  xl:pt-4  xl:pb-4  xl:pl-4  xl:pr-4
  // 2xl:p-4 2xl:px-4 2xl:py-4 2xl:pt-4 2xl:pb-4 2xl:pl-4 2xl:pr-4

  '5': 5,
  //    p-5     px-5     py-5     pt-5    p b-5    pl -5    pr- 5
  //  sm:p-5  sm:px-5  sm:py-5  sm:pt-5  sm:pb-5  sm:pl-5  sm:pr-5
  //  md:p-5  md:px-5  md:py-5  md:pt-5  md:pb-5  md:pl-5  md:pr-5
  //  lg:p-5  lg:px-5  lg:py-5  lg:pt-5  lg:pb-5  lg:pl-5  lg:pr-5
  //  xl:p-5  xl:px-5  xl:py-5  xl:pt-5  xl:pb-5  xl:pl-5  xl:pr-5
  // 2xl:p-5 2xl:px-5 2xl:py-5 2xl:pt-5 2xl:pb-5 2xl:pl-5 2xl:pr-5

  '6': 6,
  //     p-6     px-6     py-6     pt-6     pb-6    p l-6    pr -6
  //  sm:p-6  sm:px-6  sm:py-6  sm:pt-6  sm:pb-6  sm:pl-6  sm:pr-6
  //  md:p-6  md:px-6  md:py-6  md:pt-6  md:pb-6  md:pl-6  md:pr-6
  //  lg:p-6  lg:px-6  lg:py-6  lg:pt-6  lg:pb-6  lg:pl-6  lg:pr-6
  //  xl:p-6  xl:px-6  xl:py-6  xl:pt-6  xl:pb-6  xl:pl-6  xl:pr-6
  // 2xl:p-6 2xl:px-6 2xl:py-6 2xl:pt-6 2xl:pb-6 2xl:pl-6 2xl:pr-6

  '7': 7,
  //     p-7     px-7     py-7     pt-7     pb-7    p l-7    pr -7
  //  sm:p-7  sm:px-7  sm:py-7  sm:pt-7  sm:pb-7  sm:pl-7  sm:pr-7
  //  md:p-7  md:px-7  md:py-7  md:pt-7  md:pb-7  md:pl-7  md:pr-7
  //  lg:p-7  lg:px-7  lg:py-7  lg:pt-7  lg:pb-7  lg:pl-7  lg:pr-7
  //  xl:p-7  xl:px-7  xl:py-7  xl:pt-7  xl:pb-7  xl:pl-7  xl:pr-7
  // 2xl:p-7 2xl:px-7 2xl:py-7 2xl:pt-7 2xl:pb-7 2xl:pl-7 2xl:pr-7

  '8': 8,
  //     p-8     px-8     py-8     pt-8     pb-8    p l-8    pr -8
  //  sm:p-8  sm:px-8  sm:py-8  sm:pt-8  sm:pb-8  sm:pl-8  sm:pr-8
  //  md:p-8  md:px-8  md:py-8  md:pt-8  md:pb-8  md:pl-8  md:pr-8
  //  lg:p-8  lg:px-8  lg:py-8  lg:pt-8  lg:pb-8  lg:pl-8  lg:pr-8
  //  xl:p-8  xl:px-8  xl:py-8  xl:pt-8  xl:pb-8  xl:pl-8  xl:pr-8
  // 2xl:p-8 2xl:px-8 2xl:py-8 2xl:pt-8 2xl:pb-8 2xl:pl-8 2xl:pr-8

  '9': 9,
  //     p-9     px-9     py-9     pt-9     pb-9    p l-9    pr -9
  //  sm:p-9  sm:px-9  sm:py-9  sm:pt-9  sm:pb-9  sm:pl-9  sm:pr-9
  //  md:p-9  md:px-9  md:py-9  md:pt-9  md:pb-9  md:pl-9  md:pr-9
  //  lg:p-9  lg:px-9  lg:py-9  lg:pt-9  lg:pb-9  lg:pl-9  lg:pr-9
  //  xl:p-9  xl:px-9  xl:py-9  xl:pt-9  xl:pb-9  xl:pl-9  xl:pr-9
  // 2xl:p-9 2xl:px-9 2xl:py-9 2xl:pt-9 2xl:pb-9 2xl:pl-9 2xl:pr-9

  '10': 10,
  //     p-10     px-10     py-10     pt-10     pb-10     pl-10     pr-10
  //  sm:p-10  sm:px-10  sm:py-10  sm:pt-10  sm:pb-10  sm:pl-10  sm:pr-10
  //  md:p-10  md:px-10  md:py-10  md:pt-10  md:pb-10  md:pl-10  md:pr-10
  //  lg:p-10  lg:px-10  lg:py-10  lg:pt-10  lg:pb-10  lg:pl-10  lg:pr-10
  //  xl:p-10  xl:px-10  xl:py-10  xl:pt-10  xl:pb-10  xl:pl-10  xl:pr-10
  // 2xl:p-10 2xl:px-10 2xl:py-10 2xl:pt-10 2xl:pb-10 2xl:pl-10 2xl:pr-10

  '11': 11,
  //     p-11     px-11     py-11     pt-11     pb-11     pl-11     pr-11
  //  sm:p-11  sm:px-11  sm:py-11  sm:pt-11  sm:pb-11  sm:pl-11  sm:pr-11
  //  md:p-11  md:px-11  md:py-11  md:pt-11  md:pb-11  md:pl-11  md:pr-11
  //  lg:p-11  lg:px-11  lg:py-11  lg:pt-11  lg:pb-11  lg:pl-11  lg:pr-11
  //  xl:p-11  xl:px-11  xl:py-11  xl:pt-11  xl:pb-11  xl:pl-11  xl:pr-11
  // 2xl:p-11 2xl:px-11 2xl:py-11 2xl:pt-11 2xl:pb-11 2xl:pl-11 2xl:pr-11

  '12': 12,
  //     p-12     px-12     py-12     pt-12     pb-12     pl-12     pr-12
  //  sm:p-12  sm:px-12  sm:py-12  sm:pt-12  sm:pb-12  sm:pl-12  sm:pr-12
  //  md:p-12  md:px-12  md:py-12  md:pt-12  md:pb-12  md:pl-12  md:pr-12
  //  lg:p-12  lg:px-12  lg:py-12  lg:pt-12  lg:pb-12  lg:pl-12  lg:pr-12
  //  xl:p-12  xl:px-12  xl:py-12  xl:pt-12  xl:pb-12  xl:pl-12  xl:pr-12
  // 2xl:p-12 2xl:px-12 2xl:py-12 2xl:pt-12 2xl:pb-12 2xl:pl-12 2xl:pr-12

  '14': 14,
  //     p-14     px-14     py-14     pt-14     pb-14     pl-14     pr-14
  //  sm:p-14  sm:px-14  sm:py-14  sm:pt-14  sm:pb-14  sm:pl-14  sm:pr-14
  //  md:p-14  md:px-14  md:py-14  md:pt-14  md:pb-14  md:pl-14  md:pr-14
  //  lg:p-14  lg:px-14  lg:py-14  lg:pt-14  lg:pb-14  lg:pl-14  lg:pr-14
  //  xl:p-14  xl:px-14  xl:py-14  xl:pt-14  xl:pb-14  xl:pl-14  xl:pr-14
  // 2xl:p-14 2xl:px-14 2xl:py-14 2xl:pt-14 2xl:pb-14 2xl:pl-14 2xl:pr-14

  '16': 16,
  //     p-16     px-16     py-16     pt-16     pb-16     pl-16     pr-16
  //  sm:p-16  sm:px-16  sm:py-16  sm:pt-16  sm:pb-16  sm:pl-16  sm:pr-16
  //  md:p-16  md:px-16  md:py-16  md:pt-16  md:pb-16  md:pl-16  md:pr-16
  //  lg:p-16  lg:px-16  lg:py-16  lg:pt-16  lg:pb-16  lg:pl-16  lg:pr-16
  //  xl:p-16  xl:px-16  xl:py-16  xl:pt-16  xl:pb-16  xl:pl-16  xl:pr-16
  // 2xl:p-16 2xl:px-16 2xl:py-16 2xl:pt-16 2xl:pb-16 2xl:pl-16 2xl:pr-16

  '20': 20,
  //     p-20     px-20     py-20     pt-20     pb-20     pl-20     pr-20
  //  sm:p-20  sm:px-20  sm:py-20  sm:pt-20  sm:pb-20  sm:pl-20  sm:pr-20
  //  md:p-20  md:px-20  md:py-20  md:pt-20  md:pb-20  md:pl-20  md:pr-20
  //  lg:p-20  lg:px-20  lg:py-20  lg:pt-20  lg:pb-20  lg:pl-20  lg:pr-20
  //  xl:p-20  xl:px-20  xl:py-20  xl:pt-20  xl:pb-20  xl:pl-20  xl:pr-20
  // 2xl:p-20 2xl:px-20 2xl:py-20 2xl:pt-20 2xl:pb-20 2xl:pl-20 2xl:pr-20

  '24': 24,
  //     p-24     px-24     py-24     pt-24     pb-24     pl-24     pr-24
  //  sm:p-24  sm:px-24  sm:py-24  sm:pt-24  sm:pb-24  sm:pl-24  sm:pr-24
  //  md:p-24  md:px-24  md:py-24  md:pt-24  md:pb-24  md:pl-24  md:pr-24
  //  lg:p-24  lg:px-24  lg:py-24  lg:pt-24  lg:pb-24  lg:pl-24  lg:pr-24
  //  xl:p-24  xl:px-24  xl:py-24  xl:pt-24  xl:pb-24  xl:pl-24  xl:pr-24
  // 2xl:p-24 2xl:px-24 2xl:py-24 2xl:pt-24 2xl:pb-24 2xl:pl-24 2xl:pr-24

  '28': 28,
  //     p-28     px-28     py-28     pt-28     pb-28     pl-28     pr-28
  //  sm:p-28  sm:px-28  sm:py-28  sm:pt-28  sm:pb-28  sm:pl-28  sm:pr-28
  //  md:p-28  md:px-28  md:py-28  md:pt-28  md:pb-28  md:pl-28  md:pr-28
  //  lg:p-28  lg:px-28  lg:py-28  lg:pt-28  lg:pb-28  lg:pl-28  lg:pr-28
  //  xl:p-28  xl:px-28  xl:py-28  xl:pt-28  xl:pb-28  xl:pl-28  xl:pr-28
  // 2xl:p-28 2xl:px-28 2xl:py-28 2xl:pt-28 2xl:pb-28 2xl:pl-28 2xl:pr-28

  '32': 32,
  //     p-32     px-32     py-32     pt-32     pb-32     pl-32     pr-32
  //  sm:p-32  sm:px-32  sm:py-32  sm:pt-32  sm:pb-32  sm:pl-32  sm:pr-32
  //  md:p-32  md:px-32  md:py-32  md:pt-32  md:pb-32  md:pl-32  md:pr-32
  //  lg:p-32  lg:px-32  lg:py-32  lg:pt-32  lg:pb-32  lg:pl-32  lg:pr-32
  //  xl:p-32  xl:px-32  xl:py-32  xl:pt-32  xl:pb-32  xl:pl-32  xl:pr-32
  // 2xl:p-32 2xl:px-32 2xl:py-32 2xl:pt-32 2xl:pb-32 2xl:pl-32 2xl:pr-32

  // ALIASES:  Supported padding aliases (legacy)
  'zero': 0, // alias for 0
  'none': 0, // alias for 0
  'xs': 1, // alias for 1
  'sm': 2, // alias for 2
  'md': 4, // alias for 4
  'lg': 8, // alias for 8
  'xl': 16, // alias for 16
  '2xl': 24, // alias for 24
  '3xl': 32, // alias for 32
}
