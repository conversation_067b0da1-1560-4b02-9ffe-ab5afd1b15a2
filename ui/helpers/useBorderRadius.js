import { useMemo } from 'react'
import {
  getResponsiveClass,
  getSideClassesAtBreakpoint,
} from './getResponsiveClasses'

export default function useBorderRadius(borderRadius) {
  return useMemo(() => {
    return getSideClassesAtBreakpoint(borderRadius, getBorderRadiusClasses)
  }, [borderRadius])
}

export function getBorderRadiusClasses(borderRadius = {}, breakpoint = '') {
  const classes = []

  for (const [side, sideValue] of Object.entries(borderRadius)) {
    const equalSides = Object.entries(borderRadius).every(
      ([, value]) => value === sideValue
    )

    const sidePrefix =
      !equalSides && radiusCorners.includes(side) ? `-${side}` : ''

    const radiusValue = radiusSizes[sideValue]

    if (radiusValue) {
      classes.push(
        getResponsiveClass(`rounded${sidePrefix}`, radiusValue, breakpoint)
      )
    }
  }

  return classes.join(' ')
}

const radiusCorners = ['tl', 'tr', 'bl', 'br']

const radiusSizes = {
  'none': 'none',
  // rounded-none xs:rounded-none sm:rounded-none md:rounded-none lg:rounded-none xl:rounded-none 2xl:rounded-none
  // rounded-tl-none xs:rounded-tl-none sm:rounded-tl-none md:rounded-tl-none lg:rounded-tl-none xl:rounded-tl-none 2xl:rounded-tl-none
  // rounded-tr-none xs:rounded-tr-none sm:rounded-tr-none md:rounded-tr-none lg:rounded-tr-none xl:rounded-tr-none 2xl:rounded-tr-none
  // rounded-bl-none xs:rounded-bl-none sm:rounded-bl-none md:rounded-bl-none lg:rounded-bl-none xl:rounded-bl-none 2xl:rounded-bl-none
  // rounded-br-none xs:rounded-br-none sm:rounded-br-none md:rounded-br-none lg:rounded-br-none xl:rounded-br-none 2xl:rounded-br-none

  'xs': 'sm',
  // rounded-sm xs:rounded-sm sm:rounded-sm md:rounded-sm lg:rounded-sm xl:rounded-sm 2xl:rounded-sm
  // rounded-tl-sm xs:rounded-tl-sm sm:rounded-tl-sm md:rounded-tl-sm lg:rounded-tl-sm xl:rounded-tl-sm 2xl:rounded-tl-sm
  // rounded-tr-sm xs:rounded-tr-sm sm:rounded-tr-sm md:rounded-tr-sm lg:rounded-tr-sm xl:rounded-tr-sm 2xl:rounded-tr-sm
  // rounded-bl-sm xs:rounded-bl-sm sm:rounded-bl-sm md:rounded-bl-sm lg:rounded-bl-sm xl:rounded-bl-sm 2xl:rounded-bl-sm
  // rounded-br-sm xs:rounded-br-sm sm:rounded-br-sm md:rounded-br-sm lg:rounded-br-sm xl:rounded-br-sm 2xl:rounded-br-sm

  'sm': '',
  // rounded xs:rounded sm:rounded md:rounded lg:rounded xl:rounded 2xl:rounded 3xl:rounded full:rounded
  // rounded-tl xs:rounded-tl sm:rounded-tl md:rounded-tl lg:rounded-tl xl:rounded-tl 2xl:rounded-tl
  // rounded-tr xs:rounded-tr sm:rounded-tr md:rounded-tr lg:rounded-tr xl:rounded-tr 2xl:rounded-tr
  // rounded-bl xs:rounded-bl sm:rounded-bl md:rounded-bl lg:rounded-bl xl:rounded-bl 2xl:rounded-bl
  // rounded-br xs:rounded-br sm:rounded-br md:rounded-br lg:rounded-br xl:rounded-br 2xl:rounded-br

  'md': 'md',
  // rounded-md xs:rounded-md md:rounded-md md:rounded-md lg:rounded-md xl:rounded-md 2xl:rounded-md
  // rounded-tl-md xs:rounded-tl-md md:rounded-tl-md md:rounded-tl-md lg:rounded-tl-md xl:rounded-tl-md 2xl:rounded-tl-md
  // rounded-tr-md xs:rounded-tr-md md:rounded-tr-md md:rounded-tr-md lg:rounded-tr-md xl:rounded-tr-md 2xl:rounded-tr-md
  // rounded-bl-md xs:rounded-bl-md md:rounded-bl-md md:rounded-bl-md lg:rounded-bl-md xl:rounded-bl-md 2xl:rounded-bl-md
  // rounded-br-md xs:rounded-br-md md:rounded-br-md md:rounded-br-md lg:rounded-br-md xl:rounded-br-md 2xl:rounded-br-md

  'lg': 'lg',
  // rounded-lg xs:rounded-lg md:rounded-lg md:rounded-lg lg:rounded-lg xl:rounded-lg 2xl:rounded-lg
  // rounded-tl-lg xs:rounded-tl-lg md:rounded-tl-lg md:rounded-tl-lg lg:rounded-tl-lg xl:rounded-tl-lg 2xl:rounded-tl-lg
  // rounded-tr-lg xs:rounded-tr-lg md:rounded-tr-lg md:rounded-tr-lg lg:rounded-tr-lg xl:rounded-tr-lg 2xl:rounded-tr-lg
  // rounded-bl-lg xs:rounded-bl-lg md:rounded-bl-lg md:rounded-bl-lg lg:rounded-bl-lg xl:rounded-bl-lg 2xl:rounded-bl-lg
  // rounded-br-lg xs:rounded-br-lg md:rounded-br-lg md:rounded-br-lg lg:rounded-br-lg xl:rounded-br-lg 2xl:rounded-br-lg

  'xl': 'xl',
  // rounded-xl xs:rounded-xl md:rounded-xl md:rounded-xl lg:rounded-xl xl:rounded-xl 2xl:rounded-xl
  // rounded-tl-xl xs:rounded-tl-xl md:rounded-tl-xl md:rounded-tl-xl lg:rounded-tl-xl xl:rounded-tl-xl 2xl:rounded-tl-xl
  // rounded-tr-xl xs:rounded-tr-xl md:rounded-tr-xl md:rounded-tr-xl lg:rounded-tr-xl xl:rounded-tr-xl 2xl:rounded-tr-xl
  // rounded-bl-xl xs:rounded-bl-xl md:rounded-bl-xl md:rounded-bl-xl lg:rounded-bl-xl xl:rounded-bl-xl 2xl:rounded-bl-xl
  // rounded-br-xl xs:rounded-br-xl md:rounded-br-xl md:rounded-br-xl lg:rounded-br-xl xl:rounded-br-xl 2xl:rounded-br-xl

  '2xl': '2xl',
  // rounded-2xl xs:rounded-2xl md:rounded-2xl md:rounded-2xl lg:rounded-2xl xl:rounded-2xl 2xl:rounded-2xl
  // rounded-tl-2xl xs:rounded-tl-2xl md:rounded-tl-2xl md:rounded-tl-2xl lg:rounded-tl-2xl xl:rounded-tl-2xl 2xl:rounded-tl-2xl
  // rounded-tr-2xl xs:rounded-tr-2xl md:rounded-tr-2xl md:rounded-tr-2xl lg:rounded-tr-2xl xl:rounded-tr-2xl 2xl:rounded-tr-2xl
  // rounded-bl-2xl xs:rounded-bl-2xl md:rounded-bl-2xl md:rounded-bl-2xl lg:rounded-bl-2xl xl:rounded-bl-2xl 2xl:rounded-bl-2xl
  // rounded-br-2xl xs:rounded-br-2xl md:rounded-br-2xl md:rounded-br-2xl lg:rounded-br-2xl xl:rounded-br-2xl 2xl:rounded-br-2xl

  '3xl': '3xl',
  // rounded-3xl xs:rounded-3xl md:rounded-3xl md:rounded-3xl lg:rounded-3xl xl:rounded-3xl 2xl:rounded-3xl
  // rounded-tl-3xl xs:rounded-tl-3xl md:rounded-tl-3xl md:rounded-tl-3xl lg:rounded-tl-3xl xl:rounded-tl-3xl 2xl:rounded-tl-3xl
  // rounded-tr-3xl xs:rounded-tr-3xl md:rounded-tr-3xl md:rounded-tr-3xl lg:rounded-tr-3xl xl:rounded-tr-3xl 2xl:rounded-tr-3xl
  // rounded-bl-3xl xs:rounded-bl-3xl md:rounded-bl-3xl md:rounded-bl-3xl lg:rounded-bl-3xl xl:rounded-bl-3xl 2xl:rounded-bl-3xl
  // rounded-br-3xl xs:rounded-br-3xl md:rounded-br-3xl md:rounded-br-3xl lg:rounded-br-3xl xl:rounded-br-3xl 2xl:rounded-br-3xl

  'full': 'full',
  // rounded-full xs:rounded-full md:rounded-full md:rounded-full lg:rounded-full xl:rounded-full 2xl:rounded-full
  // rounded-tl-full xs:rounded-tl-full md:rounded-tl-full md:rounded-tl-full lg:rounded-tl-full xl:rounded-tl-full 2xl:rounded-tl-full
  // rounded-tr-full xs:rounded-tr-full md:rounded-tr-full md:rounded-tr-full lg:rounded-tr-full xl:rounded-tr-full 2xl:rounded-tr-full
  // rounded-bl-full xs:rounded-bl-full md:rounded-bl-full md:rounded-bl-full lg:rounded-bl-full xl:rounded-bl-full 2xl:rounded-bl-full
  // rounded-br-full xs:rounded-br-full md:rounded-br-full md:rounded-br-full lg:rounded-br-full xl:rounded-br-full 2xl:rounded-br-full
}
