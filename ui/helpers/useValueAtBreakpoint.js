import { useMemo } from 'react'

import { breakpointKeys } from 'utils/media'
import useBreakpoint from './useBreakpoint'

/**
 * Returns a value for a specific breakpoint, and fall back to a previous breakpoint if value is not set for the current one, or the default value when none of the previous attempts has yieled one.
 *
 * @param {import('./getResponsiveClasses').ResponsiveValue} data an object with breakpoints as keys where we want to fetch data from
 * @param {string|number|boolean} defaultValue the ultimate fall back value
 * @returns `object`
 */
export function useValueAtBreakpoint(data, defaultValue) {
  const breakpoint = useBreakpoint()

  return useMemo(() => {
    if (!data || typeof data !== 'object' || !breakpoint) return defaultValue

    if (data[breakpoint]) return data[breakpoint]

    const breakpointIndex = breakpointKeys.indexOf(breakpoint)
    const prevBreakpoints = breakpointKeys.slice(0, breakpointIndex).reverse()

    let value = defaultValue

    for (const breakpointKey of prevBreakpoints) {
      if (data[breakpointKey]) {
        value = data[breakpointKey]
        break
      }
    }

    return value
  }, [data, breakpoint, defaultValue])
}
