import { useResponsiveClasses } from './useResponsiveClasses'

import { gapOptions } from './useGap'

const spacingOptions = {
  ...gapOptions,

  // Legacy fallbacks
  'zero': '0', // gap-0 sm:gap-0 md:gap-0 lg:gap-0 xl:gap-0 2xl:gap-0
  'xs': '1', // gap-1 sm:gap-1 md:gap-1 lg:gap-1 xl:gap-1 2xl:gap-1
  'sm': '2', // gap-2 sm:gap-2 md:gap-2 lg:gap-2 xl:gap-2 2xl:gap-2
  'md': '4', // gap-4 sm:gap-4 md:gap-4 lg:gap-4 xl:gap-4 2xl:gap-4
  'lg': '8', // gap-8 sm:gap-8 md:gap-8 lg:gap-8 xl:gap-8 2xl:gap-8
  'xl': '16', // gap-16 sm:gap-16 md:gap-16 lg:gap-16 xl:gap-16 2xl:gap-16
  '2xl': '24', // gap-24 sm:gap-24 md:gap-24 lg:gap-24 xl:gap-24 2xl:gap-24
  '3xl': '32', // gap-32 sm:gap-32 md:gap-32 lg:gap-32 xl:gap-32 2xl:gap-32
}

export default function useSpacing(spacing) {
  return useResponsiveClasses('gap', spacingOptions, spacing)
}
