import { isObject, isSet } from 'utils/types'
import { useMemo } from 'react'
import { breakpointKeys } from 'utils/media'
import { getResponsiveClass } from './getResponsiveClasses'

export function useResponsiveClasses(cssClass, options, value) {
  return useMemo(() => {
    if (!isObject(value)) return ''

    const classes = breakpointKeys.reduce((acc, breakpoint) => {
      const valueAtBreakpoint = value[breakpoint]

      if (!valueAtBreakpoint) {
        return acc
      }

      const classValue = Array.isArray(options)
        ? options.find(option => option === valueAtBreakpoint)
        : options[valueAtBreakpoint]

      if (isSet(classValue)) {
        return [...acc, getResponsiveClass(cssClass, classValue, breakpoint)]
      }

      return acc
    }, [])

    return classes.join(' ')
  }, [cssClass, options, value])
}
