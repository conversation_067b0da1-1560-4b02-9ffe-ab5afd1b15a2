import { useResponsiveClasses } from './useResponsiveClasses'

export const justifySelfOptions = [
  'auto', // justify-self-auto sm:justify-self-auto md:justify-self-auto lg:justify-self-auto xl:justify-self-auto 2xl:justify-self-auto 3xl:justify-self-auto
  'start', // justify-self-start sm:justify-self-start md:justify-self-start lg:justify-self-start xl:justify-self-start 2xl:justify-self-start 3xl:justify-self-start
  'center', // justify-self-center sm:justify-self-center md:justify-self-center lg:justify-self-center xl:justify-self-center 2xl:justify-self-center 3xl:justify-self-center
  'end', // justify-self-end sm:justify-self-end md:justify-self-end lg:justify-self-end xl:justify-self-end 2xl:justify-self-end 3xl:justify-self-end
  'stretch', // justify-self-stretch sm:justify-self-stretch md:justify-self-stretch lg:justify-self-stretch xl:justify-self-stretch 2xl:justify-self-stretch 3xl:justify-self-stretch
]

export function useJustifySelf(justifySelf) {
  return useResponsiveClasses(
    'justify-self',
    justifySelfOptions,
    justifySelf,
  )
}
