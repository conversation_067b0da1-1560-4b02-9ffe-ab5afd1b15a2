import { useMemo } from 'react'
import { removeUndefined } from 'utils/objects'
import { useValueAtBreakpoint } from './useValueAtBreakpoint'

/**
 * @typedef {Object} PositionOffset The position offset of the element.
 * @property {Object} top The top offset
 * @property {Object} right The right offset.
 * @property {Object} bottom The bottom offset.
 * @property {Object} left The left offset.
 */

/**
 * Get the position offset of the element.
 * @param {Object} position The position of the element.
 * @param {PositionOffset} options The position offset options.
 * @param {Object} options.top The top offset.
 * @param {Object} options.right The right offset.
 * @param {Object} options.bottom The bottom offset.
 * @param {Object} options.left The left offset.
 * @returns {Object} The position offset of the element.
 */
export function usePositionOffset(position, { top, right, bottom, left } = {}) {
  const positionValue = useValueAtBreakpoint(position, 'static')

  const offsetTop = useValueAtBreakpoint(top, undefined)
  const offsetRight = useValueAtBreakpoint(right, undefined)
  const offsetBottom = useValueAtBreakpoint(bottom, undefined)
  const offsetLeft = useValueAtBreakpoint(left, undefined)

  return useMemo(() => {
    if (positionValue === 'static') return {}

    return removeUndefined({
      top: offsetTop,
      right: offsetRight,
      bottom: offsetBottom,
      left: offsetLeft,
    })
  }, [positionValue, offsetTop, offsetRight, offsetBottom, offsetLeft])
}
