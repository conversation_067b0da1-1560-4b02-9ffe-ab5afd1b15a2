export default function useTextClasses(textSettings = {}) {
  return getTextClasses(textSettings)
}

export function getTextClasses({
  color,
  size,
  family,
  style,
  align,
  textCase,
  weight,
} = {}) {
  const classNames = []

  if (color) {
    classNames.push(`text-${color}`)
  }

  if (size) {
    classNames.push(`text-${size}`)
  }

  if (style === 'italic') {
    classNames.push('italic')
  }

  if (align) {
    classNames.push(`text-${align}`)
  }

  if (textCase) {
    classNames.push(textCase)
  }

  if (weight) {
    classNames.push(`font-${weight}`)
  }

  if (family) {
    classNames.push(`font-${family}`)
  }

  return classNames.join(' ')
}
