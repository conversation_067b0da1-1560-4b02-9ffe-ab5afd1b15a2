import { useMemo } from 'react'
import { getResponsiveClasses } from './getResponsiveClasses'

export function useFontFamily(family, defaultFamily = 'body') {
  return useMemo(() => {
    return getResponsiveClasses('font', family, fontFamilies, defaultFamily)
  }, [defaultFamily, family])
}

const fontFamilies = [
  'body', // font-body sm:font-body md:font-body lg:font-body xl:font-body 2xl:font-body
  'heading', // font-heading sm:font-heading md:font-heading lg:font-heading xl:font-heading 2xl:font-heading
  'display', // font-display sm:font-display md:font-display lg:font-display xl:font-display 2xl:font-display
  'mono', // font-mono sm:font-mono md:font-mono lg:font-mono xl:font-mono 2xl:font-mono
]
