import get from 'lodash/get'

import { usePageContext } from 'components/PageProvider'
import { useBlockResources } from 'components/content/blocks/BlockResourceSourceProvider'
import { formatDate, useDatetimeLocale } from 'utils/datetime'

export function useFieldResource(source, fallbackValue) {
  const locale = useDatetimeLocale()

  const blockResources = useBlockResources() ?? {}
  const { resources } = usePageContext()

  const blockField = get(blockResources, source?.value)
  const resourceField = get(resources, source?.value)

  let sourceField = blockField ?? resourceField

  if (source?.enabled && source?.type === 'date') {
    sourceField = formatDate(sourceField, source?.format, {
      locale,
    })
  }

  return source?.enabled ? sourceField : fallbackValue
}

export default useFieldResource
