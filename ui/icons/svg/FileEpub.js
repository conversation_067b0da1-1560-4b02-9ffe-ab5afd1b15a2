import React from 'react'

import SvgWrap from '../SvgWrap'

export default function FileEpub(props) {
  return (
    <SvgWrap viewBox="0 0 512 512" width="24" height="24" {...props}>
      <path d="m 64,464 h 48 v 48 H 64 C 28.7,512 0,483.3 0,448 V 64 C 0,28.7 28.7,0 64,0 h 165.5 c 17,0 33.3,6.7 45.3,18.7 l 90.5,90.5 c 12,12 18.7,28.3 18.7,45.3 V 304 H 336 V 160 h -80 c -17.7,0 -32,-14.3 -32,-32 V 48 H 64 c -8.8,0 -16,7.2 -16,16 v 384 c 0,8.8 7.2,16 16,16 z" />
      <path d="m 197.58984,371.11363 h -46.54913 v 40.28014 h 42.53618 c 3.43247,0 5.98158,1.15619 7.64733,3.46857 1.71625,2.23778 2.57437,5.22148 2.57437,8.95111 0,3.72965 -0.83288,6.78796 -2.49864,9.17494 -1.66577,2.31238 -4.24012,3.46857 -7.72306,3.46857 h -42.53618 v 46.65781 h 48.29061 c 3.53343,0 6.1835,1.23079 7.95021,3.69236 1.81719,2.38696 2.72578,5.59444 2.72578,9.62245 0,3.87884 -0.90859,7.04904 -2.72578,9.51059 -1.76671,2.38697 -4.41678,3.58046 -7.95021,3.58046 h -61.10299 c -4.89631,0 -8.42973,-1.60375 -10.60027,-4.81124 -2.12006,-3.20749 -3.18009,-8.39169 -3.18009,-15.5526 V 365.85483 c 0,-4.77395 0.47954,-8.65277 1.43863,-11.63648 0.95907,-3.0583 2.44816,-5.25879 4.46727,-6.60146 2.06957,-1.41727 4.69439,-2.1259 7.87446,-2.1259 h 59.36151 c 3.5839,0 6.23397,1.19349 7.95022,3.58047 1.76671,2.31236 2.65006,5.37066 2.65006,9.1749 0,3.87884 -0.88335,7.01174 -2.65006,9.3987 -1.71625,2.31238 -4.36632,3.46857 -7.95022,3.46857 z m 62.94954,74.51823 h -18.72889 v 46.43406 c 0,6.63875 -1.06003,11.67377 -3.18008,15.10505 -2.12006,3.43127 -6.66128,5.1469 -9.89182,5.1469 -3.38201,0 -6.1078,-1.67834 -8.17737,-5.03503 -2.06959,-3.43126 -3.10438,-8.42897 -3.10438,-14.99314 V 365.85483 c 0,-7.31009 1.13575,-12.53159 3.40726,-15.66449 2.27148,-3.1329 5.88061,-4.69935 10.8274,-4.69935 h 28.84788 c 8.53071,0 15.09278,0.96971 19.68622,2.90913 4.54297,1.8648 8.45498,4.9604 11.73602,9.2868 3.33151,4.32637 5.85538,9.62246 7.57161,15.88826 1.71624,6.26582 2.57436,13.31484 2.57436,21.14707 0,16.70879 -3.48294,29.38958 -10.44883,38.04235 -6.96591,8.57817 -17.33903,12.86726 -31.11938,12.86726 z m -5.45157,-75.18957 h -13.27732 v 50.12639 h 13.27732 c 5.30013,0 9.71691,-0.82052 13.25034,-2.46156 3.5839,-1.64106 6.30969,-4.3264 8.17737,-8.05602 1.86767,-3.72965 2.8015,-8.61549 2.8015,-14.65752 0,-7.23551 -1.43861,-13.12834 -4.31584,-17.6785 -3.23055,-4.84853 -9.86834,-7.27279 -19.91337,-7.27279 z m 55.52936,71.27345 v -78.88192 c 0,-6.71335 1.00954,-11.74836 3.02863,-15.10503 2.06957,-3.35669 4.77012,-5.03504 8.10165,-5.03504 3.48293,0 8.84624,1.67835 10.86536,5.03504 2.06957,3.35667 3.10436,8.39168 3.10436,15.10503 v 80.67215 c 0,9.17493 0.68144,16.85799 2.04434,23.04918 1.41337,6.11662 1.27448,10.89056 4.80789,14.32182 3.53343,3.35669 8.48022,5.03504 14.84038,5.03504 8.78307,0 12.7527,-3.43127 16.38708,-10.29382 3.63437,-6.93714 5.45156,-17.41744 5.45156,-31.4409 v -81.34347 c 0,-6.78795 1.00956,-11.82296 3.02867,-15.10503 2.01911,-3.35669 6.95875,-5.03504 10.34073,-5.03504 3.38199,0 6.10777,1.67835 8.17734,5.03504 2.12006,3.28207 3.18009,8.31708 3.18009,15.10503 v 78.88192 c 0,12.82999 -0.85811,23.53406 -2.57434,32.11223 -1.66577,8.57818 -4.84585,16.11205 -9.54024,22.60161 -4.03821,5.51987 -8.73262,9.5479 -14.08324,12.08407 -5.35061,2.53615 -11.60981,3.80422 -18.77762,3.80422 -8.53069,0 -15.87516,-1.34267 -22.03341,-4.02802 -6.15825,-2.75993 -11.18076,-6.97442 -15.06752,-12.64347 -3.88677,-5.74366 -6.73875,-13.05376 -8.55593,-21.9303 -1.81719,-8.95113 -2.72578,-19.61791 -2.72578,-32.00034 z m 154.64186,67.80489 h -33.92086 c -4.89631,0 -8.40449,-1.60375 -10.52454,-4.81124 -2.06957,-3.28209 -3.10436,-8.46629 -3.10436,-15.5526 V 365.85483 c 0,-7.2355 1.06002,-12.4197 3.18007,-15.5526 2.17054,-3.20749 5.65348,-4.81124 10.44883,-4.81124 h 35.96521 c 5.30014,0 9.89359,0.48485 13.78036,1.45456 3.88675,0.96971 7.3697,2.83453 10.44883,5.59445 2.62482,2.31237 4.94679,5.25879 6.9659,8.83924 2.01909,3.50589 3.55865,7.42202 4.6187,11.74839 1.06002,4.2518 1.59003,8.76467 1.59003,13.5386 0,16.41043 -5.55253,28.41988 -16.65758,36.02834 14.588,6.86255 21.882,20.21467 21.882,40.05636 0,9.17493 -1.59005,17.45473 -4.77014,24.8394 -3.18007,7.31011 -7.47065,12.7181 -12.87175,16.22397 -3.38199,2.08859 -7.26875,3.58044 -11.66029,4.47555 -4.39154,0.82052 -9.51501,1.23078 -15.37041,1.23078 z m -4.27802,-72.83991 h -18.54493 v 47.88861 h 21.91437 c 15.19371,0 20.17828,-8.09332 20.17828,-24.27995 0,-8.27981 -1.96862,-14.28454 -5.90586,-18.01419 -3.93724,-3.72965 -9.81786,-5.59447 -17.64186,-5.59447 z m -18.54493,-66.23843 v 42.40604 h 16.48978 c 5.60299,0 9.91882,-0.78323 12.94749,-2.34969 3.07913,-1.56643 5.42633,-4.55015 7.0416,-8.95114 1.26193,-3.13289 1.8929,-6.63875 1.8929,-10.51758 0,-8.27981 -1.99386,-13.76239 -5.98159,-16.44773 -3.98772,-2.75993 -10.07025,-4.1399 -18.2476,-4.1399 z" />
    </SvgWrap>
  )
}
