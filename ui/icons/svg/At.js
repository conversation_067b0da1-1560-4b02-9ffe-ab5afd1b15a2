import React from 'react'

import SvgWrap from '../SvgWrap'

export default function At(props) {
  return (
    <SvgWrap
      viewBox="0 0 512 512"
      {...props}
      className={`scale-90 ${props.className ?? ''}`}
    >
      <path
        fillRule="evenodd"
        d="M263.4 16.12C198.2 14.06 136.2 38.14 89.31 83.67C42.03 129.5 16 191.2 16 257.2c0 118.6 87.89 221.1 204.5 238.5C221.7 495.9 222.8 496 224 496c11.69 0 21.92-8.547 23.7-20.45c1.953-13.11-7.078-25.33-20.19-27.28C134.3 434.3 64 352.2 64 257.2c0-52.94 20.86-102.3 58.73-139.1c37.53-36.41 86.25-55.83 139.2-54.03C364.5 67.2 448 157.9 448 266.3v19.05c0 24.45-19.73 44.36-44 44.36S360 309.8 360 285.3V168.7c0-13.25-10.75-23.1-24-23.1c-11.92 0-21.38 8.855-23.24 20.25C294.4 151.9 272.2 144 248 144c-61.75 0-112 50.25-112 111.1s50.25 111.1 112 111.1c30.75 0 58.62-12.48 78.88-32.62c16.41 25.4 44.77 42.32 77.12 42.32c50.73 0 92-41.44 92-92.36V266.3C496 132.2 391.6 19.1 263.4 16.12zM248 320c-35.3 0-64-28.7-64-63.1c0-35.29 28.7-63.1 64-63.1s64 28.7 64 63.1C312 291.3 283.3 320 248 320z"
      />
    </SvgWrap>
  )
}
