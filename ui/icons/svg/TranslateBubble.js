import React from 'react'

import SvgWrap from '../SvgWrap'

export default function TranslateBubble(props) {
  return (
    <SvgWrap viewBox="0 0 40 40" fill="none" {...props}>
      <path
        d="M22.2978 12.6117H10.4572"
        stroke="#3C819C"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></path>
      <path
        d="M16.3776 10.6187V12.6119"
        stroke="#3C819C"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></path>
      <path
        d="M19.3436 12.588C19.3436 17.6897 15.3572 21.8185 10.4453 21.8185"
        stroke="#3C819C"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></path>
      <path
        d="M16.9114 18.8881C17.7116 19.9387 18.6954 20.765 19.803 21.2686"
        stroke="#3C819C"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></path>
      <path
        d="M20 36.6667C29.2048 36.6667 36.6667 29.2048 36.6667 20C36.6667 10.7953 29.2048 3.33337 20 3.33337C10.7953 3.33337 3.33337 10.7953 3.33337 20C3.33337 29.2048 10.7953 36.6667 20 36.6667Z"
        stroke="#3C819C"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></path>
      <mask
        id="mask0_911_1690"
        maskUnits="userSpaceOnUse"
        x="20"
        y="22"
        width="9"
        height="8"
        style={{ maskType: 'alpha' }}
      >
        <rect
          x="20"
          y="22"
          width="8.94872"
          height="7.92251"
          fill="#D9D9D9"
        ></rect>
      </mask>
      <g mask="url(#mask0_911_1690)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M25.273 23.5565L24.6226 22.3837L23.9638 23.5518L20.3374 29.9816L21.6439 30.7185L23.0787 28.1746L26.1186 28.1746L27.5266 30.7137L28.8384 29.9863L25.273 23.5565ZM25.2868 26.6746L24.6115 25.4568L23.9247 26.6746L25.2868 26.6746Z"
          fill="#3C819C"
        ></path>
      </g>
    </SvgWrap>
  )
}
