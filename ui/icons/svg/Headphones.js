import React from 'react'

import SvgWrap from '../SvgWrap'

export default function Headphones(props) {
  return (
    <SvgWrap viewBox="0 0 226 223" {...props}>
      <g transform="translate(0.000000,223.000000) scale(0.100000,-0.100000)">
        <path
          xmlns="http://www.w3.org/2000/svg"
          d="M1009 2220 c-138 -16 -245 -49 -389 -121 -261 -130 -461 -361 -558 -644 -50 -148 -54 -189 -59 -585 -3 -230 -1 -404 6 -449 28 -208 203 -383 409 -412 157 -22 288 36 371 164 56 85 64 144 59 440 -3 237 -4 243 -30 299 -37 80 -105 149 -181 184 -54 25 -73 29 -152 28 -95 0 -172 -18 -232 -54 -19 -11 -36 -20 -39 -20 -12 0 11 203 32 288 73 292 334 559 630 644 69 20 101 23 254 23 203 -1 252 -10 397 -80 223 -108 420 -338 482 -565 20 -73 48 -296 38 -306 -2 -3 -20 4 -39 16 -61 36 -138 54 -233 54 -79 1 -98 -3 -152 -28 -76 -35 -144 -104 -181 -184 -27 -56 -27 -61 -30 -299 -5 -298 4 -359 59 -442 188 -282 620 -192 761 157 23 56 23 63 23 477 0 450 -4 494 -57 650 -101 295 -309 527 -591 660 -50 24 -112 50 -137 58 -127 42 -324 62 -461 47z m-449 -1321 c70 -40 75 -57 78 -299 2 -118 1 -234 -3 -258 -8 -52 -52 -106 -100 -122 -84 -28 -229 36 -279 123 -38 65 -48 133 -37 278 7 108 12 131 34 166 70 111 215 164 307 112z m1296 6 c58 -17 116 -62 151 -118 22 -35 27 -58 34 -166 14 -196 -9 -275 -100 -344 -60 -46 -93 -58 -163 -60 -61 -2 -93 14 -132 67 -20 26 -21 41 -21 279 0 283 2 294 74 336 43 24 89 26 157 6z"
        />
      </g>
    </SvgWrap>
  )
}
