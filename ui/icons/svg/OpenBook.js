import React from 'react'

import SvgWrap from '../SvgWrap'

export default function ClosedBook(props) {
  return (
    <SvgWrap
      width="34"
      height="34"
      viewBox="0 0 600 600"
      preserveAspectRatio="xMidYMid meet"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g
        transform="translate(0.000000,600.000000) scale(0.100000,-0.100000)"
        fill="currentColor"
        stroke="none"
      >
        <path
          d="M1420 3046 l0 -1366 483 0 c267 0 513 -5 552 -10 190 -28 352 -172
399 -356 9 -34 16 -76 16 -93 l0 -31 143 0 143 0 13 75 c30 178 159 329 334
388 59 20 84 21 590 25 l527 3 -2 1362 -3 1362 -540 0 c-485 0 -547 -2 -604
-18 -152 -41 -282 -119 -385 -228 l-67 -70 -72 73 c-80 82 -155 135 -253 179
-135 61 -139 62 -731 66 l-543 5 0 -1366z m1035 1064 c190 -28 352 -172 399
-357 14 -54 16 -170 14 -1007 l-3 -946 -35 24 c-63 43 -173 94 -250 117 -71
21 -97 22 -472 26 l-398 5 0 1074 0 1074 338 0 c185 0 369 -5 407 -10z m1873
-1067 l2 -1071 -397 -4 -398 -4 -88 -27 c-95 -29 -167 -63 -234 -108 -23 -16
-44 -29 -47 -29 -3 0 -6 422 -6 938 0 1030 -1 1006 61 1123 40 75 123 158 198
197 106 57 138 61 541 59 l365 -2 3 -1072z"
        />
      </g>
    </SvgWrap>
  )
}
