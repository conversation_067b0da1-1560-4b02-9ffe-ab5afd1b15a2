import dynamic from 'next/dynamic'

export default {
  'app-store': dynamic(() => import('./AppStore')),
  'arrow': dynamic(() => import('./Arrow')),
  'arrow-rotate-right': dynamic(() => import('./ArrowRotateRight')),
  'article': dynamic(() => import('./Article')),
  'asterisk': dynamic(() => import('./Asterisk')),
  'at': dynamic(() => import('./At')),
  'audio': dynamic(() => import('./Audio')),
  'backward-step': dynamic(() => import('./BackwardStep')),
  'bars': dynamic(() => import('./Bars')),
  'book': dynamic(() => import('./Book')),
  'closed-book': dynamic(() => import('./ClosedBook')),
  'open-book': dynamic(() => import('./OpenBook')),
  'calendar': dynamic(() => import('./Calendar')),
  'check': dynamic(() => import('./Check')),
  'caret-up': dynamic(() => import('./CaretUp')),
  'chevron-down': dynamic(() => import('./ChevronDown')),
  'chevron-left': dynamic(() => import('./ChevronLeft')),
  'chevron-right': dynamic(() => import('./ChevronRight')),
  'chevron-up': dynamic(() => import('./ChevronUp')),
  'download': dynamic(() => import('./Download')),
  'download-arrow': dynamic(() => import('./DownloadArrow')),
  'download-arrow-cloud': dynamic(() => import('./DownloadArrowCloud')),
  'email': dynamic(() => import('./Email')),
  'envelope': dynamic(() => import('./Envelope')),
  'expand-arrows': dynamic(() => import('./ExpandArrows')),
  'external-link': dynamic(() => import('./ExternalLink')),
  'facebook': dynamic(() => import('./Facebook')),
  'file-invoice': dynamic(() => import('./FileInvoice')),
  'file-pdf': dynamic(() => import('./FilePdf')),
  'file-epub': dynamic(() => import('./FileEpub')),
  'forward-step': dynamic(() => import('./ForwardStep')),
  'globe': dynamic(() => import('./Globe')),
  'google-play': dynamic(() => import('./GooglePlay')),
  'grid': dynamic(() => import('./Grid')),
  'headphones': dynamic(() => import('./Headphones')),
  'instagram': dynamic(() => import('./Instagram')),
  'language': dynamic(() => import('./Language')),
  'link': dynamic(() => import('./Link')),
  'map-marker': dynamic(() => import('./MapMarker')),
  'page': dynamic(() => import('./Page')),
  'pause-solid': dynamic(() => import('./PauseSolid')),
  'play': dynamic(() => import('./Play')),
  'play-circle': dynamic(() => import('./PlayCircle')),
  'play-solid': dynamic(() => import('./PlaySolid')),
  'podcast': dynamic(() => import('./Podcast')),
  'sda-logo-circular': dynamic(() => import('./SDALogoCircular')),
  'search': dynamic(() => import('./Search')),
  'share': dynamic(() => import('./Share')),
  'sound-cloud': dynamic(() => import('./SoundCloud')),
  'speech-bubble': dynamic(() => import('./SpeechBubble')),
  'spinner': dynamic(() => import('./Spinner')),
  'spinner-third': dynamic(() => import('./SpinnerThird')),
  'spotify': dynamic(() => import('./Spotify')),
  'stack': dynamic(() => import('./Stack')),
  'telegram': dynamic(() => import('./Telegram')),
  'times': dynamic(() => import('./Times')),
  'translate-bubble': dynamic(() => import('./TranslateBubble')),
  'tv': dynamic(() => import('./Tv')),
  'twitter': dynamic(() => import('./Twitter')),
  'user': dynamic(() => import('./User')),
  'video': dynamic(() => import('./Video')),
  'vimeo': dynamic(() => import('./Vimeo')),
  'volume': dynamic(() => import('./Volume')),
  'volume-xmark': dynamic(() => import('./VolumeXMark')),
  'whatsapp': dynamic(() => import('./Whatsapp')),
  'youtube': dynamic(() => import('./Youtube')),
  'xmark': dynamic(() => import('./Xmark')),
}
