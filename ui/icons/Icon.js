import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import icons from './svg/index'

const Clickable = dynamic(() => import('ui/helpers/Clickable'))

export default function Icon({
  className = '',
  iconClassName = '',
  name,
  size,
  onClick,
  boxed,
  style,
}) {
  // if no name is provided, return nothing
  if (!name) return null

  const boxClass = boxed ? `rounded p-2` : ''

  if (typeof name !== 'string') {
    return (
      <Clickable
        className={`item-center flex justify-center ${className} ${boxClass}`}
        onClick={onClick}
        style={style}
      >
        {name}
      </Clickable>
    )
  }

  // if name is a string (icon name), get the icon component, otherwise expect it to be a component (a "node" element)
  const IconComponent = icons[name]
  if (!IconComponent) return null

  return (
    <Clickable
      className={`item-center flex justify-center ${className} ${boxClass}`}
      onClick={onClick}
      style={style}
    >
      <IconComponent className={iconClassName} size={size} />
    </Clickable>
  )
}

Icon.propTypes = {
  className: PropTypes.string,
  iconClassName: PropTypes.string,
  boxed: PropTypes.bool,
  name: PropTypes.node.isRequired,
  onClick: PropTypes.func,
  size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  style: PropTypes.object,
}
