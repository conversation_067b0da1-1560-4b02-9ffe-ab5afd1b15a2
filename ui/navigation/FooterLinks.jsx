import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Link = dynamic(() => import('ui/navigation/Link'))

export default function FooterLinks({ className = '', items = [], title }) {
  return (
    <div className={className}>
      <h3 className="mb-2 font-bold uppercase ltr:text-left rtl:text-right">
        {title}
      </h3>
      {items?.length > 0 && (
        <ul className="columns-2 space-y-2 leading-relaxed sm:space-y-0 md:columns-auto">
          {items.map((item, key) => (
            <FooterLink key={`footer-item-${key}`} {...item} />
          ))}
        </ul>
      )}
    </div>
  )
}
FooterLinks.propTypes = {
  className: PropTypes.string,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      url: PropTypes.string,
      external: PropTypes.bool,
    })
  ),
  title: PropTypes.node,
}

export function FooterLink({ className = '', label, onClick, url }) {
  return (
    <li className={className}>
      {url?.match(/^(http|https|ftp):\/\/\w/) ? (
        <a href={url} rel="noopener noreferrer" target="_blank">
          {label}
        </a>
      ) : (
        <Link to={url} onClick={onClick}>
          {label}
        </Link>
      )}
    </li>
  )
}
FooterLink.propTypes = {
  className: PropTypes.string,
  label: PropTypes.string,
  onClick: PropTypes.func,
  url: PropTypes.string,
}
