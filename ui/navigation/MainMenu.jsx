import React, { useCallback, useMemo } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'

import { useCloseNavOnClick } from 'ui/navigation/Navigation'
import useToggle from 'ui/helpers/useToggle'

const Badge = dynamic(() => import('ui/feedback/Badge'))
const Icon = dynamic(() => import('ui/icons/Icon'))

export default function MainMenu({ className = '', children }) {
  return (
    <ul id="main-menu" role="navigation" className={className}>
      {children}
    </ul>
  )
}
MainMenu.propTypes = {
  className: PropTypes.string,
  children: PropTypes.node,
}

export function ItemLevel1({
  children,
  className = '',
  count,
  label,
  icon,
  url,
  onClick,
  onClose,
}) {
  const hasChildren = !!children
  const [open, onToggle, setOpen] = useToggle(false)

  const handleHover = useCallback(
    e => {
      setOpen(e.type === 'mouseenter')
    },
    [setOpen]
  )

  const handleClose = useCallback(() => {
    setOpen(false)
    if (typeof onClose === 'function') onClose()
  }, [setOpen, onClose])

  return (
    <li
      className={`w-full select-none px-6 py-2 lg:w-auto lg:px-3 xl:px-4 ${className}`}
      onMouseLeave={handleHover}
      onMouseEnter={handleHover}
    >
      <span className="flex flex-row items-center justify-between space-x-1 rtl:space-x-reverse">
        <ItemLink
          className="text-2xl font-bold lg:text-base"
          count={count}
          href={url}
          onClick={onClick}
          icon={icon}
          onClose={handleClose}
        >
          {label}
        </ItemLink>

        {hasChildren && (
          <button onClick={onToggle} className="px-1">
            <Icon
              className={`text-sm transition-transform duration-300 ease-in-out md:block ${
                open ? '-rotate-180 text-color1-700' : 'text-gray-500'
              }`}
              name="chevron-down"
            />
          </button>
        )}
      </span>

      {hasChildren && (
        <div
          className={
            open ? 'left-10 right-10 mt-2 lg:absolute lg:mt-0' : 'hidden'
          }
        >
          <div className="lg:border-b lg:pt-9" />
          <ul className="bg-white lg:flex lg:flex-row lg:flex-wrap lg:border-b-4 lg:border-color1-500 lg:px-6 lg:py-6 xl:px-12">
            {React.Children.map(children, child =>
              React.cloneElement(child, { onClose: handleClose })
            )}
            {/* {children} */}
          </ul>
        </div>
      )}
    </li>
  )
}
ItemLevel1.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  count: PropTypes.number,
  icon: PropTypes.string,
  label: PropTypes.node,
  onClick: PropTypes.func,
  onClose: PropTypes.func,
  url: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
}

export function ItemLevel2({
  children,
  className = '',
  count,
  label,
  url,
  onClick,
  onClose,
}) {
  return (
    <li
      className={`w-full select-none space-y-4 px-6 py-2 lg:w-auto lg:px-3 xl:px-5 ${className}`}
    >
      <span className="flex flex-row items-center justify-between space-x-1 rtl:space-x-reverse">
        <ItemLink
          className="text-lg font-semibold lg:text-base lg:uppercase"
          onClick={onClick}
          count={count}
          onClose={onClose}
          href={url}
        >
          {label}
        </ItemLink>
      </span>
      {children && (
        <ul className="space-y-1">
          {React.Children.map(children, child =>
            React.cloneElement(child, { onClose })
          )}
        </ul>
      )}
    </li>
  )
}
ItemLevel2.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  count: PropTypes.number,
  label: PropTypes.node,
  onClick: PropTypes.func,
  onClose: PropTypes.func,
  url: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
}

export function ItemLevel3({
  children,
  className = '',
  count,
  label,
  url,
  onClick,
  onClose,
}) {
  const hasChildren = !!children

  return (
    <li
      className={`w-full select-none px-6 py-2 lg:w-auto lg:px-0 lg:py-1 ${className}`}
    >
      <span className="flex flex-row items-center justify-between space-x-1 rtl:space-x-reverse">
        <ItemLink
          className="text-base lg:text-sm lg:uppercase"
          count={count}
          onClick={onClick}
          onClose={onClose}
          href={url}
        >
          {label}
        </ItemLink>
      </span>
      {hasChildren && (
        <ul className="space-y-1">
          {React.Children.map(children, child =>
            React.cloneElement(child, { onClose })
          )}
        </ul>
      )}
    </li>
  )
}
ItemLevel3.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  count: PropTypes.number,
  icon: PropTypes.string,
  label: PropTypes.node,
  onClick: PropTypes.func,
  onClose: PropTypes.func,
  url: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
}

function ItemLink({
  children,
  className = '',
  count,
  icon,
  onClick,
  onClose,
  href,
}) {
  const router = useRouter()
  const active = useMemo(() => router.asPath === href, [router, href])

  const handleClick = useCloseNavOnClick({ href, onClick, onClose })

  return (
    <a
      className={`p-1normal-case grow rounded leading-loose outline-none hover:text-color1-700 focus-visible:ring-2 focus-visible:ring-color1-500 lg:justify-start lg:uppercase ${
        active ? 'font-semibold text-color1-700' : 'cursor-pointer'
      } ${className}`}
      onClick={handleClick}
      href={href}
      tabIndex={0}
    >
      <span className="flex flex-row items-center space-x-1 rtl:space-x-reverse">
        {icon && <Icon name={icon} />}
        <span className="whitespace-nowrap">{children}</span>
        {count > 0 && (
          <Badge className="bg-danger-500 text-white" label={count} size="sm" />
        )}
      </span>
    </a>
  )
}
ItemLink.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  count: PropTypes.string,
  icon: PropTypes.string,
  onClick: PropTypes.func,
  onClose: PropTypes.func,
  href: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
}
