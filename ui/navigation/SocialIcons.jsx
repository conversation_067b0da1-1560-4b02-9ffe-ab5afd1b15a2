import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const SvgIcon = dynamic(() => import('ui/icons/SvgIcon'))

export default function SocialIcons({ className = '', items = [] }) {
  return (
    <div
      className={`flex flex-row justify-center space-x-1 rtl:space-x-reverse md:space-x-2 lg:items-start lg:justify-end xl:space-x-4 ${className}`}
    >
      {items?.map(({ id, platform }, i) => (
        <SocialIcon
          className="text-white"
          id={id}
          platform={platform}
          key={`social-link-${platform}-${id}-${i}`}
        />
      ))}
    </div>
  )
}
SocialIcons.propTypes = {
  className: PropTypes.string,
  items: PropTypes.array,
}

const socialConfig = {
  facebook: {
    baseUrl: 'https://www.facebook.com/',
  },
  instagram: {
    baseUrl: 'https://www.instagram.com/',
  },
  twitter: {
    baseUrl: 'https://twitter.com/',
  },
  youtube: {
    baseUrl: 'https://www.youtube.com/c/',
  },
}

export function SocialIcon({ id = '', platform, className = '' }) {
  const { baseUrl } = socialConfig[platform] || {}
  if (!baseUrl) return null

  return (
    <a
      className={`flex items-center ${className}`}
      href={`${baseUrl}${id}`}
      rel="noopener noreferrer"
      target="_blank"
    >
      <SvgIcon name={platform} />
    </a>
  )
}
SocialIcon.propTypes = {
  className: PropTypes.string,
  id: PropTypes.string,
  platform: PropTypes.oneOf(['facebook', 'instagram', 'twitter', 'youtube']),
}
