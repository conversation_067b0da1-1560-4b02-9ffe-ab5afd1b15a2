import React from 'react'
import PropTypes from 'prop-types'

export default function TopMenu({ items, title }) {
  if (!Array.isArray(items)) return null

  return (
    <div className="bg-color1-800 text-color1-300">
      <div className="mx-auto flex h-8 w-full max-w-screen-xl flex-row items-center justify-start space-x-4 overflow-x-scroll px-6 no-scrollbar rtl:space-x-reverse md:justify-end md:px-6 xl:px-12">
        {title && <p className="flex-shrink-0">{title}</p>}
        <ul className="flex flex-shrink-0 flex-row space-x-4 rtl:space-x-reverse lg:space-x-6">
          {items.map((item, index) => (
            <li
              key={`top-menu-item-${item.key}-${index}`}
              className="text-color1-100"
            >
              <a href={item.url} className="uppercase hover:text-white">
                {item.label}
              </a>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}
TopMenu.propTypes = {
  items: PropTypes.array,
  title: PropTypes.string,
}
