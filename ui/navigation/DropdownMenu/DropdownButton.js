import React from 'react'
import PropTypes from 'prop-types'
import clsx from 'clsx'
import Icon from 'ui/icons/Icon'
import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'

const iconSizeClasses = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
}

export function DropdownButton({
  className,
  icon = 'bar',
  iconSize = { xs: 'md' },
  open,
  openedClassName,
  openedIcon = 'xmark',
}) {
  const iconSizeValue = useValueAtBreakpoint(iconSize)

  return (
    <button
      className={clsx(
        'relative flex items-center justify-center',
        open ? openedClassName : className,
        iconSizeClasses[iconSizeValue]
      )}
    >
      <Icon
        name={icon}
        className={clsx('absolute inset-0 h-full w-full transition-opacity', {
          'opacity-1': !open,
          'opacity-0': open,
        })}
        iconClassName="h-full w-full"
      />
      <Icon
        name={openedIcon}
        className={clsx('absolute inset-0 h-full w-full transition-opacity', {
          'opacity-0': !open,
          'opacity-1': open,
        })}
        iconClassName="h-full w-full"
      />
    </button>
  )
}

DropdownButton.propTypes = {
  className: PropTypes.string,
  icon: PropTypes.string,
  iconSize: PropTypes.object,
  open: PropTypes.bool,
  openedClassName: PropTypes.string,
  openedIcon: PropTypes.string,
}
