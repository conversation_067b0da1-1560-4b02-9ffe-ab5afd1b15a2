import Dropdown from 'ui/buttons/Dropdown'
import { getTextColor } from 'ui/helpers/getColor'
import { MenuItem } from '../Menu/components/MenuItem'
import { DropdownButton } from './DropdownButton'

/**
 * @typedef {object} MenuItemShape
 * @property {string} [label] - The text to display for the menu item.
 * @property {string} [url] - The URL the menu item links to.
 * @property {Array<MenuItemShape>} [children] - Sub-menu items.
 */

/**
 * DropdownMenu component.
 *
 * @param {object} props - The component props
 * @param {string} [props.activeColor] - Color for the active menu item
 * @param {string} [props.activeItemClassName] - CSS class for the active menu item
 * @param {('left'|'right'|'center'|object)} [props.alignment] - Alignment of the dropdown panel
 * @param {string} [props.bgColor] - Background color of the dropdown panel
 * @param {object} [props.border] - Border properties for the dropdown panel
 * @param {object} [props.borderRadius] - Border radius for the dropdown panel
 * @param {string} [props.className] - Additional CSS classes for the items container (ul)
 * @param {string} [props.direction] - Direction of items within the dropdown
 * @param {object} [props.divider] - Divider properties between items
 * @param {string} [props.icon] - Icon for the dropdown button
 * @param {string} [props.iconClassName] - CSS class for the dropdown button icon
 * @param {object} [props.iconSize] - Size of the dropdown button icon
 * @param {string} [props.itemClassName] - CSS class for individual menu items
 * @param {string} [props.itemColor] - Text color for menu items
 * @param {object} [props.itemPadding] - Padding for menu items
 * @param {Array<MenuItemShape>} props.items - Array of menu item objects
 * @param {object} [props.justify] - Justification of content within menu items
 * @param {string|number} [props.offset] - Offset of the dropdown panel
 * @param {string} [props.openedIcon] - Icon for the dropdown button when open
 * @param {string} [props.openedIconClassName] - CSS class for the opened dropdown button icon
 * @param {string} [props.panelClass] - CSS class for the dropdown panel
 * @param {boolean} [props.showSubmenuChevron] - Whether to show chevrons for submenus
 * @param {string} [props.submenuItemActiveColor] - Text color for active submenu items
 * @param {string} [props.submenuItemActiveFontWeight] - Font weight for active submenu items
 * @param {string} [props.submenuItemColor] - Text color for submenu items
 * @param {object} [props.submenuItemDivider] - Divider properties between submenu items
 * @param {string} [props.submenuItemFontWeight] - Font weight for submenu items
 * @param {string} [props.submenuItemHoverColor] - Text color for submenu items on hover
 * @param {object} [props.submenuItemPadding] - Padding for submenu items
 * @param {object} [props.submenuItemSpacing] - Spacing for submenu items
 * @param {object} [props.submenuItemTextAlign] - Text alignment for submenu items
 * @param {object} [props.submenuItemTextSize] - Text size for submenu items
 * @param {object} [props.submenuPadding] - Padding for submenus
 * @returns {React.ReactElement}
 */
export function DropdownMenu({
  activeColor,
  activeItemClassName,
  alignment,
  bgColor,
  border,
  borderRadius,
  className,
  direction,
  divider,
  icon,
  iconClassName,
  iconSize,
  itemClassName,
  itemColor,
  itemPadding,
  items,
  justify,
  offset,
  openedIcon,
  openedIconClassName,
  panelClass,
  showSubmenuChevron,
  submenuItemActiveColor,
  submenuItemActiveFontWeight,
  submenuItemColor,
  submenuItemDivider,
  submenuItemFontWeight,
  submenuItemHoverColor,
  submenuItemPadding,
  submenuItemSpacing,
  submenuItemTextAlign,
  submenuItemTextSize,
  submenuPadding,
}) {
  const colorClass = getTextColor(itemColor)

  return (
    <Dropdown
      alignment={alignment}
      bgColor={bgColor}
      border={border}
      borderRadius={borderRadius}
      button={({ open }) => (
        <DropdownButton
          className={iconClassName}
          icon={icon}
          iconSize={iconSize}
          open={open}
          openedIcon={openedIcon}
          openedClassName={openedIconClassName}
        />
      )}
      color="none"
      direction={direction}
      divider={divider}
      hasMaxHeight={false}
      hideArrow
      icon={icon}
      iconClass={colorClass}
      panelAs="nav"
      itemsAs="ul"
      itemsClass={className}
      offset={offset}
      openedIcon={openedIcon}
      panelClass={panelClass}
      size="sm"
      variant="flat"
    >
      {({ close }) =>
        items.map((item, key) => (
          <MenuItem
            key={key}
            {...item}
            activeColor={activeColor}
            activeClassName={activeItemClassName}
            className={itemClassName}
            onLinkClick={() => {
              close()
            }}
            itemPadding={itemPadding}
            justify={justify}
            showSubmenuChevron={showSubmenuChevron}
            submenuDirection="vertical"
            submenuItemActiveColor={submenuItemActiveColor}
            submenuItemActiveFontWeight={submenuItemActiveFontWeight}
            submenuItemColor={submenuItemColor}
            submenuItemDivider={submenuItemDivider}
            submenuItemFontWeight={submenuItemFontWeight}
            submenuItemHoverColor={submenuItemHoverColor}
            submenuItemPadding={submenuItemPadding}
            submenuItemSpacing={submenuItemSpacing}
            submenuItemTextAlign={submenuItemTextAlign}
            submenuItemTextSize={submenuItemTextSize}
            submenuPadding={submenuPadding}
          />
        ))
      }
    </Dropdown>
  )
}

export default DropdownMenu
