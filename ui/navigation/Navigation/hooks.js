import React, { useCallback, useContext, useMemo } from 'react'

import { useRouter } from 'next/router'
import { usePageLoading } from 'ui/feedback/PageLoading'

export const NavigationContext = React.createContext()

export function useNavigationContext() {
  return useContext(NavigationContext)
}

/**
 * Returns a callback function the closes header's nav and shows page's loading bar
 * @param {Object} params
 * @param {String} params.href - current url
 * @param {Function} params.onClick - Callback for custom onClick event
 * @param {Function} params.onClose - Callback to close parent wrapper
 *
 * @returns {Function}
 */
export function useCloseNavOnClick({ href, onClick, onClose }) {
  const router = useRouter()
  const { setPage } = usePageLoading()
  const { closeNav } = useNavigationContext()
  const active = useMemo(() => router.asPath === href, [router, href])

  const handleClick = useCallback(
    e => {
      e.preventDefault()
      setPage(active ? null : router.asPath)

      closeNav()

      if (onClose) onClose() // Close parent wrapper
      if (onClick) onClick(e) // Call custom onClick event

      router.push(href)
    },
    [active, closeNav, onClose, onClick, router, setPage, href]
  )

  return handleClick
}
