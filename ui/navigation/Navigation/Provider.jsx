import React, { useMemo, useState } from 'react'
import PropTypes from 'prop-types'

import { NavigationContext } from './hooks'

export default function NavigationProvider({ children }) {
  const [showNav, setShowNav] = useState(false)
  const [contentTopMargin, setContentTopMargin] = useState(0)

  const value = useMemo(
    () => ({
      showNav,
      closeNav: () => setShowNav(false),
      toggleNav: () => setShowNav(!showNav),
      contentTopMargin,
      setContentTopMargin,
    }),
    [showNav, contentTopMargin]
  )

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  )
}

NavigationProvider.propTypes = {
  children: PropTypes.node,
}
