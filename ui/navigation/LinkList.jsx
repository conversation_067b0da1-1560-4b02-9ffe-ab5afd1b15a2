import clsx from 'clsx'
import React from 'react'

import dynamic from 'next/dynamic'

import { usePageContext } from 'components/PageProvider'
import { getDocumentUrl } from 'utils/documents'
import useColumns from 'ui/helpers/useColumns'
import useWidth from 'ui/helpers/useWidth'
import useGap from 'ui/helpers/useGap'
import usePlaceItems from 'ui/helpers/usePlaceItems'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))

/**
 * Renders a list of links
 *
 * @param {object} props The component props
 * @param {React.ReactNode} [props.children] Child elements, used if `items` array is not provided
 * @param {string} [props.className=''] Additional CSS classes for the component
 * @param {string} [props.id] The ID for the component
 * @param {'default'|'primary'|'secondary'|'tertiary'|'success'|'info'|'warning'|'danger'} [props.color] Color theme for the list items
 * @param {Array<LinkItem>} [props.items] An array of item objects to render
 * @param {string} [props.title] A title for the list
 * @param {'simple'|'text'} [props.variant='text'] The visual variant of the list items
 * @returns {React.ReactElement}
 */
export default function LinkList({
  children,
  className = '',
  columns,
  gap,
  id,
  color,
  items,
  listType = 'list',
  placeItems,
  title,
  variant = 'text',
  width,
}) {
  const columnClasses = useColumns(columns)
  const gapClasses = useGap(gap)
  const placeItemsClasses = usePlaceItems(placeItems)
  const widthClasses = useWidth(width)
  return (
    <div className={`space-y-4 ${className}`} id={id}>
      {title && <h3 className="text-xl">{title}</h3>}
      <ul
        className={clsx(
          listType === 'grid' &&
            clsx('grid', columnClasses, gapClasses, placeItemsClasses),
          listType === 'list' && 'flex flex-col gap-3',
          widthClasses,
          className
        )}
        id={id}
      >
        {Array.isArray(items)
          ? items.map((item, i) => (
              <li key={`list-item-${i}`}>
                <LinkItem color={color} variant={variant} {...item} />
              </li>
            ))
          : children}
      </ul>
    </div>
  )
}

const colors = {
  primary: 'bg-color1-700 text-white dark:bg-color1-600 dark:text-color1-200',
  success: 'bg-success-200 text-gray-700',
  info: 'bg-info-200 text-gray-700',
  danger: 'bg-danger-200 text-gray-700',
  warning: 'bg-warning-200 text-gray-700',
}

/**
 * Renders a single item in a LinkList
 *
 * @param {object} props The component props
 * @param {boolean} [props.active] Whether the link is active
 * @param {'default'|'primary'|'success'|'info'|'warning'|'danger'} [props.color] Color theme for the item
 * @param {React.ReactNode} [props.extra] Extra content to display on the right side
 * @param {object} [props.file] File object for download links
 * @param {string|React.ReactNode} [props.icon] Icon to display
 * @param {string} [props.label] The text label for the link
 * @param {'link'|'download'} [props.type] The type of link
 * @param {'simple'|'text'} [props.variant] The visual variant of the item
 * @param {string} [props.url] The URL for the link
 * @returns {React.ReactElement}
 */
export function LinkItem({
  active,
  color,
  extra,
  file,
  icon,
  label,
  type,
  variant,
  url,
}) {
  const colorClass = colors[color]
  const isDownload = type === 'download'
  const { site } = usePageContext()

  const variantMap = {
    simple: {
      backgroundColor: 'bg-gray-100',
      spacing: 'px-6 py-3',
    },
  }

  const { backgroundColor, spacing } = variantMap[variant] || {
    backgroundColor: 'bg-white',
    spacing: '',
  }

  return (
    <div
      className={`w-full rounded-lg ${backgroundColor} transition-colors duration-300 ease-in-out dark:bg-gray-700 ${
        active ? 'bg-color1-700' : ''
      }`}
    >
      <Link
        className={`flex flex-1 items-center space-x-4 rtl:space-x-reverse ${spacing}`}
        to={isDownload ? getDocumentUrl(file, site.entity) : url}
        disabled={active}
        download={isDownload}
      >
        {(icon || isDownload) && (
          <div
            className={`flex items-center justify-center rounded-md  leading-none text-3xl ${colorClass} ${
              colorClass ? 'h-6 w-6 p-1.5' : 'h-4 w-4'
            } ${active ? 'bg-transparent' : ''}`}
          >
            {typeof icon === 'string' || isDownload ? (
              <Icon
                name={icon || (isDownload ? '' : 'download')}
                className={`${active ? 'text-color1-700' : ''}`}
                size="22"
              />
            ) : (
              icon
            )}
          </div>
        )}
        <div
          className={`flex-1 text-base ${
            active ? 'font-bold text-white' : 'text-gray-800 dark:text-gray-100'
          }`}
        >
          {label}
        </div>
        {extra && <div className="text-gray-400">{extra}</div>}
      </Link>
    </div>
  )
}
