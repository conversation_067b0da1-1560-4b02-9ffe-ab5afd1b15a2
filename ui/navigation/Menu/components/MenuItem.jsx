import clsx from 'clsx'
import { useCallback, useEffect, useRef, useState } from 'react'

import dynamic from 'next/dynamic'
import { useUserCookieConsent } from 'components/CookieConsentProvider'
import { useRouter } from 'next/router'
import { SubNavigation } from './SubNavigation'

import useBorder from 'ui/helpers/useBorder'
import usePadding from 'ui/helpers/usePadding'
import useFlex from 'ui/helpers/useFlex'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Link = dynamic(() => import('ui/navigation/Link'))
const SubItem = dynamic(() => import('./SubItem'))

/**
 * @typedef {object} SubMenuItemShape
 * @property {string} [label] - The text to display for the sub-menu item.
 * @property {string} [url] - The URL the sub-menu item links to.
 * @property {boolean} [targetBlank] - Whether the link should open in a new tab.
 */

/**
 * Represents a single item in a menu, potentially with a submenu
 *
 * @param {object} props - The component props
 * @param {boolean} [props.active] - Explicitly set active state
 * @param {string} [props.activeClassName] - CSS class for the active menu item
 * @param {Array<SubMenuItemShape>} [props.children] - Child items to be rendered in a simple list (different from `items` which are for SubNavigation)
 * @param {Array<SubMenuItemShape>} [props.items] - Sub-menu items to be rendered in SubNavigation
 * @param {object} [props.itemPadding] - Padding for the main menu item
 * @param {object} [props.justify] - Justification for the main menu item content
 * @param {string} [props.className] - CSS class for the menu item
 * @param {string} [props.fontFamily] - Font family for the menu item and its submenu items
 * @param {'horizontal'|'vertical'} [props.submenuDirection='horizontal'] - Direction of the submenu
 * @param {object} [props.submenuBgColor] - Background color for the submenu
 * @param {object} [props.submenuBorderRadius] - Border radius for the submenu
 * @param {object} [props.submenuItemActiveBackgroundColor] - Background color for active submenu items
 * @param {object} [props.submenuItemActiveColor] - Text color for active submenu items
 * @param {object} [props.submenuItemActiveFontWeight] - Font weight for active submenu items
 * @param {object} [props.submenuItemBorderRadius] - Border radius for submenu items
 * @param {object} [props.submenuItemColor] - Text color for submenu items
 * @param {object} [props.submenuItemDivider] - Divider properties between submenu items
 * @param {object} [props.submenuItemFontWeight] - Font weight for submenu items
 * @param {object} [props.submenuItemHoverColor] - Text color for submenu items on hover
 * @param {object} [props.submenuItemPadding] - Padding for submenu items
 * @param {object} [props.submenuItemSpacing] - Spacing for submenu items
 * @param {object} [props.submenuItemTextAlign] - Text alignment for submenu items
 * @param {object} [props.submenuItemTextSize] - Text size for submenu items
 * @param {object} [props.submenuPadding] - Padding for the submenu container
 * @param {boolean} [props.showSubmenuChevron] - Whether to show a chevron for submenus
 * @param {string} [props.label] - The text label for the menu item
 * @param {function} [props.onLinkClick] - Callback function when a link is clicked
 * @param {string} [props.url] - URL for the menu item link
 * @param {'built-in'|'one-trust'|'trust-arc'} [props.cookieOptions] - Specifies behavior for cookie consent links
 * @param {object} [props.site] - Site-specific data, potentially including cookieSettings
 * @returns {React.ReactElement} The rendered menu item or null if cookie conditions are met
 */
export function MenuItem({
  active,
  activeClassName,
  children,
  items,
  itemPadding,
  justify,
  className,
  fontFamily,
  submenuDirection = 'horizontal',
  submenuBgColor,
  submenuBorderRadius,
  submenuItemActiveBackgroundColor,
  submenuItemActiveColor,
  submenuItemActiveFontWeight,
  submenuItemBorderRadius,
  submenuItemColor,
  submenuItemDivider,
  submenuItemFontWeight,
  submenuItemHoverColor,
  submenuItemPadding,
  submenuItemSpacing,
  submenuItemTextAlign,
  submenuItemTextSize,
  submenuPadding,
  showSubmenuChevron,
  label,
  onLinkClick,
  url,
  cookieOptions,
  site,
}) {
  const { cookieSettings } = site || {}
  const { asPath } = useRouter()

  if (submenuItemDivider && !submenuItemDivider?.width.b) {
    submenuItemDivider.width = { b: submenuItemDivider?.width, t: { xs: 0 } }
  }

  const borderClasses = useBorder(submenuItemDivider)
  const itemPaddingClasses = usePadding(itemPadding)
  const flexClasses = useFlex({ xs: 'x' }, { xs: 'center' }, justify)

  const isActive =
    active ??
    ((asPath?.includes(url) && url !== '/') || (asPath === '/' && !url))

  const expandRef = useRef()
  const [isOpen, setIsOpen] = useState(submenuDirection !== 'vertical')

  const [isHovered, setIsHovered] = useState(false)

  const handleHover = useCallback(
    e => {
      setIsHovered(e.type === 'mouseenter')
    },
    [setIsHovered]
  )

  const { setShowCookieConsent, disabled } = useUserCookieConsent()

  useEffect(() => {
    if (active && !isOpen && !expandRef.current) {
      setIsOpen(true)
      expandRef.current = true
    }
  }, [active, isOpen])

  const toggleSubItems = useCallback(() => {
    setIsOpen(!isOpen)
  }, [isOpen])

  if (cookieOptions === 'built-in' && cookieSettings?.disabled) return null

  const displayedClassName = isActive ? activeClassName ?? className : className

  return (
    <li
      className={`flex flex-col ${itemPaddingClasses}`}
      onMouseLeave={handleHover}
      onMouseEnter={handleHover}
    >
      <div className={`gap-2 ${flexClasses}`}>
        {!disabled && cookieOptions === 'built-in' ? (
          <Clickable
            className={displayedClassName}
            onClick={() => setShowCookieConsent(true)}
          >
            {label}
          </Clickable>
        ) : cookieOptions === 'one-trust' ? (
          <div id="ot-sdk-show-settings"></div>
        ) : cookieOptions === 'trust-arc' ? (
          <div id="teconsent"></div>
        ) : url ? (
          <Link
            className={`${displayedClassName} w-full text-nowrap`}
            onClick={onLinkClick}
            to={url}
          >
            {label}
          </Link>
        ) : (
          <Clickable
            className={`${displayedClassName} w-full text-nowrap`}
            onClick={toggleSubItems}
          >
            {label}
          </Clickable>
        )}
        {items?.length > 0 && showSubmenuChevron && (
          <Icon
            name="chevron-down"
            onClick={toggleSubItems}
            className={clsx(
              'transition duration-300',
              {
                'text-xs': submenuDirection === 'horizontal',
                'rotate-180': submenuDirection === 'vertical' && isOpen,
              },
              displayedClassName
            )}
          />
        )}
      </div>

      {children?.length > 0 && (
        <button
          className="select-none rounded-full p-1 text-gray-600 transition-colors duration-300 ease-in-out hover:bg-white hover:text-color1-800"
          onClick={toggleSubItems}
        >
          <Icon name={isOpen ? 'chevron-up' : 'chevron-down'} />
        </button>
      )}

      {children?.length > 0 && isOpen && (
        <ul className="py-2 pl-2">
          {children.map((child, i) => (
            <SubItem key={i} {...child} onClick={onLinkClick} />
          ))}
        </ul>
      )}

      {items?.length > 0 && (
        <div className="relative">
          <SubNavigation
            layout={submenuDirection}
            open={submenuDirection === 'horizontal' ? isHovered : isOpen}
            bgColor={submenuBgColor}
            borderRadius={submenuBorderRadius}
            padding={submenuPadding}
            spacing={submenuItemSpacing}
          >
            {items.map((item, i) => (
              <>
                <SubItem
                  key={i}
                  {...item}
                  onClick={onLinkClick}
                  borderRadius={submenuItemBorderRadius}
                  activeColor={submenuItemActiveColor}
                  color={submenuItemColor}
                  activeFontWeight={submenuItemActiveFontWeight}
                  fontFamily={fontFamily}
                  fontWeight={submenuItemFontWeight}
                  activeBackgroundColor={submenuItemActiveBackgroundColor}
                  hoverColor={submenuItemHoverColor}
                  padding={submenuItemPadding}
                  textAlign={submenuItemTextAlign}
                  textSize={submenuItemTextSize}
                />
                {i < items.length - 1 && (
                  <div className={`w-full ${borderClasses}`} />
                )}
              </>
            ))}
          </SubNavigation>
        </div>
      )}
    </li>
  )
}
