import { Transition } from '@headlessui/react'
import clsx from 'clsx'

import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'
import { getBackgroundColor, getTextColor } from 'ui/helpers/getColor'
import useBorderRadius from 'ui/helpers/useBorderRadius'
import useSpacing from 'ui/helpers/useSpacing'
import usePadding from 'ui/helpers/usePadding'
import Icon from 'ui/icons/Icon'

const transitions = {
  horizontal: {
    enter: 'transition-all duration-200',
    enterFrom: 'opacity-50 !translate-y-1/2',
    enterTo: 'opacity-100',
    leave: 'transition-all duration-75',
    leaveFrom: 'opacity-100 translate-y-4',
    leaveTo: 'opacity-50 translate-y-1/2',
  },
  vertical: {
    enter: 'transition-all duration-200',
    enterFrom: 'max-h-0',
    enterTo: 'max-h-screen',
    leave: 'transition-all duration-75',
    leaveFrom: 'max-h-screen',
    leaveTo: 'max-h-0',
  },
}

/**
 * Renders a sub-navigation panel with transition effects, typically for dropdown menus
 *
 * @param {object} props - The component props
 * @param {'horizontal'|'vertical'} props.layout - The layout direction of the sub-navigation
 * @param {boolean} props.open - Controls the visibility of the sub-navigation
 * @param {function} [props.onClose] - Callback function when the mouse leaves the sub-navigation panel (for horizontal layout)
 * @param {React.ReactNode} props.children - The content of the sub-navigation, usually a list of SubItem components
 * @param {object} [props.bgColor] - Background color for the sub-navigation panel
 * @param {object} [props.borderRadius] - Border radius for the sub-navigation panel
 * @param {object} [props.padding] - Padding for the sub-navigation panel
 * @param {object} [props.spacing] - Spacing properties for the sub-navigation panel (e.g., space-y-2)
 * @returns {React.ReactElement} The rendered sub-navigation component
 */
export function SubNavigation({
  layout,
  open,
  onClose,
  children,
  bgColor,
  borderRadius,
  padding,
  spacing,
}) {
  const bgColorAtBreakpoint = useValueAtBreakpoint(bgColor)
  const bgColorClass = getBackgroundColor(bgColorAtBreakpoint)
  const caretColorClass = getTextColor(bgColorAtBreakpoint)
  const borderRadiusClasses = useBorderRadius(borderRadius)
  const paddingClasses = usePadding(padding, 'pl-4 pt-2 md:pl-6')
  const spacingClasses = useSpacing(spacing)

  return (
    <Transition
      show={open}
      appear={true}
      unmount={false}
      className={clsx({
        'absolute top-full left-1/2 -translate-x-1/2 translate-y-4 min-w-32':
          layout === 'horizontal',
        [paddingClasses]: layout === 'vertical',
      })}
      {...transitions[layout]}
    >
      {layout === 'horizontal' ? (
        <div
          className={clsx(
            'flex flex-col drop-shadow-md',
            bgColorClass,
            borderRadiusClasses,
            paddingClasses,
            spacingClasses
          )}
          onMouseLeave={onClose}
        >
          <Icon
            name="caret-up"
            className={clsx(
              'absolute -top-3 left-1/2 -translate-x-1/2 text-2xl',
              caretColorClass
            )}
          />
          {children}
        </div>
      ) : (
        children
      )}
    </Transition>
  )
}
