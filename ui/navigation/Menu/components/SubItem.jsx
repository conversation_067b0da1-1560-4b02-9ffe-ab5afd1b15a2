import clsx from 'clsx'

import { useRouter } from 'next/router'

import {
  getBackgroundColor,
  getHoverBackgroundColor,
  getTextColor,
} from 'ui/helpers/getColor'
import useBorderRadius from 'ui/helpers/useBorderRadius'
import useTextAlign from 'ui/helpers/useTextAlign'
import Icon from 'ui/icons/Icon'
import Link from 'ui/navigation/Link'
import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'
import useFontWeight from 'ui/helpers/useFontWeight'
import { useFontFamily } from 'ui/helpers/useFontFamily'
import useTextSize from 'ui/helpers/useTextSize'
import usePadding from 'ui/helpers/usePadding'

/**
 * Represents a single sub-item in a menu, typically used within a SubNavigation component
 *
 * @param {object} props - The component props
 * @param {boolean} [props.active] - Explicitly set active state. If not provided, active state is determined by matching `url` with the current path
 * @param {string} [props.className] - Additional CSS classes for the main container of the sub-item
 * @param {string} [props.icon='minus'] - Name of the icon to display
 * @param {object} [props.borderRadius] - Border radius for the sub-item. Defaults to 'rounded-md' if not specified or empty
 * @param {object} [props.activeBackgroundColor] - Background color when the item is active
 * @param {string|object} [props.activeColor='gray-900'] - Text color when the item is active
 * @param {string|object} [props.activeFontWeight] - Font weight when the item is active. Defaults to 'font-semibold'
 * @param {string|object} [props.color='color1-700'] - Default text color of the label
 * @param {string} [props.fontFamily] - Font family for the label
 * @param {string|object} [props.fontWeight] - Font weight for the label
 * @param {string|object} [props.hoverColor='gray-100'] - Background color on hover when the item is not active
 * @param {object} [props.padding] - Padding for the sub-item. Defaults to 'px-2 py-1'
 * @param {object} [props.textAlign] - Text alignment for the label
 * @param {object} [props.textSize] - Text size for the label
 * @param {string} [props.iconClass] - Additional CSS classes for the icon
 * @param {string} [props.labelClass] - Additional CSS classes for the label text
 * @param {string} [props.label] - The text content of the sub-item
 * @param {boolean} [props.marked] - If true, applies 'text-color1-700' to the icon; otherwise, 'text-gray-400'
 * @param {string} [props.url] - The URL the sub-item links to. If provided, renders as a Link; otherwise, renders as a div
 * @param {function} [props.onClick] - Callback function when the sub-item (if it's a Link) is clicked
 * @returns {React.ReactElement} The rendered sub-item
 */
export function SubItem({
  active,
  className,
  icon = 'minus',
  borderRadius,
  activeBackgroundColor,
  activeColor = 'gray-900',
  activeFontWeight,
  color = 'color1-700',
  fontFamily,
  fontWeight,
  hoverColor = 'gray-100',
  padding,
  textAlign,
  textSize,
  iconClass,
  labelClass,
  label,
  marked,
  url,
  onClick,
}) {
  const { asPath } = useRouter()

  const isActive =
    active ??
    ((asPath?.includes(url) && url !== '/') || (asPath === '/' && !url))

  const textAlignClasses = useTextAlign(textAlign)
  const textSizeClass = useTextSize(textSize)
  const labelColorClass = getTextColor(color)
  const activeLabelColorClass = getTextColor(activeColor)
  const activeBackgroundColorClass = getBackgroundColor(activeBackgroundColor)
  const hoverClass = getHoverBackgroundColor(hoverColor)
  const fontFamilyClass = useFontFamily(fontFamily)
  const fontWeightValue = useValueAtBreakpoint(fontWeight)
  const activeFontWeightValue = useValueAtBreakpoint(
    activeFontWeight,
    'font-semibold'
  )
  const fontWeightClass = useFontWeight(fontWeightValue)
  const activeFontWeightClass = useFontWeight(activeFontWeightValue)
  const borderRadiusClasses = useBorderRadius(borderRadius) ?? 'rounded-md'
  const paddingClasses = usePadding(padding, 'px-2 py-1')

  return url ? (
    <Link
      className={clsx(
        'flex transition-colors duration-300 ease-in-out',
        className,
        paddingClasses,
        {
          [borderRadiusClasses]: borderRadiusClasses.trim() != '',
          'rounded-md': borderRadiusClasses.trim() == '',
          [`cursor-default ${activeBackgroundColorClass}`]: isActive,
          [hoverClass]: !isActive,
        }
      )}
      to={url}
      onClick={onClick}
    >
      <Icon
        className={clsx(
          'mr-2 mt-1 text-base',
          {
            'text-color1-700': marked,
            'text-gray-400': !marked,
          },
          iconClass
        )}
        name={icon}
      />
      <div
        className={clsx(
          {
            [`${activeLabelColorClass} ${activeFontWeightClass}`]: isActive,
            [`${labelColorClass} ${fontWeightClass}`]: !isActive,
          },
          'w-full',
          fontFamilyClass,
          textAlignClasses,
          textSizeClass,
          labelClass
        )}
      >
        {label}
      </div>
    </Link>
  ) : (
    <div className="flex py-1 pl-2">
      <Icon
        className={clsx(
          'mr-2 mt-1 text-base',
          {
            'text-color1-700': marked,
            'text-gray-400': !marked,
          },
          iconClass
        )}
        name={icon}
      />
      <div className={className}>{label}</div>
    </div>
  )
}

export default SubItem
