import { MenuItem } from './components/MenuItem'

/**
 * @typedef {object} MenuItemObject
 * @property {string} [label] - The text to display for the menu item
 * @property {string} [url] - The URL the menu item links to
 * @property {Array<MenuItemObject>} [children] - Sub-menu items
 * @property {string} [icon] - Icon for the menu item
 * @property {boolean} [targetBlank] - Whether the link should open in a new tab
 */

/**
 * Menu component to render a list of menu items
 *
 * @param {object} props - The component props
 * @param {string} [props.activeItemClassName] - CSS class for the active menu item
 * @param {string} [props.className] - Additional CSS classes for the menu container (ul element). Defaults to 'space-y-4' if not provided
 * @param {string} [props.itemClassName] - CSS class for individual menu items
 * @param {object} [props.itemPadding] - Padding for menu items
 * @param {Array<MenuItemObject>} [props.items=[]] - Array of menu item objects
 * @param {object} [props.site] - Site context or data
 * @param {string} [props.fontFamily] - Font family for menu items
 * @param {boolean} [props.showSubmenuChevron] - Whether to show chevrons for submenus
 * @param {object} [props.submenuBgColor] - Background color for submenus
 * @param {object} [props.submenuItemActiveBackgroundColor] - Background color for active submenu items
 * @param {object} [props.submenuItemActiveColor] - Text color for active submenu items
 * @param {object} [props.submenuItemActiveFontWeight] - Font weight for active submenu items
 * @param {object} [props.submenuBorderRadius] - Border radius for submenus
 * @param {object} [props.submenuItemColor] - Text color for submenu items
 * @param {object} [props.submenuItemBorderRadius] - Border radius for submenu items
 * @param {object} [props.submenuItemDivider] - Divider properties between submenu items
 * @param {object} [props.submenuItemFontWeight] - Font weight for submenu items
 * @param {object} [props.submenuItemHoverColor] - Text color for submenu items on hover
 * @param {object} [props.submenuItemPadding] - Padding for submenu items
 * @param {object} [props.submenuItemSpacing] - Spacing for submenu items
 * @param {object} [props.submenuItemTextAlign] - Text alignment for submenu items
 * @param {object} [props.submenuItemTextSize] - Text size for submenu items
 * @param {object} [props.submenuPadding] - Padding for submenus
 * @returns {JSX.Element} The rendered menu
 */
export function Menu({
  activeItemClassName,
  className,
  itemClassName,
  itemPadding,
  items = [],
  site,
  fontFamily,
  showSubmenuChevron,
  submenuBgColor,
  submenuItemActiveBackgroundColor,
  submenuItemActiveColor,
  submenuItemActiveFontWeight,
  submenuBorderRadius,
  submenuItemColor,
  submenuItemBorderRadius,
  submenuItemDivider,
  submenuItemFontWeight,
  submenuItemHoverColor,
  submenuItemPadding,
  submenuItemSpacing,
  submenuItemTextAlign,
  submenuItemTextSize,
  submenuPadding,
}) {
  return (
    <ul className={className ?? 'space-y-4'}>
      {items.map((item, key) => (
        <MenuItem
          key={`menu-item-${item.url}-${key}`}
          {...item}
          site={site}
          className={itemClassName}
          activeClassName={activeItemClassName}
          fontFamily={fontFamily}
          itemPadding={itemPadding}
          showSubmenuChevron={showSubmenuChevron}
          submenuBgColor={submenuBgColor}
          submenuBorderRadius={submenuBorderRadius}
          submenuItemActiveBackgroundColor={submenuItemActiveBackgroundColor}
          submenuItemActiveColor={submenuItemActiveColor}
          submenuItemActiveFontWeight={submenuItemActiveFontWeight}
          submenuItemBorderRadius={submenuItemBorderRadius}
          submenuItemColor={submenuItemColor}
          submenuItemDivider={submenuItemDivider}
          submenuItemFontWeight={submenuItemFontWeight}
          submenuItemHoverColor={submenuItemHoverColor}
          submenuItemPadding={submenuItemPadding}
          submenuItemSpacing={submenuItemSpacing}
          submenuItemTextAlign={submenuItemTextAlign}
          submenuItemTextSize={submenuItemTextSize}
          submenuPadding={submenuPadding}
        />
      ))}
    </ul>
  )
}
