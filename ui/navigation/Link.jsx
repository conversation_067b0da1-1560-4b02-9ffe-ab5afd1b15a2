import React from 'react'
import PropTypes from 'prop-types'

import NextLink from 'next/link'
import { useRouter } from 'next/router'
import { usePageLoading } from 'ui/feedback/PageLoading'
import { usePageContext } from 'components/PageProvider'

const Link = React.forwardRef(
  (
    {
      as = 'a',
      activeClass = '',
      basic,
      className = '',
      children,
      disabled,
      download,
      external,
      onClick,
      to = '',
      underline,
      ...rest
    },
    ref
  ) => {
    const router = useRouter()
    const { pageData } = usePageContext()
    const { language, defaultLanguage } = pageData || {}

    const { setPage } = usePageLoading()

    const activePathId = `${language === defaultLanguage ? (to ? '' : '/') : `/${language}`}${to}`
    const active = router.asPath === activePathId

    const activeClasses = active ? activeClass : ''

    if (to?.startsWith('www.')) to = `https://${to}`

    const isExternal =
      external || to?.match(/^((http|https|ftp):\/\/)/) !== null

    const props = {
      ref,
      className: `${
        basic
          ? `text-color1-700 hover:text-color1-800 break-words ${
              disabled ? 'text-gray-600 hover:text-gray-600' : ''
            } ${underline ? 'underline' : ''}`
          : ''
      }  ${activeClasses} ${className}`,
      href: isExternal ? to : undefined,
      target: isExternal ? '_blank' : undefined,
      download,
      onClick:
        onClick ||
        (to
          ? e => {
              e.stopPropagation()
              setPage(router.asPath)
            }
          : null),
      ...rest,
    }

    const link = React.createElement(
      as,
      {
        ...props,
        onClick: to
          ? e => {
              e.stopPropagation()
              if (!isExternal) {
                setPage(router.asPath)
              }
              onClick?.(e)
            }
          : onClick,
      },
      children
    )

    if (as !== 'a' || isExternal || !to) {
      return link
    }

    // Check if the link is any of these, if so don't add the language prefix
    const isMailto = to?.startsWith('mailto:')
    const isTel = to?.startsWith('tel:')
    const isHash = to?.startsWith('#')
    const isDefaultLanguage = language === defaultLanguage

    const addLanguagePrefix =
      !isExternal && !isMailto && !isTel && !isHash && !isDefaultLanguage

    const href = addLanguagePrefix ? `/${language}${to}` : to

    return (
      <NextLink {...props} href={href}>
        {children}
      </NextLink>
    )
  }
)
Link.propTypes = {
  as: PropTypes.oneOf(['a', 'button']),
  activeClass: PropTypes.string,
  basic: PropTypes.bool,
  children: PropTypes.node,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  download: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  external: PropTypes.bool,
  label: PropTypes.string,
  onClick: PropTypes.func,
  to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  underline: PropTypes.bool,
  locale: PropTypes.string,
}

export default Link

export function ConditionalLink({ condition, children, ...rest }) {
  return condition ? <Link {...rest}>{children}</Link> : children
}
ConditionalLink.propTypes = {
  condition: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  children: PropTypes.node,
}
