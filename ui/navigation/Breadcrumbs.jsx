import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { getConditionalItems } from 'utils/arrays'
import { isArray } from 'utils/types'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))

export default function Breadcrumbs({ items }) {
  if (!isArray(items) || items.length === 0) return null

  return (
    <div className="font-semibold text-sm md:text-base">
      <nav
        role="navigation"
        className="-mx-1 flex flex-wrap items-center leading-6 md:leading-4"
      >
        {getConditionalItems(items).map((item, key) => (
          <BreadcrumbItem {...item} key={`breadcrumb-${key}`} />
        ))}
      </nav>
    </div>
  )
}
Breadcrumbs.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      isLast: PropTypes.bool,
      url: PropTypes.string,
    })
  ),
}

export function BreadcrumbItem({ label, isLast, url }) {
  if (isLast) {
    return <span className="mx-1 text-gray-600 dark:text-gray-50">{label}</span>
  }

  return (
    <Link
      className="mx-1 inline-flex items-center space-x-2 hover:underline rtl:space-x-reverse dark:text-gray-400"
      to={url}
    >
      <span>{label}</span>
      <Icon
        name="chevron-right"
        className="mt-px inline-block text-gray-500 text-xs dark:text-gray-400"
      />
    </Link>
  )
}
BreadcrumbItem.propTypes = {
  label: PropTypes.string,
  isLast: PropTypes.bool,
  url: PropTypes.string,
}
