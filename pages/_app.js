import PropTypes from 'prop-types'

import { appWithTranslation } from 'next-i18next'
import { QueryClient, QueryClientProvider } from 'react-query'
import { PageLoadingProvider } from 'ui/feedback/PageLoading'

import 'styles/styles.css'
import 'styles/fonts.css'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: true,
    },
  },
})

function BaseFrontend({ Component, pageProps }) {
  return (
    <QueryClientProvider client={queryClient}>
      <PageLoadingProvider>
        <Component {...pageProps} />
      </PageLoadingProvider>
    </QueryClientProvider>
  )
}
BaseFrontend.propTypes = {
  Component: PropTypes.func,
  pageProps: PropTypes.object,
}

export default appWithTranslation(BaseFrontend)
