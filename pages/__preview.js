import { useEffect } from 'react'

import bodyParser from 'body-parser'
import { promisify } from 'util'

import Content from 'components/content/Content'
import CookieConsentProvider from 'components/CookieConsentProvider'

// Convert the bodyParser middleware to a promise, so we can use it with async/await
const parseBody = promisify(bodyParser.urlencoded({ extended: true }))

/**
 * Render the preview of a page or a block
 * @param {Object} props
 * @param {Object} props.page Page/block data
 * @returns {React.ReactElement}
 */
export default function Preview({ page }) {
  // Send the height of the content to the parent window
  useEffect(() => {
    // If the code is running on the server, do nothing
    if (typeof window === 'undefined') return

    // Function to send the height of the content to the parent window
    function sendHeight() {
      // Get the height of the content
      const rootChildren = document.getElementById('ROOT')?.children

      const heights = []

      for (let i = 0; i < rootChildren.length; i++) {
        heights.push(rootChildren[i].scrollHeight)
      }

      const height = heights.reduce((a, b) => a + b, 0)

      // Send the height to the parent window, via postMessage API
      window.parent.postMessage({ height }, '*')
    }

    // Send the height of the content to the parent window when
    // - the content loads
    sendHeight()

    // - the content changes size
    window.onresize = sendHeight

    // - the preview loads
    window.onload = sendHeight
  }, [page])

  return (
    <CookieConsentProvider settings={page.site?.cookieSettings}>
      <Content page={page} id="ROOT" />
    </CookieConsentProvider>
  )
}

/**
 * Get the preview data from the request body and pass it as props to the page
 * @param {Object} context
 * @param {Object} context.req Request object
 * @param {Object} context.res Response object
 * @param {Object} context.query Query parameters
 * @returns {Object} Props to pass to the page
 */
export async function getServerSideProps({ req, res, query }) {
  // If request is not a post request, or there is no `preview` parameter, directly return a 404
  // TODO: Increase security by checking the preview token
  if (req.method !== 'POST' || !query.preview) {
    res.statusCode = 404
    res.end()
    return { props: {} }
  }

  // Parse the form data so we can access the content and site data from request's body
  await parseBody(req, res)

  // Parse the content and site from form data (sent by the preview iframe in the Backend)
  const content = JSON.parse(req.body.content || '{}')
  const site = JSON.parse(req.body.site || '{}')

  return {
    props: {
      page: { content, site },
    },
  }
}
