import React from 'react'

import { useTranslation } from 'next-i18next'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'

import ErrorTemplate from 'ui/templates/Error'

// NOTE: This handles unexpected errors like when site is not found.
// Actual "404 Not found" for pages is handled between the Frontend API and the PageNotFound component in pages/[[...slug]].js

export default function Error404() {
  const { t } = useTranslation()
  return (
    <ErrorTemplate title={t('pageError')} message={t('pageErrorDescription')} />
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'en', ['common'])),
    },
  }
}
