import { useEffect } from 'react'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import dynamic from 'next/dynamic'

import CookieConsentProvider from 'components/CookieConsentProvider'
import PageMeta, { getPageMeta } from 'components/PageMeta'
import PageNotFound from 'components/PageNotFound'
import PagePassword from 'components/PagePassword'
import PageProvider from 'components/PageProvider'
import Content from 'components/content/Content'
import NavigationProvider from 'ui/navigation/Navigation'
import { omit } from 'utils/objects'
import PageLoading from 'ui/feedback/PageLoading'
import PageFavicon from 'components/Favicon'
import { validateJsonString } from 'utils/json'
import useOnlineStatus from 'hooks/useOnlineStatus'
import useIsStandalone from 'hooks/useIsStandalone'
import { useRouter } from 'next/router'

const CookieConsent = dynamic(() => import('ui/feedback/CookieConsent'))
const ErrorTemplate = dynamic(() => import('ui/templates/Error'))

const defaultCacheControl = 'public, s-maxage=300, stale-while-revalidate=86400' // 5 minutes

export default function Page({ page, noRender }) {
  const isOnline = useOnlineStatus()
  const isStandalone = useIsStandalone()
  const router = useRouter()

  // Handle offline redirect in useEffect to avoid SSR issues and render-time side effects
  useEffect(() => {
    if (!page || !page.site) {
      if (!isOnline && isStandalone) {
        router.replace('/offline-downloads')
      }
    }
  }, [page, isOnline, isStandalone, router])

  if (noRender) return null // do no render HTML when server responds with XML, JSON or plain text

  if (!page || !page.site) {
    // Show loading state while checking offline conditions to prevent flash
    if (!isOnline && isStandalone) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          </div>
        </div>
      )
    }
    return <ErrorTemplate />
  }

  return <PageProviders page={page} />
}

function PageProviders({ page }) {
  const { isRTL, language, site } = page

  return (
    <PageProvider page={page}>
      <NavigationProvider>
        <CookieConsentProvider settings={site?.cookieSettings || {}}>
          <main
            dir={isRTL ? 'rtl' : 'ltr'}
            lang={language}
            className="overflow-x-hidden"
          >
            <PageFavicon favicon={site?.favicon} />
            <PageMeta page={page} />
            <PageContent page={page} />
            <CookieConsent scripts={page.scripts} analytics={page.analytics} />
          </main>
        </CookieConsentProvider>
      </NavigationProvider>
    </PageProvider>
  )
}

function PageContent({ page }) {
  return page.notFound || (page.dynamicResource && page.resourceNotFound) ? (
    <PageNotFound page={page} />
  ) : page.passwordProtected && !page.passwordValid ? (
    <PagePassword page={page} />
  ) : (
    <>
      <PageLoading />
      <Content page={page} />
    </>
  )
}

export async function getServerSideProps({ req, res, params, query }) {
  const {
    NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_API_CLIENT_TOKEN,
    NEXT_PUBLIC_API_CACHE_CONTROL,
  } = process.env

  // Get the localeData from the API or cache
  const localeData = req.headers['x-locale-data']

  // The api will send the site language as defaultLocale and 'en' if
  const { defaultLocale } = localeData
    ? JSON.parse(localeData)
    : {
        defaultLocale: 'en',
        locales: ['en'],
      }

  // Determine the locale based on the NEXT_LOCALE cookie
  const nextLocaleCookie = req.cookies['NEXT_LOCALE']
  const locale = nextLocaleCookie === defaultLocale ? '' : nextLocaleCookie

  // Remove the valid locale from params.slug before sending to the API.
  // The locale is sent separately as a header.
  if (params.slug && locale) {
    if (params.slug.length > 1 && params.slug[0] === locale) {
      params.slug.splice(0, 1)
    }
    // Else remove the position from slug, which would leave it undefined
    else params.slug = undefined
  }

  // Start with a "notFound" page object
  let page = { notFound: true }

  // Fetch page from API, based in page.slug
  const headers = {
    Cookie: req.headers.cookie ?? '',
    ClientToken: NEXT_PUBLIC_API_CLIENT_TOKEN,
    Origin: req.headers.host,
    Language: locale,
  }

  const bypassApiCache = Boolean(query['apicache-bypass'])
  const clearApiCache = Boolean(query['apicache-clear'])

  if (bypassApiCache) {
    // Bypass API cache
    headers['x-apicache-bypass'] = true
  }

  // Send client IP
  headers['X-Forwarded-For'] =
    req.headers['x-forwarded-for'] ||
    req.headers['X-Forwarded-For'] ||
    req.connection.remoteAddress

  // const cacheControlHeaders = { 'Cache-Control': 'no-store' }
  const queryString = new URLSearchParams(
    omit({ ...query, locale: locale || defaultLocale }, 'slug')
  ).toString()

  try {
    const response = await fetch(
      `${NEXT_PUBLIC_API_URL}/web/pages/${encodeURI(params.slug)}${
        queryString ? `?${queryString}` : ''
      }`,
      {
        method: 'GET',
        headers,
        cache: 'no-store',
      }
      // {
      //   headers,
      //   params: omit(query, 'slug'),
      // }
    )

    if (response.ok) {
      page = await response.json()
    }
  } catch (error) {
    console.log(error) // eslint-disable-line no-console
  }

  if (bypassApiCache || clearApiCache) {
    // Clear CDNs cache
    res.setHeader('Cache-Control', 'public, s-maxage=0, Pragma: no-cache')
    res.setHeader('CDN-Cache-Control', 'public, s-maxage=0')
    res.setHeader('Vercel-CDN-Cache-Control', 'public, s-maxage=0')
  } else {
    // Sets Cache-Control header when page has some settings for it, or uses the default
    res.setHeader(
      'Cache-Control',
      page.cacheControl || NEXT_PUBLIC_API_CACHE_CONTROL || defaultCacheControl
    )
  }

  // Ensures response has a 404 status code when page or its dynamic resource are not found (and if page has a redirect, skip this)
  if (
    !page.redirect &&
    (page.notFound || (page.dynamicResource && page.resourceNotFound))
  ) {
    res.statusCode = 404
  }

  // Stop when site is not found
  if (!page.site) {
    return {
      notFound: true,
    }
  }

  // When there is a redirect for this page
  if (page.redirect) {
    res.setHeader('Cache-Control', 'no-cache')

    return {
      redirect: page.redirect,
    }
  }

  // When page is set to return XML, JSON or plain text
  if (
    page.xml?.enabled ||
    page.json?.enabled ||
    page.text?.enabled ||
    page.html?.enabled
  ) {
    const isXml = page.xml?.enabled
    const isJson = page.json?.enabled
    const isText = page.text?.enabled
    const isHtml = page.html?.enabled
    const { resource } = isXml ? page.xml : isJson ? page.json : {} // If enabled and has a resource, resource should be the output
    const outputString = resource
      ? page.resources[resource] // this page resource is expected to be a string in XML format
      : isHtml
        ? page.html.content
        : isText
          ? page.text.content
          : validateJsonString(page.json?.content) // this page content is expected to be a JSON content (in string format)

    // Output the string content if it is not empty or if it is a text content (empty text content should be rendered)
    if (outputString || (isText && outputString === '')) {
      const contentType = isXml
        ? 'text/xml'
        : isJson
          ? 'application/json'
          : isHtml
            ? 'text/html'
            : 'text/plain'
      res.setHeader('Content-Type', contentType)
      res.write(outputString)
      res.end()

      return {
        props: {
          noRender: true,
        },
      }
    }

    // If there is no XML, JSON, or string, return 404
    page.notFound = true
  }

  const { i18nNamespaces, site, language, availableLanguages = null } = page
  const pageLocale = language || site.language || locale || 'en'
  const meta = getPageMeta(page, pageLocale)

  return {
    props: {
      page: {
        ...page,
        pageData: {
          ...meta,
          id: page.id || null,
          site,
          availableLanguages,
          host: req.headers.host,
          url: req.url,
          language: pageLocale,
          defaultLanguage: defaultLocale,
          absoluteUrl: `https://${req.headers.host}${page.path}`,
        },
      },
      ...(await serverSideTranslations(pageLocale, [
        'common',
        'cookies',
        'media-library',
        'payments',
        ...(i18nNamespaces || []),
      ])),
    },
  }
}
