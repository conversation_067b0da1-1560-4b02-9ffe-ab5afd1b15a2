const CACHE_MAX_AGE_HOURS = 1
const MAX_AGE = CACHE_MAX_AGE_HOURS * 60 * 60
const { NEXT_PUBLIC_API_URL, NEXT_PUBLIC_API_CLIENT_TOKEN } = process.env

export default async function handler(req, res) {
  res.statusCode = 200
  res.setHeader('Content-Type', 'text/xml')
  res.setHeader(
    'Cache-Control',
    `public, s-maxage=${MAX_AGE}, stale-while-revalidate`
  )

  let data = []

  try {
    const response = await fetch(`${NEXT_PUBLIC_API_URL}/web/manifest`, {
      headers: {
        ClientToken: NEXT_PUBLIC_API_CLIENT_TOKEN,
        Origin: req.headers.host,
      },
    })

    data = await response.json()
  } catch (error) {
    res.statusCode = 500
    console.error(error) // eslint-disable-line no-console
  }

  const json = JSON.stringify(data)
  res.setHeader('Content-Type', 'application/json')
  res.end(json)
}
