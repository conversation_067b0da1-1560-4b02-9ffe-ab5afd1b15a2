import { APIError, postFetch } from 'utils/http'

const cookieExpiration = 60 * 60 * 24 // 1 day

export default async function protectedPage(req, res) {
  try {
    const result = await postFetch('/web/pages/password', req.body, {
      headers: {
        Origin: req.headers?.host,
      },
    })
    if (result.passwordMatches && result.password) {
      // Set cookie for 1 day
      res.setHeader(
        'Set-Cookie',
        `secure-page=${result.password.iv},${result.password.content}; SameSite=Lax; HttpOnly; Max-Age=${cookieExpiration}; Path=${result.path}`
      )
    }

    return res.status(200).send({})
  } catch (error) {
    if (error instanceof APIError && error.status === 401) {
      return res.status(401).send(error.data)
    }
    return res.status(500).send({})
  }
}
