import { getRequest } from 'utils/fetcher'
import {
  generateSitemapIndex,
  generateSitemapIndexItem,
  generateSitemapItem,
  generateSitemapWrap,
} from 'utils/sitemap'

const CACHE_MAX_AGE_HOURS = 1
const MAX_AGE = CACHE_MAX_AGE_HOURS * 60 * 60

const dynamicResources = ['pages', 'articles', 'shows', 'episodes']

export default async function handler(req, res) {
  const { host = 'localhost' } = req.headers

  res.statusCode = 200
  res.setHeader('Content-Type', 'text/xml')
  res.setHeader(
    'Cache-Control',
    `public, s-maxage=${MAX_AGE}, stale-while-revalidate`
  )

  const protocol = `http${host.startsWith('localhost:') ? '' : 's'}`
  const domain = `${protocol}://${host}`

  // Note: in order to keep XML sizes small (under 4.5MB), we split the sitemap into several files, one per Dynamic Resource (not per module!).

  const xml = generateSitemapIndex(
    dynamicResources
      .map(resource =>
        generateSitemapIndexItem(`${domain}/sitemap-${resource}.xml`)
      )
      .join('\n  ')
  )
  res.end(xml)
}

/**
 *
 * @param {String} resource name of the Dynamic Resources to be fetched (default will be webmodule's pages)
 * @param {*} req request object
 * @param {*} res response object
 */
export async function fetchResources(resource = 'Page', req, res) {
  let items = []

  res.statusCode = 200
  res.setHeader('Content-Type', 'text/xml')
  res.setHeader(
    'Cache-Control',
    `public, s-maxage=${MAX_AGE}, stale-while-revalidate`
  )

  try {
    const data = getRequest(`/web/sitemap?resource=${resource}`, {
      headers: {
        Origin: req.headers.host,
      },
    })

    if (Array.isArray(data.items)) {
      items = [...data.items] // [ { loc: 'https://...', lastmod: Date }, ...]
    }
  } catch (error) {
    res.statusCode = 500
    console.log(error) // eslint-disable-line no-console
  }

  const xml = generateSitemapWrap(
    items.map(item => generateSitemapItem(item.loc, item.lastmod)).join('\n  ')
  )

  res.end(xml)
}
