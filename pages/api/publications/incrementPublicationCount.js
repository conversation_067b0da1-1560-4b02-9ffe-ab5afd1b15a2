import { postFetch } from 'utils/http'

export default async function handler(req, res) {
  const { language, publicationId } = req.body

  try {
    // Increment count of downloads for the publication in the database
    // We send the language to identify the correct publication that has to have their count incremented.
    // A response is not needed, we just send it. This way we avoid the user waiting for the response from the API before downloading.
    await postFetch(
      `/publications/${publicationId}/incrementCount`,
      {
        language,
      },
      {
        headers: {
          Origin: req.headers.host,
        },
      }
    )

    console.log('Downloaded file') // eslint-disable-line no-console

    res.status(200).send('Success') // eslint-disable-line no-console
  } catch (error) {
    console.error('Error downloading file:', error) // eslint-disable-line no-console
    res.status(500).send('Error downloading file')
  }
}
