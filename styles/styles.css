@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Default fallback fonts */
  --font-sans: 'Noto Sans';
  --font-serif: 'Noto Serif';
  --font-header: 'Noto Serif';
  --font-body: 'Noto Sans';
  --font-display: 'Noto Sans Display';
  --font-mono: 'Noto Sans Mono';

  /* Fallback custom colors (in space separated RGB values) */

  /* Gray color (base #a0aec0)*/
  --gray-50: 248 248 248; /* rgb(248, 248, 248) */
  --gray-100: 240 240 240; /* rgb(240, 240, 240) */
  --gray-200: 225 225 225; /* rgb(225, 225, 225) */
  --gray-300: 205 205 205; /* rgb(205, 205, 205) */
  --gray-400: 174 174 174; /* rgb(174, 174, 174) */
  --gray-500: 145 145 145; /* rgb(145, 145, 145) */
  --gray-600: 118 118 118; /* rgb(118, 118, 118) */
  --gray-700: 94 94 94; /* rgb(94, 94, 94) */
  --gray-800: 75 75 75; /* rgb(75, 75, 75) */
  --gray-900: 61 61 61; /* rgb(61, 61, 61) */
  --gray-950: 34 34 34; /* rgb(34, 34, 34) */

  /* Custom color 1 (magenta, base #ff00ff)*/
  --color1-50: 255 245 255; /* rgb(255, 245, 255) */
  --color1-100: 255 234 255; /* rgb(255, 234, 255) */
  --color1-200: 255 212 255; /* rgb(255, 212, 255) */
  --color1-300: 255 180 255; /* rgb(255, 180, 255) */
  --color1-400: 255 123 255; /* rgb(255, 123, 255) */
  --color1-500: 255 0 255; /* rgb(255, 0, 255) */
  --color1-600: 208 0 208; /* rgb(208, 0, 208) */
  --color1-700: 168 0 168; /* rgb(168, 0, 168) */
  --color1-800: 136 0 136; /* rgb(136, 0, 136) */
  --color1-900: 113 0 113; /* rgb(113, 0, 113) */
  --color1-950: 68 0 68; /* rgb(68, 0, 68) */

  /* Custom color 2 (green, base #66ee55)*/
  --color2-50: 230 252 227; /* rgb(230, 252, 227) */
  --color2-100: 202 249 196; /* rgb(202, 249, 196) */
  --color2-200: 157 244 145; /* rgb(157, 244, 145) */
  --color2-300: 94 237 75; /* rgb(94, 237, 75) */
  --color2-400: 41 201 20; /* rgb(41, 201, 20) */
  --color2-500: 34 168 17; /* rgb(34, 168, 17) */
  --color2-600: 28 137 14; /* rgb(28, 137, 14) */
  --color2-700: 22 109 11; /* rgb(22, 109, 11) */
  --color2-800: 17 85 9; /* rgb(17, 85, 9) */
  --color2-900: 15 71 7; /* rgb(15, 71, 7) */
  --color2-950: 8 41 4; /* rgb(8, 41, 4) */

  /* Custom color 3 (teal, base #00cccc)*/
  --color3-50: 219 255 255; /* rgb(219, 255, 255) */
  --color3-100: 176 255 255; /* rgb(176, 255, 255) */
  --color3-200: 0 250 250; /* rgb(0, 250, 250) */
  --color3-300: 0 228 228; /* rgb(0, 228, 228) */
  --color3-400: 0 194 194; /* rgb(0, 194, 194) */
  --color3-500: 0 162 162; /* rgb(0, 162, 162) */
  --color3-600: 0 132 132; /* rgb(0, 132, 132) */
  --color3-700: 0 105 105; /* rgb(0, 105, 105) */
  --color3-800: 0 84 84; /* rgb(0, 84, 84) */
  --color3-900: 0 69 69; /* rgb(0, 69, 69) */
  --color3-950: 0 39 39; /* rgb(0, 39, 39) */

  /* NOTE: Other custom colors (4 to 6) have no fallback and they will be rendered transparent if they are not set in the backend.*/

  /* System colors: */

  --danger-100: 255 245 245; /* #fff5f5 */
  --danger-200: 255 222 215; /* #ffded7 */
  --danger-300: 254 178 178; /* #feb2b2 */
  --danger-400: 252 129 129; /* #fc8181 */
  --danger-500: 245 101 101; /* #f56565 */
  --danger-600: 229 62 62; /* #e53e3e */
  --danger-700: 197 48 48; /* #c53030 */
  --danger-800: 155 44 44; /* #9b2c2c */
  --danger-900: 116 42 42; /* #742a2a */

  --warning-100: 255 250 240; /* #fffaf0 */
  --warning-200: 254 235 200; /* #feebc8 */
  --warning-300: 251 211 141; /* #fbd38d */
  --warning-400: 246 173 85; /* #f6ad55 */
  --warning-500: 237 137 54; /* #ed8936 */
  --warning-600: 221 107 32; /* #dd6b20 */
  --warning-700: 192 86 33; /* #c05621 */
  --warning-800: 156 66 33; /* #9c4221 */
  --warning-900: 123 52 30; /* #7b341e */

  --info-100: 230 255 250; /* #e6fffa */
  --info-200: 201 255 255; /* #c9fffd */
  --info-300: 129 230 217; /* #81e6d9 */
  --info-400: 79 209 197; /* #4fd1c5 */
  --info-500: 56 178 172; /* #38b2ac */
  --info-600: 49 151 149; /* #319795 */
  --info-700: 44 122 123; /* #2c7a7b */
  --info-800: 40 94 97; /* #285e61 */
  --info-900: 35 78 82; /* #234e52 */

  --success-100: 240 255 244; /* #f0fff4 */
  --success-200: 219 252 232; /* #dbfce8 */
  --success-300: 154 230 180; /* #9ae6b4 */
  --success-400: 104 211 145; /* #68d391 */
  --success-500: 72 187 120; /* #48bb78 */
  --success-600: 56 161 105; /* #38a169 */
  --success-700: 47 133 90; /* #2f855a */
  --success-800: 39 103 73; /* #276749 */
  --success-900: 34 84 61; /* #22543d */

  /* Components */
  /* - Button*/
  --button-roundness: 0.25rem;
  --button-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* TODO: Remove this if the issue with tailwind forms is fixed in upstream */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e") !important;
}
