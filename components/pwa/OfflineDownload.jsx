import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import PropTypes from 'prop-types'
import { useTranslation } from 'next-i18next'

import useVideoDownload from '../../hooks/useVideoDownload'
import useOnlineStatus from '../../hooks/useOnlineStatus'
import useVideoStorage from '../../hooks/useVideoStorage'
import { formatBytes } from '../../utils/strings'

import Button from '../../ui/buttons/Button'
import DeleteConfirmationModal from './DeleteConfirmationModal'
import Alert from '../../ui/feedback/Alert'
import { Select } from '../../ui/data-entry/Select'

export default function OfflineDownload({
  videoUrls,
  thumbnailUrl,
  title,
  subtitle,
  body,
  channel,
  show,
  videoId,
  variant = 'primary',
  size = 'md',
  className = '',
  showProgress = true,
  showSuccessMessage = true,
  onDownloadStart,
  onDownloadComplete,
  onDownloadError,
  onVideoDeleted,
}) {
  const { t } = useTranslation('pwa')
  const [downloadComplete, setDownloadComplete] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteError, setDeleteError] = useState(null)
  const [storedVideo, setStoredVideo] = useState(null)
  const [selectedQuality, setSelectedQuality] = useState(0) // Index of selected quality option
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [showStorageInfo, setShowStorageInfo] = useState(false)
  const isOnline = useOnlineStatus()

  const { downloadVideo, isDownloading, downloadProgress, downloadError } =
    useVideoDownload(t)

  const {
    videos,
    deleteVideo,
    storageUsage,
    loading: storageLoading,
  } = useVideoStorage(t)

  // Normalize videoUrls to always be an array
  const qualityOptions = Array.isArray(videoUrls)
    ? videoUrls
    : videoUrls
      ? [{ url: videoUrls.trim(), label: 'Default' }]
      : []

  const hasValidUrls = qualityOptions.length > 0
  const hasMultipleQualities = qualityOptions.length > 1
  const selectedVideoUrl = qualityOptions[selectedQuality]?.url
  const selectedFileSize = qualityOptions[selectedQuality]?.fileSize

  // Check if video is already stored when videoId or videos change
  useEffect(() => {
    if (videoId && videos.length > 0) {
      const existingVideo = videos.find(video => video.id === videoId)
      setStoredVideo(existingVideo || null)
    } else {
      setStoredVideo(null)
    }
  }, [videoId, videos])

  // Reset selected quality when videoUrl changes or if current selection is out of bounds
  useEffect(() => {
    if (selectedQuality >= qualityOptions.length) {
      setSelectedQuality(0)
    }
  }, [videoUrls, selectedQuality, qualityOptions.length])

  const handleDownload = async () => {
    if (!selectedVideoUrl?.trim()) return

    try {
      // Call onDownloadStart callback if provided
      onDownloadStart?.()

      await downloadVideo(selectedVideoUrl, thumbnailUrl, {
        id: videoId?.trim() || undefined,
        title: title?.trim() || undefined,
        subtitle: subtitle?.trim() || undefined,
        body: body || undefined,
        channel: channel?.trim() || undefined,
        show: show?.trim() || undefined,
        quality: qualityOptions[selectedQuality]?.label || undefined,
      })

      setDownloadComplete(true)

      // Call onDownloadComplete callback if provided
      onDownloadComplete?.()

      // Hide success message after 5 seconds
      setTimeout(() => {
        setDownloadComplete(false)
      }, 5000)
    } catch (error) {
      // Error is already handled by the hook
      onDownloadError?.(error)
    }
  }

  const handleDeleteClick = () => {
    setShowDeleteConfirmation(true)
  }

  const handleDeleteConfirm = async () => {
    if (!storedVideo?.id) return

    try {
      setIsDeleting(true)
      setDeleteError(null)
      setShowDeleteConfirmation(false)

      await deleteVideo(storedVideo.id)

      // Call onVideoDeleted callback if provided
      onVideoDeleted?.(storedVideo)

      // Reset stored video state
      setStoredVideo(null)
    } catch (error) {
      setDeleteError(error.message)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDeleteCancel = () => {
    setShowDeleteConfirmation(false)
  }

  // Check if selected video is too large for available storage
  const isVideoTooLarge = () => {
    if (!selectedFileSize || !storageUsage?.available) return false
    return selectedFileSize > storageUsage.available
  }

  const isDisabled =
    !hasValidUrls ||
    !selectedVideoUrl?.trim() ||
    isDownloading ||
    !isOnline ||
    isVideoTooLarge()
  const videoIsStored = Boolean(storedVideo)

  return (
    <div className={`offline-download ${className}`}>
      {/* Quality Selector - only show if multiple qualities available and video not stored */}
      {hasMultipleQualities && !videoIsStored && (
        <div className="mb-3">
          <label
            htmlFor="quality-select"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {t('videoQuality')}
          </label>
          <Select
            name="quality-select"
            value={selectedQuality}
            onChange={e => setSelectedQuality(parseInt(e.target.value))}
            disabled={isDownloading}
            options={qualityOptions.map((option, index) => ({
              label: `${option.label}${option.fileSize ? ` (${formatBytes(option.fileSize, 0)})` : ''}`,
              value: index,
            }))}
            selectClass="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
          />
        </div>
      )}

      {/* Show message when no valid URLs are available */}
      {!hasValidUrls && !videoIsStored && (
        <div className="mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="flex items-center">
            <svg
              className="w-4 h-4 text-gray-500 mr-2 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
            <p className="text-gray-700 font-medium text-sm">
              {t('noVideoUrls')}
            </p>
          </div>
          <p className="text-gray-600 text-xs mt-1 ml-6">
            {t('noVideoUrlsDetail')}
          </p>
        </div>
      )}

      {/* Show different content based on whether video is stored */}
      {videoIsStored ? (
        <div className="space-y-3">
          {/* Video already stored message */}
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <svg
                className="w-4 h-4 text-green-600 mr-2 flex-shrink-0"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <div>
                <p className="text-green-800 font-medium text-sm">
                  {t('videoAlreadyDownloaded')}
                </p>
                <p className="text-green-700 text-xs mt-1">
                  {t('videoAvailableOffline')}{' '}
                  <Link
                    href="/offline-downloads"
                    className="underline hover:text-green-900"
                  >
                    {t('downloadsPage')}
                  </Link>
                  .
                </p>
              </div>
            </div>
          </div>

          {/* Delete button */}
          <Button
            onClick={handleDeleteClick}
            disabled={isDeleting}
            variant="danger"
            size={size}
            label={isDeleting ? t('deleting') : t('deleteFromDevice')}
            title={t('removeVideoTooltip')}
            className="w-full sm:w-auto"
          />
        </div>
      ) : hasValidUrls ? (
        <div className="space-y-2">
          <Button
            onClick={handleDownload}
            disabled={isDisabled}
            variant={variant}
            size={size}
            label={isDownloading ? t('downloading') : t('downloadVideo')}
            title={
              !isOnline
                ? t('needOnlineToDownload')
                : isVideoTooLarge()
                  ? t('videoTooLarge')
                  : isDownloading
                    ? t('downloadInProgress')
                    : t('downloadVideoQualityTooltip', {
                        quality:
                          qualityOptions[selectedQuality]?.label ||
                          t('selected'),
                      })
            }
            className="w-full sm:w-auto"
          />

          {/* Video too large message */}
          {isVideoTooLarge() && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <svg
                  className="w-4 h-4 text-red-600 mr-2 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
                <div>
                  <p className="text-red-800 font-medium text-sm">
                    {t('insufficientStorage')}
                  </p>
                  <p className="text-red-700 text-xs mt-1">
                    {t('videoLargerThanStorage', {
                      videoSize: formatBytes(selectedFileSize, 0),
                      availableSize: formatBytes(
                        storageUsage?.available || 0,
                        0
                      ),
                    })}{' '}
                    <Link
                      href="/offline-downloads"
                      className="underline hover:text-red-900"
                    >
                      {t('freeUpSpace')}
                    </Link>{' '}
                    {t('toDownloadVideo')}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* File size info */}
          {selectedFileSize && !isVideoTooLarge() && (
            <p className="text-sm text-gray-600">
              {t('estimatedDownloadSize')}{' '}
              <span className="font-medium">
                {formatBytes(selectedFileSize, 0)}
              </span>
            </p>
          )}
        </div>
      ) : null}

      {/* Delete Error */}
      {deleteError && (
        <div className="mt-3">
          <Alert variant="danger" title={t('deleteError')}>
            {deleteError}
          </Alert>
        </div>
      )}

      {/* Download Progress */}
      {isDownloading && showProgress && !videoIsStored && (
        <div className="mt-3">
          <div className="mb-2 flex justify-between text-sm text-gray-600">
            <span>
              {t('downloading')}
              {hasMultipleQualities
                ? ` (${qualityOptions[selectedQuality]?.label})`
                : ''}
              ...
            </span>
            <span>{Math.round(downloadProgress)}%</span>
          </div>
          <div
            className="relative h-2 w-full overflow-hidden rounded-full"
            style={{ backgroundColor: '#e5e7eb' }} // gray-200
          >
            <div
              style={{
                position: 'absolute',
                left: 0,
                top: 0,
                height: '100%',
                width: `${Math.max(0, Math.min(100, downloadProgress))}%`,
                backgroundColor: '#2563eb', // blue-600
                borderRadius: '9999px',
                transition: 'width 300ms ease-out',
                minWidth: downloadProgress > 0 ? '4px' : '0px',
              }}
            />
          </div>
        </div>
      )}

      {/* Download Error */}
      {downloadError && !videoIsStored && (
        <div className="mt-3">
          <Alert variant="danger" title={t('downloadError')}>
            {downloadError}
          </Alert>
        </div>
      )}

      {/* Download Success Message */}
      {downloadComplete &&
        showSuccessMessage &&
        !isDownloading &&
        !downloadError &&
        !videoIsStored && (
          <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <svg
                className="w-4 h-4 text-green-600 mr-2 flex-shrink-0"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <p className="text-green-800 font-medium text-sm">
                {t('videoDownloadedSuccessfully')}
                {hasMultipleQualities
                  ? ` ${t('inQuality', { quality: qualityOptions[selectedQuality]?.label })}`
                  : ''}
                !
              </p>
            </div>
            <p className="text-green-700 text-xs mt-1">
              {t('viewDownloadedVideos')}{' '}
              <Link
                href="/offline-downloads"
                className="underline hover:text-green-900"
              >
                {t('downloadsPage')}
              </Link>
              .
            </p>
          </div>
        )}

      {/* Offline Message */}
      {!isOnline && !videoIsStored && (
        <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-center">
            <svg
              className="w-4 h-4 text-amber-600 mr-2 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <p className="text-amber-800 font-medium text-sm">
              {t('needOnlineToDownload')}
            </p>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        show={showDeleteConfirmation}
        videoToDelete={storedVideo}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />

      {/* Storage info toggle link - positioned at bottom right */}
      {storageUsage && (
        <div className="flex justify-start mt-4">
          <button
            type="button"
            onClick={() => setShowStorageInfo(!showStorageInfo)}
            className="text-xs text-gray-500 hover:text-gray-700 underline focus:outline-none focus:text-gray-700"
          >
            {showStorageInfo ? t('hideStorageInfo') : t('showStorageInfo')}
          </button>
        </div>
      )}

      {/* Storage Info - only visible when toggled */}
      {showStorageInfo && storageUsage && (
        <div className="mt-3">
          <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
            <div className="text-sm">
              <div className="flex items-center justify-between gap-2 mb-1">
                <span className="text-gray-600">{t('storageAvailable')}:</span>
                <span className="font-medium text-gray-900">
                  {storageLoading
                    ? t('loading')
                    : storageUsage.available !== null
                      ? formatBytes(storageUsage.available, 0)
                      : t('unknown')}
                </span>
              </div>

              <div className="flex items-center justify-between gap-2">
                <span className="text-gray-600">{t('videosStored')}:</span>
                <span className="font-medium text-gray-900">
                  {storageLoading
                    ? t('loading')
                    : `${videos.length} (${formatBytes(storageUsage.used, 0)})`}
                </span>
              </div>

              {/* Show current video size if it's stored */}
              {storedVideo?.size && (
                <div className="flex items-center justify-between gap-2 mt-1">
                  <span className="text-gray-600">{t('thisVideoSize')}:</span>
                  <span className="font-medium text-gray-900">
                    {storageLoading
                      ? t('loading')
                      : formatBytes(storedVideo.size, 0)}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

OfflineDownload.propTypes = {
  videoUrls: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.arrayOf(
      PropTypes.shape({
        url: PropTypes.string.isRequired,
        label: PropTypes.string.isRequired,
        fileSize: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      })
    ),
  ]),
  thumbnailUrl: PropTypes.string,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  body: PropTypes.object,
  channel: PropTypes.string,
  show: PropTypes.string,
  videoId: PropTypes.string,
  variant: PropTypes.string,
  size: PropTypes.string,
  className: PropTypes.string,
  label: PropTypes.string,
  downloadingLabel: PropTypes.string,
  showProgress: PropTypes.bool,
  showSuccessMessage: PropTypes.bool,
  onDownloadStart: PropTypes.func,
  onDownloadComplete: PropTypes.func,
  onDownloadError: PropTypes.func,
  onVideoDeleted: PropTypes.func,
}
