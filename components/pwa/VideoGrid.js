import React from 'react'
import { useTranslation } from 'next-i18next'
import { formatBytes } from 'utils/strings'

export default function VideoGrid({
  videos,
  selectedVideo,
  onVideoSelect,
  onDeleteClick,
  refresh,
  storageLoading,
}) {
  const { t } = useTranslation('pwa')
  const formatDuration = seconds => {
    if (!seconds || isNaN(seconds) || seconds <= 0) return null

    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    }
  }

  return (
    <div className="rounded-lg bg-white shadow-sm">
      <div className="p-4 sm:p-6 border-b border-gray-200">
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex-1">
            <div className="flex items-center justify-between sm:justify-start">
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-gray-900">
                  {t('downloadedVideos')}
                </h2>
                <p className="mt-1 text-sm text-gray-600">
                  {videos.length}{' '}
                  {videos.length !== 1 ? t('videos') : t('video')}{' '}
                  {t('videoAvailable')}
                </p>
              </div>
              <div className="flex sm:hidden ml-3">
                <button
                  onClick={() => refresh()}
                  disabled={storageLoading}
                  className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  title={t('refreshDownloadedVideos')}
                >
                  <svg
                    className={`h-4 w-4 ${storageLoading ? 'animate-spin' : ''}`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <div className="hidden sm:flex justify-end">
            <button
              onClick={() => refresh()}
              disabled={storageLoading}
              className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title={t('refreshDownloadedVideos')}
            >
              <svg
                className={`h-4 w-4 ${storageLoading ? 'animate-spin' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Scrollable container */}
      <div className="max-h-96 lg:max-h-[500px] overflow-y-auto">
        <div className="p-4 sm:p-6">
          {videos.length === 0 ? (
            <div className="text-center text-gray-500">
              {t('noVideosDownloaded')}
            </div>
          ) : (
            <>
              {/* Mobile: List layout, Desktop: Grid layout */}
              <div className="space-y-3 md:hidden">
                {/* Mobile List View */}
                {videos.map(video => (
                  <div
                    key={video.id}
                    className={`relative rounded-xl border-2 p-3 transition-all duration-200 ${
                      selectedVideo?.id === video.id
                        ? 'border-blue-600 bg-blue-50 shadow-lg ring-2 ring-blue-200 ring-opacity-50'
                        : 'border-gray-200'
                    }`}
                  >
                    {/* Delete button - positioned in top right corner */}
                    <button
                      type="button"
                      className="absolute -top-2 -right-2 z-10 p-1.5 bg-danger-600 text-white rounded-full shadow-md transition-all duration-200 opacity-100 hover:opacity-100"
                      onClick={e => onDeleteClick(e, video)}
                      title={t('deleteVideo')}
                    >
                      <svg
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>

                    <button
                      type="button"
                      className="w-full cursor-pointer text-left focus:outline-none"
                      onClick={() => onVideoSelect(video)}
                    >
                      <div className="flex flex-row space-x-3">
                        {/* Thumbnail - Full height on mobile */}
                        <div
                          className={`relative w-28 h-20 flex-shrink-0 overflow-hidden rounded-lg bg-gray-100 ${
                            selectedVideo?.id === video.id
                              ? 'ring-2 ring-blue-300'
                              : ''
                          }`}
                        >
                          {video.thumbnailBlobUrl || video.thumbnailUrl ? (
                            <img
                              src={video.thumbnailBlobUrl || video.thumbnailUrl}
                              alt={video.title || t('videoThumbnail')}
                              className="h-full w-full object-cover"
                              onError={e => {
                                e.target.style.display = 'none'
                                e.target.nextSibling.style.display = 'flex'
                              }}
                            />
                          ) : null}
                          {/* Fallback icon when no thumbnail */}
                          <div
                            className={`absolute inset-0 flex items-center justify-center ${
                              video.thumbnailBlobUrl || video.thumbnailUrl
                                ? 'hidden'
                                : 'flex'
                            }`}
                          >
                            <svg
                              className="h-4 w-4 text-gray-400"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1.5}
                                d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                              />
                            </svg>
                          </div>

                          {/* Duration badge */}
                          {video.duration && (
                            <div className="absolute bottom-1 right-1">
                              <div className="bg-black bg-opacity-70 text-white text-xs font-medium px-1 py-0.5 rounded-md">
                                {formatDuration(video.duration)}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Video info */}
                        <div className="flex-1 space-y-1 min-w-0">
                          {(video.channel || video.show) && (
                            <p className="text-xs text-gray-500 line-clamp-1">
                              {[video.channel, video.show]
                                .filter(Boolean)
                                .join(' - ')}
                            </p>
                          )}
                          <h3
                            className={`line-clamp-2 text-sm font-semibold ${
                              selectedVideo?.id === video.id
                                ? 'text-blue-700'
                                : 'text-gray-900'
                            }`}
                          >
                            {video.title || t('untitledVideo')}
                          </h3>
                          {video.subtitle && (
                            <p className="text-xs text-gray-600 line-clamp-1">
                              {video.subtitle}
                            </p>
                          )}
                          <div className="text-xs text-gray-500">
                            <span>{formatBytes(video.size)}</span>
                          </div>
                        </div>
                      </div>
                    </button>
                  </div>
                ))}
              </div>

              {/* Desktop Grid View */}
              <div className="hidden md:grid md:grid-cols-3 xl:grid-cols-4 gap-6">
                {videos.map(video => (
                  <div
                    key={video.id}
                    className={`group relative rounded-xl border-2 p-4 transition-all duration-200 md:hover:shadow-md ${
                      selectedVideo?.id === video.id
                        ? 'border-blue-600 bg-blue-50 shadow-lg ring-2 ring-blue-200 ring-opacity-50'
                        : 'border-gray-200 md:hover:border-gray-300'
                    }`}
                  >
                    {/* Delete button - positioned in top right corner */}
                    <button
                      type="button"
                      className={`absolute -top-2 -right-2 z-10 p-1.5 bg-danger-600 text-white rounded-full shadow-md transition-all duration-200 opacity-100 md:opacity-0 md:group-hover:opacity-100 ${
                        selectedVideo?.id === video.id ? 'md:!opacity-100' : ''
                      }`}
                      onClick={e => onDeleteClick(e, video)}
                      title={t('deleteVideo')}
                    >
                      <svg
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>

                    <button
                      type="button"
                      className="w-full cursor-pointer text-left focus:outline-none"
                      onClick={() => onVideoSelect(video)}
                    >
                      <div className="space-y-3">
                        {/* Thumbnail - Full width on desktop */}
                        <div
                          className={`relative w-full aspect-video overflow-hidden rounded-lg bg-gray-100 ${
                            selectedVideo?.id === video.id
                              ? 'ring-2 ring-blue-300'
                              : ''
                          }`}
                        >
                          {video.thumbnailBlobUrl || video.thumbnailUrl ? (
                            <img
                              src={video.thumbnailBlobUrl || video.thumbnailUrl}
                              alt={video.title || t('videoThumbnail')}
                              className="h-full w-full object-cover transition-transform duration-200 md:group-hover:scale-105"
                              onError={e => {
                                e.target.style.display = 'none'
                                e.target.nextSibling.style.display = 'flex'
                              }}
                            />
                          ) : null}
                          {/* Fallback icon when no thumbnail */}
                          <div
                            className={`absolute inset-0 flex items-center justify-center ${
                              video.thumbnailBlobUrl || video.thumbnailUrl
                                ? 'hidden'
                                : 'flex'
                            }`}
                          >
                            <svg
                              className="h-12 w-12 text-gray-400"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1.5}
                                d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                              />
                            </svg>
                          </div>

                          {/* Duration badge */}
                          {video.duration && (
                            <div className="absolute bottom-2 right-2">
                              <div className="bg-black bg-opacity-70 text-white text-xs font-medium px-2 py-1 rounded-md">
                                {formatDuration(video.duration)}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Video info */}
                        <div className="space-y-2">
                          <div>
                            {(video.channel || video.show) && (
                              <p className="text-xs text-gray-500 line-clamp-1 mb-1">
                                {[video.channel, video.show]
                                  .filter(Boolean)
                                  .join(' - ')}
                              </p>
                            )}
                            <h3
                              className={`line-clamp-2 text-sm font-semibold ${
                                selectedVideo?.id === video.id
                                  ? 'text-blue-700'
                                  : 'text-gray-900 md:group-hover:text-blue-600'
                              }`}
                            >
                              {video.title || t('untitledVideo')}
                            </h3>
                            {video.subtitle && (
                              <p className="text-xs text-gray-600 line-clamp-1 mt-1">
                                {video.subtitle}
                              </p>
                            )}
                          </div>

                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>{formatBytes(video.size)}</span>
                          </div>
                        </div>
                      </div>
                    </button>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
