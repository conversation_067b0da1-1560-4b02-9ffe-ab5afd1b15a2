import React from 'react'
import { useTranslation } from 'next-i18next'

export default function OfflineIndicator({ isOnline, showBackOnline }) {
  const { t } = useTranslation('pwa')
  if (isOnline && !showBackOnline) return null

  return (
    <div
      className={`mt-4 p-4 rounded-lg ${
        showBackOnline
          ? 'bg-success-100 border border-success-200'
          : 'bg-warning-100 border border-warning-200'
      }`}
    >
      <div className="flex items-center">
        {showBackOnline ? (
          // Check icon for back online
          <svg
            className="w-5 h-5 text-success-800 mr-2 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clipRule="evenodd"
            />
          </svg>
        ) : (
          // Warning icon for offline
          <svg
            className="w-5 h-5 text-warning-800 mr-2 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
        )}
        <div>
          <p
            className={`font-medium ${
              showBackOnline ? 'text-success-800' : 'text-warning-800'
            }`}
          >
            {showBackOnline ? t('backOnline') : t('currentlyOffline')}
          </p>
          <p
            className={`text-sm ${
              showBackOnline ? 'text-success-700' : 'text-warning-700'
            }`}
          >
            {showBackOnline ? t('backOnlineMessage') : t('offlineMessage')}
          </p>
        </div>
      </div>
    </div>
  )
}
