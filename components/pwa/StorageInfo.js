import React from 'react'
import { useTranslation } from 'next-i18next'
import { formatBytes } from 'utils/strings'

export default function StorageInfo({
  storageUsage,
  videos,
  refresh,
  storageLoading,
}) {
  const { t } = useTranslation('pwa')
  if (!storageUsage) return null

  return (
    <div className="flex-shrink-0 bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Mobile Layout */}
      <div className="sm:hidden">
        <div className="flex items-center justify-between p-3">
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                {t('videosStored')}:
              </span>
              <span className="text-sm font-semibold text-gray-900">
                {videos.length} ({formatBytes(storageUsage.used)})
              </span>
            </div>
            {storageUsage.available !== undefined && (
              <div className="flex items-center justify-between mt-1">
                <span className="text-sm text-gray-600">{t('available')}:</span>
                <span className="text-sm font-semibold text-gray-900">
                  {formatBytes(storageUsage.available || 0)}
                </span>
              </div>
            )}
          </div>
          <button
            onClick={() => refresh()}
            disabled={storageLoading}
            className="ml-3 flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title={t('refreshStorageInfo')}
          >
            <svg
              className={`h-4 w-4 ${storageLoading ? 'animate-spin' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden sm:block p-3">
        <div className="flex items-start justify-between gap-3">
          <div className="text-sm space-y-1">
            <div className="flex items-center justify-between gap-4">
              <span className="text-gray-600">{t('videosStored')}:</span>
              <span className="font-medium text-gray-900">
                {videos.length} ({formatBytes(storageUsage.used)})
              </span>
            </div>
            {storageUsage.available !== undefined && (
              <div className="flex items-center justify-between gap-4">
                <span className="text-gray-600">{t('available')}:</span>
                <span className="font-medium text-gray-900">
                  {formatBytes(storageUsage.available || 0)}
                </span>
              </div>
            )}
          </div>
          <button
            onClick={() => refresh()}
            disabled={storageLoading}
            className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title={t('refreshStorageInfo')}
          >
            <svg
              className={`h-4 w-4 ${storageLoading ? 'animate-spin' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  )
}
