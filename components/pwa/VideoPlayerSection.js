import React from 'react'
import { useTranslation } from 'next-i18next'
import OfflineVideoPlayer from './OfflineVideoPlayer'
import RichText from 'ui/typography/RichText'

export default function VideoPlayerSection({ selectedVideo }) {
  const { t } = useTranslation('pwa')
  return (
    <div className="rounded-lg bg-white shadow-sm">
      {selectedVideo ? (
        <div>
          <div className="aspect-video w-full overflow-hidden rounded-lg bg-black mb-4">
            <OfflineVideoPlayer video={selectedVideo} />
          </div>

          <div>
            {(selectedVideo.channel || selectedVideo.show) && (
              <p className="text-sm text-gray-500 mb-1">
                {[selectedVideo.channel, selectedVideo.show]
                  .filter(Boolean)
                  .join(' - ')}
              </p>
            )}
            <h3 className="text-lg font-medium text-gray-900">
              {selectedVideo.title || t('untitledVideo')}
            </h3>
            {selectedVideo.subtitle && (
              <p className="text-sm text-gray-600 mt-1">
                {selectedVideo.subtitle}
              </p>
            )}
            {selectedVideo.body && (
              <div className="mt-2">
                <RichText doc={selectedVideo.body} />
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="flex h-64 lg:h-80 items-center justify-center rounded-lg bg-gray-100">
          <div className="text-center">
            <div className="text-gray-400">
              <svg
                className="mx-auto h-12 w-12 lg:h-16 lg:w-16"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
            </div>
            <p className="mt-2 text-sm lg:text-base text-gray-500">
              {t('selectVideoToPlay')}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
