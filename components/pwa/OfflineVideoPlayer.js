import React, { useState, useEffect, useRef } from 'react'
import { useTranslation } from 'next-i18next'

// Safari-compatible video component
export default function OfflineVideoPlayer({ video }) {
  const { t } = useTranslation('pwa')
  const videoRef = useRef(null)
  const [videoSrc, setVideoSrc] = useState(null)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (video && video.blob) {
      try {
        // Create a fresh blob URL for Safari
        const blobUrl = URL.createObjectURL(video.blob)
        setVideoSrc(blobUrl)
        setError(null)

        // Cleanup function
        return () => {
          try {
            URL.revokeObjectURL(blobUrl)
          } catch (revokeError) {
            // Ignore revocation errors - blob URL might already be revoked
          }
        }
      } catch (blobError) {
        setError(t('failedToPrepareVideo'))
      }
    } else {
      setVideoSrc(null)
      setError(null)
    }
  }, [t, video])

  const handleError = () => {
    setError(t('videoPlaybackFailed'))

    // Try recreating the blob URL
    if (video && video.blob) {
      try {
        // Clean up old blob URL if it exists
        if (videoSrc) {
          URL.revokeObjectURL(videoSrc)
        }

        const newBlobUrl = URL.createObjectURL(video.blob)
        setVideoSrc(newBlobUrl)
      } catch (recreateError) {
        setError(t('unableToRecoverVideo'))
      }
    }
  }

  const handleLoadStart = () => {
    setError(null)
  }

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center rounded-lg bg-red-50 border border-red-200">
        <div className="text-center p-4">
          <div className="text-red-500 mb-2">
            <svg
              className="mx-auto h-8 w-8"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <p className="text-sm text-red-600">{error}</p>
          <button
            onClick={() => setError(null)}
            className="mt-2 text-xs text-red-500 underline hover:text-red-700"
          >
            {t('tryAgain')}
          </button>
        </div>
      </div>
    )
  }

  return (
    <video
      ref={videoRef}
      controls
      className="h-full w-full"
      src={videoSrc}
      preload="metadata"
      playsInline
      onError={handleError}
      onLoadStart={handleLoadStart}
    >
      <track kind="captions" />
      {t('browserNotSupported')}
    </video>
  )
}
