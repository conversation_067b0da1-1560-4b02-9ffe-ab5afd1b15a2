import PropTypes from 'prop-types'

import Head from 'next/head'

import { getImageUrl } from 'utils/images'

const icons = [
  {
    name: 'apple-touch-icon',
    size: 180,
    rel: 'apple-touch-icon',
  },
  {
    name: 'favicon-32x32',
    size: 32,
    rel: 'icon',
  },
  {
    name: 'favicon-16x16',
    size: 16,
    rel: 'icon',
  },
]

export default function PageFavicon({ favicon }) {
  return (
    <Head>
      {icons.map(icon => (
        <link
          key={icon.name}
          rel={icon.rel}
          sizes={`${icon.size}x${icon.size}`}
          href={
            favicon
              ? getImageUrl(favicon, `w:${icon.size},h:${icon.size}`)
              : `/${icon.name}.png`
          }
        />
      ))}

      {/* <link rel="manifest" href="/site.webmanifest" /> */}
    </Head>
  )
}
PageFavicon.propTypes = {
  favicon: PropTypes.object,
}
