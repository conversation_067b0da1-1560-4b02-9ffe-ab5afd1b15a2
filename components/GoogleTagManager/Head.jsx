import { useGTMSettings } from './hooks'

/**
 * Renders GTM head script
 * @param {Object} site
 * @returns {JSX.Element} GTM head script
 * @returns {null} null if no GTM id
 */
export default function GoogleTagManagerHead({ site }) {
  const { id } = useGTMSettings(site)

  if (!id) return null

  return (
    // eslint-disable-next-line @next/next/next-script-for-ga
    <script
      id="gtm"
      dangerouslySetInnerHTML={{
        __html: `(function(w,d,s,l,i){
  w[l]=w[l]||[];
  w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});
  var f=d.getElementsByTagName(s)[0], 
      j=d.createElement(s),
      dl=l!='dataLayer'?'&l='+l:'';
      j.async=true;
      j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
      f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','${id}');`,
      }}
    />
  )
}
