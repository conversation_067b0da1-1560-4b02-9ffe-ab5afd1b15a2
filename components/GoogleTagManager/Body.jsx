import { useGTMSettings } from './hooks'

/**
 * Renders GTM body script
 * @param {Object} site
 * @returns {JSX.Element} GTM body script
 * @returns {null} null if no GTM id
 */
export default function GoogleTagManagerBody({ site }) {
  const { id } = useGTMSettings(site)

  if (!id) return null

  return (
    <noscript>
      <iframe
        title="GTM No Script"
        src={`https://www.googletagmanager.com/ns.html?id=${id}`}
        height="0"
        width="0"
        style={{ display: 'none', visibility: 'hidden' }}
      ></iframe>
    </noscript>
  )
}
