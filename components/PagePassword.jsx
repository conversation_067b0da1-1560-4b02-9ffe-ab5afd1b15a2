import PropTypes from 'prop-types'
import { useCallback, useMemo, useState } from 'react'

import { useProtectedPage } from 'hooks/useProtectedPage'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import Head from 'next/head'
import <PERSON><PERSON> from 'ui/feedback/Alert'
import { useRouter } from 'next/router'

const Header = dynamic(() => import('ui/data-display/Header'))
const Form = dynamic(() => import('ui/data-entry/Form'))
const Input = dynamic(() => import('ui/data-entry/Input'))
const Submit = dynamic(() => import('ui/data-entry/Submit'))

export default function PagePassword({ page }) {
  const { t } = useTranslation()
  const router = useRouter()

  const [isAccessing, setIsAccessing] = useState(false)

  const { mutate, error } = useProtectedPage(page)

  const onSubmit = useCallback(
    data => {
      setIsAccessing(true)
      mutate(data, {
        onSuccess: async () => {
          router.reload()
        },
        onError: () => {
          setIsAccessing(false)
        },
      })
    },
    [mutate, router]
  )

  const displayedError = useMemo(() => {
    if (error?.status === 401) {
      return {
        type: 'warning',
        message: t('passwordInvalid'),
      }
    }

    return {
      type: 'danger',
      message: t('passwordError'),
    }
  }, [error?.status, t])

  return (
    <div className="flex grow flex-col items-center justify-center">
      <Head>
        <title>{page.title}</title>
      </Head>
      <div className="max-w-md space-y-8 px-6 py-9">
        <Header
          title={t('protectedPageTitle')}
          subtitle={t('protectedPageSubtitle')}
        />

        {error && (
          <Alert type={displayedError.type} message={displayedError.message} />
        )}

        <Form onSubmit={onSubmit}>
          <Input label={t('password')} name="password" type="password" />
          <Submit label={t('access')} disabled={isAccessing} />
        </Form>
      </div>
    </div>
  )
}
PagePassword.propTypes = {
  page: PropTypes.object,
}
