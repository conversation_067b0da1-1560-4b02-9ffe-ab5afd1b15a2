import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const NotFound = dynamic(() => import('ui/feedback/NotFound'))

export default function PageNotFound({ page }) {
  const { t } = useTranslation()

  return (
    <div className="flex grow flex-col items-center justify-center">
      <NotFound
        code="404"
        title={t('pageNotFound')}
        headTitle={`${t('pageNotFound')} | ${page?.site?.title}}`}
        description={t('pageNotFoundDescription')}
      />
    </div>
  )
}
PageNotFound.propTypes = {
  page: PropTypes.object,
}
