import PropTypes from 'prop-types'
import React, { useContext } from 'react'

import Input from 'ui/data-entry/Input'
import useWidth from 'ui/helpers/useWidth'

import { FormContext, useRegisterField } from './context'
import useBorderRadius from 'ui/helpers/useBorderRadius'
import useBorder from 'ui/helpers/useBorder'
import { getBackgroundColor, getTextColor } from 'ui/helpers/getColor'
import clsx from 'clsx'
import usePadding from 'ui/helpers/usePadding'

const acceptImage = 'image/*, .png, .jpg, .jpeg'

export default function FormInput({
  className,
  disabled,
  help,
  label,
  name,
  placeholder,
  required,
  type = 'text',
  width,
  accept,
  emailErrorMessage,
  requiredMessage,
  labelColor,
  borderRadius,
  border,
  padding,
  backgroundColor,
}) {
  const { fieldError } = useContext(FormContext) // This is only going to be used for the email field.
  const borderRadiusClasses = useBorderRadius(borderRadius)
  const borderClasses = useBorder(border)
  const bgColorClass = getBackgroundColor(backgroundColor)
  const paddingClass = usePadding(padding)
  const labelClass = getTextColor(labelColor)

  const errorMessages = {
    required: requiredMessage,
    emailNotValid: emailErrorMessage,
  }

  const widthClass = useWidth(width, 'full')

  // Make the accept array of objects into a string. Where what we want is the value of the object.
  const acceptString =
    type === 'file'
      ? accept?.length > 0
        ? accept.map(a => a.value).join(',')
        : acceptImage // If there is no accept, then we want to default to images.
      : undefined

  // If type is file, we set the help to include max size
  const helpText = help && type === 'file' ? `${help} - (max 10MB)` : help

  useRegisterField(name, { field: 'input', label, required, disabled, type })

  return (
    <Input
      className={`${widthClass} ${className}`}
      disabled={disabled}
      help={helpText}
      label={label}
      labelClass={labelClass}
      name={name}
      placeholder={placeholder}
      required={required}
      type={type}
      accept={acceptString}
      errorMessages={errorMessages}
      fieldError={fieldError}
      inputClass={clsx(
        borderClasses,
        borderRadiusClasses,
        bgColorClass,
        paddingClass
      )}
    />
  )
}
FormInput.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  help: PropTypes.string,
  label: PropTypes.string,
  name: PropTypes.string,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  type: PropTypes.oneOf(['text', 'email', 'number', 'tel', 'file']),
  width: PropTypes.object,
  accept: PropTypes.array,
}
