import { useMutation } from 'react-query'

import { postRequest } from 'utils/fetcher'

/**
 * Default fetch for "post" methods
 * @param {string} url - The url to fetch
 */
export function useFormMutation(options, { pageId, blockId } = {}) {
  return useMutation(formData => {
    return postRequest('/web/feedback/form', formData, {
      headers: {
        'Page-Id': pageId,
        'Block-Id': blockId,
      },
    })
  }, options)
}
