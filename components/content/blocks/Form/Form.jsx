import PropTypes from 'prop-types'
import React, { useCallback, useMemo, useState } from 'react'
import { useRef } from 'react'

import clsx from 'clsx'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

import Button from 'ui/buttons/Button'
import UIForm from 'ui/data-entry/Form'
import Submit from 'ui/data-entry/Submit'
import useWidth from 'ui/helpers/useWidth'

import FormContextProvider from './FormContext'
import { useFormMutation } from './mutation'

const Alert = dynamic(() => import('ui/feedback/Alert'))

function getResetId() {
  return new Date().getTime().toString()
}

export default function Form({
  children,
  className,
  formTitle,
  contactEmails,
  id,
  pageData,
  submitErrorTitle,
  submitErrorMessage,
  submitLabel,
  sendAnotherLabel,
  submitSuccessTitle,
  submitSuccessMessage,
  variant,
  width,
  blockId,
}) {
  const resetId = useRef(id ?? getResetId())
  const [fieldsConfig, setFieldsConfig] = useState({})
  const { t, i18n } = useTranslation('form', { useSuspense: false })

  const { mutate, isLoading, isSuccess, error, reset } = useFormMutation(
    {
      onSuccess: () => {
        resetId.current = id ? `${id}-${getResetId()}` : getResetId()
      },
    },
    {
      pageId: pageData?.id,
      blockId,
    }
  )

  const fieldError = useMemo(() => {
    // TODO: Set this up to handle multiple error types. Currently we only want to show the email error.
    if (error?.message) {
      try {
        // errors is stringified object in error.message, of which many errors could be returned.
        return JSON.parse(error.message)?.errors?.some(
          e => e.type === 'string.email'
        )
          ? { type: 'emailNotValid' } // When email is invalid, show specific error
          : { type: 'error' } // If error is true, but not the specific email error, then show general error
      } catch (e) {
        // If there's an error parsing, return a specific error object
        return { type: 'invalidJSON' }
      }
    }
  }, [error])

  const widthClass = useWidth(width)

  const onSubmit = useCallback(
    (data, setSubmitting) => {
      const formData = new FormData()

      formData.append('contactEmails', contactEmails)

      const fields = []

      Object.keys(data).forEach(fieldName => {
        const field = {
          ...fieldsConfig[fieldName],
          value: data[fieldName],
        }

        if (field.type === 'file') {
          formData.append(`files`, field.value) // Adds every file to the form data
        } else {
          fields.push(field)
        }
      })

      formData.append('fields', JSON.stringify(fields))

      formData.append('formTitle', formTitle)
      formData.append('formUrl', pageData.absoluteUrl)
      formData.append('language', i18n.language)

      mutate(formData, {
        onSettled: () => {
          setSubmitting(false)
        },
      })
    },
    [
      mutate,
      contactEmails,
      fieldsConfig,
      formTitle,
      i18n.language,
      pageData.absoluteUrl,
    ]
  )

  const registerField = useCallback(
    (name, config) => {
      fieldsConfig[name] = config

      setFieldsConfig(fieldsConfig)
    },
    [fieldsConfig]
  )

  if (!contactEmails) {
    return null
  }

  if (isSuccess) {
    return (
      <div className={`flex flex-col space-y-4 ${className}`}>
        <Alert
          title={submitSuccessTitle || t('sendSuccessTitle')}
          type="success"
          message={submitSuccessMessage || t('sendSuccessMessage')}
        />
        <Button
          className="self-end"
          size="md"
          onClick={() => reset()}
          icon="arrow-right-long"
          label={sendAnotherLabel || t('sendAnother')}
        />
      </div>
    )
  }

  return (
    <UIForm
      variant={variant}
      onSubmit={onSubmit}
      className={clsx(widthClass, className)}
      id={resetId.current}
    >
      {error && (
        <Alert
          title={submitErrorTitle || t('sendErrorTitle')}
          message={
            error?.response?.data?.message ||
            submitErrorMessage ||
            t('sendErrorMessage')
          }
          type="danger"
        />
      )}
      <FormContextProvider
        registerField={registerField}
        fieldError={fieldError}
      >
        {children}
      </FormContextProvider>
      <div className="self-end">
        <Submit label={submitLabel || t('submit')} loading={isLoading} />
      </div>
    </UIForm>
  )
}
Form.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  contactEmails: PropTypes.string,
  formTitle: PropTypes.string,
  id: PropTypes.string,
  pageData: PropTypes.shape({
    absoluteUrl: PropTypes.string,
    id: PropTypes.string,
  }),
  submitErrorTitle: PropTypes.string,
  submitErrorMessage: PropTypes.string,
  submitLabel: PropTypes.string,
  sendAnotherLabel: PropTypes.string,
  submitSuccessTitle: PropTypes.string,
  submitSuccessMessage: PropTypes.string,
  variant: PropTypes.string,
  width: PropTypes.string,
  blockId: PropTypes.string,
}
