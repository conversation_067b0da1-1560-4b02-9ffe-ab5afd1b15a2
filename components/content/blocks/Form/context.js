import { createContext, useContext, useEffect, useRef } from 'react'

export const FormContext = createContext()

export function useRegisterField(name, config) {
  const { registerField } = useContext(FormContext)
  const registeredRef = useRef(false)

  useEffect(() => {
    if (typeof registerField === 'function' && !registeredRef.current) {
      registerField(name, config)

      registeredRef.current = true
    }
  }, [registerField, name, config])
}
