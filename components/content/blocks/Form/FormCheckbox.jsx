import PropTypes from 'prop-types'
import React from 'react'

import Checkbox from 'ui/data-entry/Checkbox'
import useWidth from 'ui/helpers/useWidth'

import { useRegisterField } from './context'
import useBorderRadius from 'ui/helpers/useBorderRadius'
import useBorder from 'ui/helpers/useBorder'
import { getBackgroundColor, getTextColor } from 'ui/helpers/getColor'
import clsx from 'clsx'
import usePadding from 'ui/helpers/usePadding'

export default function FormCheckbox({
  className,
  defaultChecked,
  disabled,
  help,
  label,
  name,
  required,
  width,
  requiredMessage,
  labelColor,
  borderRadius,
  border,
  padding,
  backgroundColor,
}) {
  const borderRadiusClasses = useBorderRadius(borderRadius)
  const borderClasses = useBorder(border)
  const bgColorClass = getBackgroundColor(backgroundColor)
  const paddingClass = usePadding(padding)
  const labelClass = getTextColor(labelColor)
  const errorMessages = {
    required: requiredMessage,
  }

  const widthClass = useWidth(width, 'full')

  useRegisterField(name, { field: 'checkbox', label, required, disabled })

  return (
    <Checkbox
      className={`${widthClass} ${className}`}
      defaultValue={defaultChecked}
      disabled={disabled}
      help={help}
      label={label}
      labelClass={labelClass}
      name={name}
      required={required}
      errorMessages={errorMessages}
      inputClass={clsx(
        borderClasses,
        borderRadiusClasses,
        bgColorClass,
        paddingClass
      )}
    />
  )
}
FormCheckbox.propTypes = {
  className: PropTypes.string,
  defaultChecked: PropTypes.bool,
  disabled: PropTypes.bool,
  help: PropTypes.string,
  label: PropTypes.string,
  name: PropTypes.string,
  required: PropTypes.bool,
  width: PropTypes.object,
}
