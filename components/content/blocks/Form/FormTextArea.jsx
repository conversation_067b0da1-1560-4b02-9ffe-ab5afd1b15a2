import PropTypes from 'prop-types'
import React from 'react'

import TextArea from 'ui/data-entry/TextArea'
import useWidth from 'ui/helpers/useWidth'

import { useRegisterField } from './context'
import useBorderRadius from 'ui/helpers/useBorderRadius'
import useBorder from 'ui/helpers/useBorder'
import { getBackgroundColor, getTextColor } from 'ui/helpers/getColor'
import clsx from 'clsx'
import usePadding from 'ui/helpers/usePadding'

export default function FormTextArea({
  className,
  disabled,
  help,
  label,
  name,
  placeholder,
  required,
  rows,
  width,
  requiredMessage,
  labelColor,
  borderRadius,
  border,
  padding,
  backgroundColor,
}) {
  const borderRadiusClasses = useBorderRadius(borderRadius)
  const borderClasses = useBorder(border)
  const bgColorClass = getBackgroundColor(backgroundColor)
  const paddingClass = usePadding(padding)
  const labelClass = getTextColor(labelColor)
  const widthClass = useWidth(width, 'full')
  const errorMessages = {
    required: requiredMessage,
  }

  useRegisterField(name, { field: 'textarea', label, required, disabled })

  return (
    <TextArea
      className={`${widthClass} ${className}`}
      disabled={disabled}
      help={help}
      label={label}
      labelClass={labelClass}
      name={name}
      placeholder={placeholder}
      required={required}
      rows={rows}
      errorMessages={errorMessages}
      inputClass={clsx(
        borderClasses,
        borderRadiusClasses,
        bgColorClass,
        paddingClass
      )}
    />
  )
}
FormTextArea.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  help: PropTypes.string,
  label: PropTypes.string,
  name: PropTypes.string,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  rows: PropTypes.number,
  width: PropTypes.object,
}
