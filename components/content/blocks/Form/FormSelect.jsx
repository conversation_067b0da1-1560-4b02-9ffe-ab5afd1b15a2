import React, { useMemo } from 'react'

import Select from 'ui/data-entry/Select'
import useWidth from 'ui/helpers/useWidth'

import { useReg<PERSON>Field } from './context'
import useBorderRadius from 'ui/helpers/useBorderRadius'
import useBorder from 'ui/helpers/useBorder'
import { getBackgroundColor, getTextColor } from 'ui/helpers/getColor'
import clsx from 'clsx'
import usePadding from 'ui/helpers/usePadding'

/**
 * Select component that can be used to create a select input in the form.
 * @param {Object} props The component props
 * @param {string} props.className The class name of the select input.
 * @param {string} props.defaultValue The default value of the select input.
 * @param {boolean} props.disabled The disabled state of the select input.
 * @param {string} props.help The help text of the select input.
 * @param {string} props.label The label of the select input.
 * @param {string} props.name The name of the select input.
 * @param {string} props.placeholder The placeholder of the select input.
 * @param {{label: string, value: string}[]} props.options The options of the select input.
 * @param {boolean} props.required The required state of the select input.
 * @param {Object} props.width The width of the select input.
 * @param {string} props.requiredMessage The required message of the select input.
 * @param {string} props.labelColor The color of the label.
 * @param {string} props.borderRadius The border radius of the select input.
 * @param {Object} props.border The border of the select input.
 * @param {Object} props.padding The padding of the select input.
 * @param {string} props.backgroundColor The background color of the select input.
 * @returns {React.ReactElement} The Select component.
 */
export default function FormSelect({
  className,
  defaultValue,
  disabled,
  help,
  label,
  name,
  placeholder,
  options,
  required,
  width,
  requiredMessage,
  labelColor,
  borderRadius,
  border,
  padding,
  backgroundColor,
}) {
  const borderRadiusClasses = useBorderRadius(borderRadius)
  const borderClasses = useBorder(border)
  const bgColorClass = getBackgroundColor(backgroundColor)
  const paddingClass = usePadding(padding)
  const labelClass = getTextColor(labelColor)
  const widthClass = useWidth(width, 'full')

  useRegisterField(name, {
    field: 'select',
    label,
    required,
    disabled,
    options,
  })

  const errorMessages = useMemo(
    () => ({
      required: requiredMessage,
    }),
    [requiredMessage]
  )

  return (
    <Select
      className={`${widthClass} ${className}`}
      disabled={disabled}
      help={help}
      label={label}
      labelClass={labelClass}
      name={name}
      required={required}
      options={options}
      defaultValue={defaultValue}
      placeholder={placeholder || '-'}
      errorMessages={errorMessages}
      selectClass={clsx(
        borderClasses,
        borderRadiusClasses,
        bgColorClass,
        paddingClass
      )}
    />
  )
}
