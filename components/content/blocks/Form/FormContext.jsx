import PropTypes from 'prop-types'
import React from 'react'
import { FormContext } from './context'

export default function FormContextProvider({
  children,
  registerField,
  fieldError,
}) {
  return (
    <FormContext.Provider value={{ registerField, fieldError }}>
      {children}
    </FormContext.Provider>
  )
}
FormContextProvider.propTypes = {
  children: PropTypes.node,
  registerField: PropTypes.func,
}
