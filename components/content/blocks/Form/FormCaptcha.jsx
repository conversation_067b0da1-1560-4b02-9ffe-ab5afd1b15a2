import PropTypes from 'prop-types'
import React from 'react'

import Captcha from 'ui/data-entry/Captcha'
import useWidth from 'ui/helpers/useWidth'

import { useRegisterField } from './context'

export default function FormCaptcha({
  className,
  defaultChecked,
  disabled,
  help,
  label,
  width,
  requiredMessage,
}) {
  const errorMessages = {
    required: requiredMessage,
  }

  const widthClass = useWidth(width, 'full')

  useRegisterField('captcha', {
    field: 'captcha',
    label,
    required: true,
    disabled,
  })

  return (
    <Captcha
      className={`${widthClass} ${className}`}
      defaultValue={defaultChecked}
      disabled={disabled}
      help={help}
      label={label}
      name="captcha"
      required
      errorMessages={errorMessages}
    />
  )
}
FormCaptcha.propTypes = {
  className: PropTypes.string,
  defaultChecked: PropTypes.bool,
  disabled: PropTypes.bool,
  help: PropTypes.string,
  label: PropTypes.string,
  required: PropTypes.bool,
  width: PropTypes.object,
}
