import PropTypes from 'prop-types'
import React, { useMemo} from 'react'

import { NewletterSubscriptionForm } from 'ui/feedback/NewsletterSubscription'

export default function NewsletterSubscription({
  listsLabel,
  newsletterSubscription = {},
  termsPageUrl,
  ...rest
}) {
  const { fields } = newsletterSubscription

  const fieldsWithLists = useMemo(() => {
    if (!fields?.length) {
      return []
    }

    return fields.map(field => {
      if (field.name === 'lists') {
        return {
          ...field,
          label: listsLabel,
        }
      }
      return field
    })
  }, [fields, listsLabel])

  return (
    <NewletterSubscriptionForm
      {...rest}
      action="subscribe"
      termsUrl={termsPageUrl}
      fields={fieldsWithLists}
      className="w-full"
    />
  )
}
NewsletterSubscription.propTypes = {
  newsletterSubscription: PropTypes.object,
  title: PropTypes.string,
  successTitle: PropTypes.string,
  successDescription: PropTypes.string,
  termsUrl: PropTypes.string,
  unsubscribeUrl: PropTypes.string,
  variant: PropTypes.string,
}
