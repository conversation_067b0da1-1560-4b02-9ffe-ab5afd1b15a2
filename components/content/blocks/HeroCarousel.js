import React from 'react'

import dynamic from 'next/dynamic'

const MediaHero = dynamic(() => import('ui/data-display/MediaHero'))
const Carousel = dynamic(() => import('ui/data-display/Carousel'))

/**
 * HeroCarousel component renders a carousel of hero items.
 * Each hero item can include a title, description, image, mobile image, logo, and call-to-action.
 * If only one item is provided, it renders that item directly without a carousel.
 * If no items are provided, it returns null.
 * @param {Object} props The component props.
 * @param {Array} props.items An array of hero items to display in the carousel.
 * @param {number} props.duration The duration for the carousel transition.
 * @param {string} props.gradientBaseColor The base color for the gradient background.
 * @param {string} props.titleTextSize The text size for the hero title.
 * @param {string} props.descriptionTextSize The text size for the hero description.
 * @param {boolean} props.showDescriptionMobile Whether to show the description on mobile devices.
 * @param {string} props.ctaVariant The variant of the call-to-action button.
 * @return {React.Element|null} The rendered HeroCarousel component or null if no items are provided.
 */
export default function HeroCarousel({
  items,
  duration,
  gradientBaseColor,
  titleTextSize,
  descriptionTextSize,
  showDescriptionMobile,
  ctaVariant,
}) {
  const Hero = ({ item }) => (
    <MediaHero
      title={item?.title}
      titleTextSize={titleTextSize}
      description={item?.description}
      descriptionTextSize={descriptionTextSize}
      showDescriptionMobile={showDescriptionMobile}
      image={item?.image}
      mobileImage={item?.mobileImage}
      logo={item?.logo}
      ctaLabel={item?.callToAction}
      ctaUrl={item?.url}
      ctaVariant={ctaVariant}
      gradientBaseColor={gradientBaseColor}
    />
  )

  // If no items are provided or the array is empty, return null
  if (!items || items.length === 0) {
    return null
  }

  // If only one item is provided, render it directly without a carousel
  if (items.length === 1) {
    return <Hero item={items[0]} />
  }

  // If multiple items are provided, render them in a carousel
  return (
    <Carousel duration={duration}>
      {items.map((item, i) => (
        <Hero key={`heroslide-${i}`} item={item} />
      ))}
    </Carousel>
  )
}
