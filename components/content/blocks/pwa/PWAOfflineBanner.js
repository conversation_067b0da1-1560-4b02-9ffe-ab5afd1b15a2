import React, { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'

import { usePageContext } from 'components/PageProvider'
import useOnlineStatus from 'hooks/useOnlineStatus'
import useIsStandalone from 'hooks/useIsStandalone'

/**
 * PWAOfflineBanner block component that shows when the user is offline.
 * Displays a fixed banner at the top of the screen directing users to the downloads page
 * where they can watch downloaded videos. Also shows a brief confirmation when coming back online.
 * Only renders in standalone PWA mode and when not already on the downloads page.
 *
 * @param {Object} props - Component props
 * @param {string} [props.offlineTitle="You're currently offline"] - Title text shown when offline
 * @param {string} [props.offlineDescription='You can still watch your downloaded videos!'] - Description text shown when offline
 * @param {string} [props.offlineLinkText='Go to Downloads'] - Text for the link to downloads page
 * @param {string} [props.backOnlineTitle="You're back online!"] - Title text shown when coming back online
 * @returns {JSX.Element|null} The rendered offline banner or null if conditions aren't met
 */
export default function PWAOfflineBanner({
  offlineTitle = "You're currently offline",
  offlineDescription = 'You can still watch your downloaded videos!',
  offlineLinkText = 'Go to Downloads',
  backOnlineTitle = "You're back online!",
}) {
  const isOnline = useOnlineStatus()
  const isStandalone = useIsStandalone()
  const router = useRouter()
  const [showBackOnline, setShowBackOnline] = useState(false)
  const [wasOffline, setWasOffline] = useState(false)
  const [bannerHeight, setBannerHeight] = useState(0)
  const bannerRef = useRef(null)
  const { site } = usePageContext()

  // Check if we're on the downloads page
  const isOnDownloadsPage = router.asPath.startsWith('/offline-downloads')

  // Update banner height when content changes
  useEffect(() => {
    if (bannerRef.current) {
      const updateHeight = () => {
        setBannerHeight(bannerRef.current.offsetHeight)
      }

      updateHeight()

      // Update height on window resize
      window.addEventListener('resize', updateHeight)
      return () => window.removeEventListener('resize', updateHeight)
    }
  }, [showBackOnline, isOnline])

  // Reset state when navigating to ensure clean state on each page
  useEffect(() => {
    const handleRouteChange = () => {
      // Reset all temporary states when navigating
      setShowBackOnline(false)
      // Don't reset wasOffline here - let the online status effect handle it
    }

    router.events.on('routeChangeStart', handleRouteChange)
    return () => {
      router.events.off('routeChangeStart', handleRouteChange)
    }
  }, [router.events])

  // Handle the "back online" notification
  useEffect(() => {
    if (!isOnline) {
      setWasOffline(true)
      setShowBackOnline(false)
    } else if (wasOffline && isOnline) {
      // User just came back online
      setShowBackOnline(true)
      // Hide the "back online" message after 3 seconds
      const timer = setTimeout(() => {
        setShowBackOnline(false)
        setWasOffline(false)
      }, 3000)
      return () => clearTimeout(timer)
    } else if (isOnline && !wasOffline) {
      // Ensure we're in a clean state when online and never was offline
      setShowBackOnline(false)
    }
  }, [isOnline, wasOffline])

  // Don't render anything if the user is online and we're not showing the "back online" message
  if (
    !site.pwa?.enabled ||
    !isStandalone ||
    isOnDownloadsPage ||
    (isOnline && !showBackOnline)
  ) {
    return null
  }

  // Determine banner styling and content based on online status
  const isShowingBackOnline = isOnline && showBackOnline
  const bannerClasses = isShowingBackOnline
    ? 'fixed top-0 left-0 right-0 z-[99999] bg-success-100 border-b border-success-200 text-success-800 shadow-lg'
    : 'fixed top-0 left-0 right-0 z-[99999] bg-warning-100 border-b border-warning-200 text-warning-800 shadow-lg'

  return (
    <>
      <div ref={bannerRef} className={bannerClasses}>
        <div className="max-w-7xl mx-auto px-4 py-3">
          {isShowingBackOnline ? (
            // Simplified layout for "back online" message
            <div className="flex items-center justify-center gap-2">
              <svg
                className="w-5 h-5 flex-shrink-0"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="text-sm font-medium">{backOnlineTitle}</span>
            </div>
          ) : (
            // Offline layout with better responsive handling
            <div className="space-y-3">
              {/* Top row: Icon + Title */}
              <div className="flex items-center gap-2">
                <svg
                  className="w-5 h-5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm sm:text-base font-medium">
                  {offlineTitle}
                </span>
              </div>

              {/* Bottom row: Description + Button (only if downloads are enabled) */}
              {site.pwa?.enableOfflineDownloads && (
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <span className="text-sm text-warning-700 flex-1">
                    {offlineDescription}
                  </span>

                  <Link
                    href="/offline-downloads"
                    className="inline-flex items-center justify-center px-4 py-2 bg-warning-200 hover:bg-warning-300 text-warning-900 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-warning-500 focus:ring-offset-2 text-sm whitespace-nowrap"
                  >
                    {offlineLinkText}
                    <svg
                      className="w-4 h-4 ml-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </Link>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      {/* Dynamic spacer that matches the actual banner height */}
      <div style={{ height: bannerHeight }} />
    </>
  )
}
