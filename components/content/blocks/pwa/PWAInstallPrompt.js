import { useState } from 'react'

import { usePageContext } from 'components/PageProvider'
import useIsIOS from 'hooks/useIsIOS'
import useIsStandalone from 'hooks/useIsStandalone'

/**
 * PWAInstallPrompt block component that shows iOS users how to install the PWA.
 * Displays a fixed banner at the top of the screen with installation instructions
 * for iOS devices when PWA is enabled and the user is not already in standalone mode.
 *
 * @param {Object} props - Component props
 * @param {string} [props.installPromptTitle='Install the app'] - Title text for the install prompt
 * @param {string} [props.installPromptDescription='Add to your home screen for quick access. Tap the share button then "Add to Home Screen"'] - Description text with installation instructions
 * @returns {JSX.Element|null} The rendered install prompt banner or null if conditions aren't met
 */
export default function PWAInstallPrompt({
  installPromptTitle = 'Install the app',
  installPromptDescription = 'Add to your home screen for quick access. Tap the share button then "Add to Home Screen"',
}) {
  const [isVisible, setIsVisible] = useState(true)
  const isIOS = useIsIOS()
  const isStandalone = useIsStandalone()
  const { site } = usePageContext()

  if (!site.pwa?.enabled || !isIOS || isStandalone || !isVisible) {
    return null
  }

  const handleClose = () => {
    setIsVisible(false)
  }

  return (
    <>
      <div className="fixed top-0 left-0 right-0 z-[9999] bg-color2-800 text-white shadow-lg">
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex-1 mr-4">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                    <span className="text-lg">📱</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold mb-1">
                    {installPromptTitle}
                  </h3>
                  <p className="text-xs text-blue-100 leading-tight">
                    {installPromptDescription}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleClose}
                className="p-2 hover:bg-white hover:bg-opacity-10 rounded-full transition-colors duration-200"
                aria-label="Close install prompt"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Subtle bottom border for depth */}
        <div className="h-0.5 bg-gradient-to-r from-transparent via-white via-opacity-20 to-transparent"></div>
      </div>
      {/* Spacer to push content below the fixed banner */}
      <div className="h-20 sm:h-16" />
    </>
  )
}
