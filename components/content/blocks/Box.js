import React from 'react'

import UIBox from 'ui/data-display/Box'
import useFlex from 'ui/helpers/useFlex'
import usePadding from 'ui/helpers/usePadding'
import useSpacing from 'ui/helpers/useSpacing'
import useWidth from 'ui/helpers/useWidth'
import { getBackgroundColor } from 'ui/helpers/getColor'
import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'
import { getClipPath } from 'ui/helpers/getClipPath'
import usePosition from 'ui/helpers/usePosition'
import { usePositionOffset } from 'ui/helpers/usePositionOffset'
import useBorder from 'ui/helpers/useBorder'
import useOverflow from 'ui/helpers/useOverflow'
import useMaxWidth from 'ui/helpers/useMaxWidth'
import clsx from 'clsx'
import useBorderRadius from 'ui/helpers/useBorderRadius'

/**
 * A Box component that can be used to wrap other components and apply styles to them.
 * @param {Object} props
 * @param {Object} props.align The alignment of the children inside the box.
 * @param {Object} props.backgroundColor The background color of the box.
 * @param {Object} props.border The border of the box.
 * @param {Object} props.borderRadius The border radius of the box.
 * @param {string} props.className The class name of the box.
 * @param {Object} props.clipPath The clip path of the box.
 * @param {Object} props.direction The direction of the box.
 * @param {Object} props.justify The justification of the children inside the box.
 * @param {Object} props.overflow The overflow of the box.
 * @param {Object} props.padding The padding of the box.
 * @param {Object} props.position The position of the box.
 * @param {Object} props.positionOffset The position offset of the box.
 * @param {Object} props.spacing The spacing of the box.
 * @param {Object} props.width The width of the box.
 * @returns {React.ReactElement} The Box component.
 */
export default function Box({
  align = { xs: 'stretch' },
  backgroundColor,
  border,
  borderRadius,
  className,
  clipPath,
  direction = { xs: 'y' },
  justify = { xs: 'start' },
  overflow,
  maxWidth,
  padding = { xs: { top: 'md' } },
  position,
  positionOffset,
  spacing = { xs: 'md' },
  width = { xs: 'full' },
  ...rest
}) {
  const bgColorClass = getBackgroundColor(backgroundColor)
  const borderClasses = useBorder(border)
  const borderRadiusClasses = useBorderRadius(borderRadius)
  const positionClass = usePosition(position)
  const flexClasses = useFlex(direction ?? { xs: 'y' }, align, justify)
  const spacingClasses = useSpacing(spacing)
  const overflowClasses = useOverflow(overflow)
  const paddingClasses = usePadding(padding)
  const widthClasses = useWidth(width)
  const maxWidthClass = useMaxWidth(maxWidth)

  const positionOffsetStyles = usePositionOffset(position, positionOffset)

  const clipPathValue = useValueAtBreakpoint(clipPath)

  return (
    <UIBox
      className={clsx(
        maxWidthClass,
        paddingClasses,
        borderClasses,
        borderRadiusClasses,
        widthClasses,
        bgColorClass,
        overflowClasses,
        flexClasses,
        spacingClasses,
        className
      )}
      positionClass={positionClass}
      style={{ ...getClipPath(clipPathValue), ...positionOffsetStyles }}
      {...rest}
    />
  )
}
