import { useMemo } from 'react'
import PropTypes from 'prop-types'

import { DiscussionEmbed } from 'disqus-react'
import dynamic from 'next/dynamic'

import useWidth from 'ui/helpers/useWidth'
import { languageToLocale } from 'utils/locales'

const CookieBlockedContent = dynamic(() =>
  import('ui/feedback/CookieBlockedContent')
)

export default function DisqusComments({
  identifier,
  shortName,
  title,
  width,
  pageData,
}) {
  const widthClasses = useWidth(width)
  const { absoluteUrl, language, site } = pageData

  const configProp = useMemo(
    () => ({
      identifier: identifier || absoluteUrl,
      url: absoluteUrl,
      title,
      language: languageToLocale(language || site.language), // disqus requires language as a locale
    }),
    [identifier, title, absoluteUrl, language, site]
  )

  return (
    <div className={`space-y-4 ${widthClasses}`}>
      {title && <h3 className="font-bold uppercase text-xl">{title}</h3>}
      <CookieBlockedContent
        blockCondition="DisqusComments"
        typeCondition="functional"
      >
        <DiscussionEmbed shortname={shortName} config={configProp} />
      </CookieBlockedContent>
    </div>
  )
}
DisqusComments.propTypes = {
  identifier: PropTypes.string,
  shortName: PropTypes.string,
  title: PropTypes.string,
  width: PropTypes.object,
  pageData: PropTypes.object,
}
