import PropTypes from 'prop-types'

import BackgroundImage from 'ui/data-display/BackgroundImage'
import { getBackgroundColor } from 'ui/helpers/getColor'
import usePadding from 'ui/helpers/usePadding'
import useSpacing from 'ui/helpers/useSpacing'

export default function ContentPlaceholder({
  children,
  className = '',
  bgColor,
  bgImage,
  bgImageAlign,
  padding,
  spacing,
  dark,
  id,
}) {
  const bgColorClass = getBackgroundColor(bgColor)
  const paddingClass = usePadding(padding)
  const spacingClass = useSpacing(spacing, 'lg')

  return (
    <div
      className={`flex grow flex-col ${bgColorClass} ${paddingClass} ${spacingClass} ${className}`}
      id={id}
    >
      <BackgroundImage
        image={bgImage}
        position={bgImageAlign}
        dark={dark}
        width={1920}
      />
      {children}
    </div>
  )
}
ContentPlaceholder.propTypes = {
  bgColor: PropTypes.string,
  bgImage: PropTypes.object,
  bgImageAlign: PropTypes.string,
  children: PropTypes.node,
  className: PropTypes.string,
  dark: PropTypes.bool,
  id: PropTypes.string,
  padding: PropTypes.object,
  spacing: PropTypes.object,
}
