import { useEffect } from 'react'
import PropTypes from 'prop-types'

import useWidth from 'ui/helpers/useWidth'

export default function HubSpotForm({ formId, portalId, regionId, width }) {
  const widthClass = useWidth(width)

  const targetId = `hubspotForm-${formId}`

  useEffect(() => {
    if (!(formId || portalId)) return null

    const script = document.createElement('script')
    script.src = 'https://js.hsforms.net/forms/v2.js'
    document.body.appendChild(script)

    script.addEventListener('load', () => {
      if (window.hbspt) {
        window.hbspt.forms.create({
          region: regionId || 'na1',
          portalId,
          formId,
          target: `#${targetId}`,
        })
      }
    })
  }, [formId, portalId, targetId, regionId])

  return (
    <div className={widthClass}>
      <div id={targetId}></div>
    </div>
  )
}
HubSpotForm.propTypes = {
  formId: PropTypes.string,
  portalId: PropTypes.string,
  regionId: PropTypes.string,
  width: PropTypes.object,
}
