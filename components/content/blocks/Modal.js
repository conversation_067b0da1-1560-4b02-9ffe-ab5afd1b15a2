import React from 'react'

import { Dialog, DialogContent } from 'ui/feedback/FloatingDialog'

import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'
import { useRouter } from 'next/router'

const boxVariantClasses = {
  right: 'right-0 inset-y-0 pr-0 w-[450px] max-w-full rounded-none', // pr-0 is to remove the padding to accomodate the scroll bar
  middle: 'top-0 bg-white pr-0 rounded-lg max-w-full ',
  left: 'left-0 inset-y-0 pr-0 w-[450px] max-w-full  rounded-none',
}

const closeClasses = {
  right: 'translate-x-96',
  middle: undefined, // Leaving it here for readability
  left: '-translate-x-40',
}

const openClasses = {
  right: 'translate-x-0',
  middle: undefined, // Leaving it here for readability
  left: 'translate-x-0',
}

const contentVariantClasses = {
  right: 'pr-6', // Readds the padding that was removed to accomodate the scroll bar
  middle: 'pr-6',
  left: 'pr-6',
}

// TODO: remove the rest of the props
export default function Modal({
  variant,
  modalName,
  title,
  description,
  children,
  // titleClass,
}) {
  // We take the query from url
  const router = useRouter()
  const { modal } = router.query || {}

  const [isOpen, setIsOpen] = React.useState(false)

  const variantChoice = useValueAtBreakpoint(variant) || 'right'

  React.useEffect(() => {
    if (modalName === modal) {
      return setIsOpen(true)
    }
    if (modalName !== modal && isOpen) {
      setIsOpen(false)
    }
  }, [modal, modalName, isOpen])

  const handleOpenChange = React.useCallback(
    open => {
      // We need to remove the modal to the url query

      if (!open) {
        delete router.query.modal
      }

      router.push(
        {
          pathname: router.pathname,
          query: router.query,
        },
        null,
        {
          shallow: true,
        }
      )
    },
    [router]
  )

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent
        title={title}
        description={description}
        boxClass={boxVariantClasses[variantChoice]}
        contentClass={`overflow-y-auto ${contentVariantClasses[variantChoice]}`}
        openClass={openClasses[variantChoice]}
        closeClass={closeClasses[variantChoice]}
        titleClass={'text-xl font-medium'}
      >
        {children}
      </DialogContent>
    </Dialog>
  )
}
