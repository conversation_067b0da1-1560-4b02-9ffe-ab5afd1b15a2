import React, { Fragment, useEffect, useMemo, useState } from 'react'

import clsx from 'clsx'
import { useTranslation } from 'next-i18next'

import Button from 'ui/buttons/Button'
import { default as UIMap } from 'ui/data-display/Map'
import useAspectRatio from 'ui/helpers/useAspectRatio'
import useWidth from 'ui/helpers/useWidth'

function getBoundsFromLocations(locations) {
  if (locations.length === 1) {
    return undefined
  }

  const initialCoordinates = locations[0].coordinates
  const initialBounds = [
    initialCoordinates[0],
    initialCoordinates[1],
    initialCoordinates[0],
    initialCoordinates[1],
  ]

  return locations.reduce((acc, { coordinates }) => {
    return [
      Math.min(acc[0], coordinates[0]),
      Math.max(acc[1], coordinates[1]),
      Math.max(acc[2], coordinates[0]),
      Math.min(acc[3], coordinates[1]),
    ]
  }, initialBounds)
}

/**
 * @param {Object} props Component props
 * @param {Array} [props.locations] Array of location objects
 * @param {string} [props.className] Additional class names for the map container
 * @param {string} [props.aspectRatio] Aspect ratio for the map
 * @param {string} [props.width] Width of the map
 * @param {boolean} [props.showSearch] Flag to show search control
 * @param {boolean} [props.showControls] Flag to show map controls
 * @param {boolean} [props.showGeolocate] Flag to show geolocation control
 * @param {string} [props.mapStyle] Style uri for the map
 */
export default function Map({
  locations,
  className,
  aspectRatio,
  width,
  showSearch,
  showControls,
  showGeolocate,
  ctrlToZoom,
  resetZoom,
  resetZoomButton,
  mapStyle,
}) {
  const { t } = useTranslation()
  const [viewport, setViewport] = useState()

  const markers = useMemo(
    () =>
      locations.map(({ coordinates, placeName, name, description, url }) => ({
        location: {
          center: true,
          coordinates,
          placeName,
        },
        description,
        url,
        name,
      })),
    [locations]
  )

  useEffect(() => {
    if (locations.length === 1) {
      setViewport({
        longitude: locations[0].coordinates[0],
        latitude: locations[0].coordinates[1],
        zoom: 13,
      })
    }
  }, [locations])

  return (
    <UIMap
      className={clsx(
        'min-h-[320px] w-full',
        useAspectRatio(aspectRatio),
        useWidth(width),
        className
      )}
      boundingBox={getBoundsFromLocations(locations)}
      markers={markers}
      mapStyle={mapStyle || undefined}
      viewport={viewport}
      onMove={setViewport}
      showSearch={showSearch}
      showControls={showControls}
      showGeolocate={showGeolocate}
      cooperativeGestures={ctrlToZoom}
      resetZoom={resetZoom}
      resetZoomButton={resetZoomButton}
      markerPopoverZoom={15}
      renderItemPopover={marker => {
        if (!marker.name && !marker.description && !marker.url) {
          return null
        }

        return (
          <>
            <div className="min-w-24 max-w-64 w-max">
              <h3 className="font-bold text-base">{marker.name}</h3>
              <p>
                {marker.description?.split(/\r?\n/).map((part, index) => (
                  <Fragment key={`map-marker-description-${index}`}>
                    {index > 0 ? <br /> : null}
                    {part}
                  </Fragment>
                ))}
              </p>
            </div>
            {marker.url ? (
              <Button label={t('readMore')} url={marker.url} variant="link" />
            ) : null}
          </>
        )
      }}
    />
  )
}
