import PropTypes from 'prop-types'

import useWidth from 'ui/helpers/useWidth'
import useGap from 'ui/helpers/useGap'
import usePlaceItems from 'ui/helpers/usePlaceItems'
import useColumns from 'ui/helpers/useColumns'

export default function Grid({
  children,
  className = '',
  columns,
  gap,
  id,
  placeItems,
  width,
}) {
  const columnClass = useColumns(columns)
  const gapClass = useGap(gap)
  const placeItemsClass = usePlaceItems(placeItems)
  const widthClass = useWidth(width)

  return (
    <div
      className={`grid grid-flow-row ${columnClass} ${gapClass} ${placeItemsClass} ${widthClass} ${className}`}
      id={id}
    >
      {children}
    </div>
  )
}
Grid.propTypes = {
  id: PropTypes.string,
  className: PropTypes.string,
  children: PropTypes.node,
  columns: PropTypes.object,
  gap: PropTypes.object,
  placeItems: PropTypes.object,
  width: PropTypes.object,
}
