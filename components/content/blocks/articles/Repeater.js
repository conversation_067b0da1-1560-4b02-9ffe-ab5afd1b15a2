import PropTypes from 'prop-types'
import BlockResourcesProvider from '../BlockResourceSourceProvider'
import React from 'react'

export function Repeater({ children, articles, pagination }) {
  if (pagination && articles?.count === 0) return null // NOTE: by default the resource fetcher in the api returns articles.count as 0, unless the block's pagination attribute is set to true, in which case it returns the actual count of articles.

  //   return (
  //     <ScrollList
  //       items={articles.items}
  //       renderItem={article => (
  //         <BlockResourcesProvider key={article.id} value={{ article }}>
  //           {children}
  //         </BlockResourcesProvider>
  //       )}
  //     />
  //   )
  return articles?.items?.map(article => (
    <BlockResourcesProvider key={article.id} value={{ article }}>
      {children}
    </BlockResourcesProvider>
  ))
}

Repeater.propTypes = {
  children: PropTypes.node,
  articles: PropTypes.shape({
    count: PropTypes.number,
    items: PropTypes.arrayOf(PropTypes.object),
  }),
}

export default Repeater
