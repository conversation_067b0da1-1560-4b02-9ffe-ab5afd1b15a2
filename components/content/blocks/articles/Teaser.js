import { useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'
import { getRequest } from 'utils/fetcher'

const Button = dynamic(() => import('ui/buttons/Button'))
const LoadingWrap = dynamic(() => import('ui/feedback/LoadingWrap'))
const ListTitle = dynamic(() => import('ui/data-display/ListTitle'))
const Link = dynamic(() => import('ui/navigation/Link'))
const Articles = dynamic(() => import('./shared/Articles'))

export default function ArticleTeaser({
  articlesTeaser = { items: [], count: 0 },
  columns,
  limit,
  direction,
  detailPageId,
  displayMode,
  featuredItemsCount,
  pageData,
  showDate,
  showDescription,
  showImage,
  showCategoryFilter,
  sortField,
  sortOrder,
  title,
}) {
  const { items: initialItems, categories, listPagePath } = articlesTeaser
  const { site } = pageData
  const { t } = useTranslation()
  const [categoryFilter, setCategoryFilter] = useState(null)
  const [items, setItems] = useState(initialItems)
  const [loading, setLoading] = useState(false)

  const showFilters = categories?.length > 1 && showCategoryFilter

  const onCategoryFilter = useCallback(
    categoryId => async () => {
      setCategoryFilter(categoryId)

      if (!categoryId) {
        setLoading(false)
        setItems(initialItems)
        return
      }

      setLoading(true)

      const res = await getRequest('/articles', {
        params: {
          siteId: site.id,
          detailPageId: detailPageId,
          filter: { categories: { $in: categoryId } },
          sort: `${sortOrder === 'desc' ? '-' : ''}${sortField}`,
          limit,
        },
      })

      const result = res.data

      if (result) {
        setItems(result.items)
      }

      setLoading(false)
    },
    [initialItems, detailPageId, site, limit, sortField, sortOrder]
  )

  return (
    <div className="space-y-4">
      <ListTitle
        title={title && <Link to={articlesTeaser.listPagePath}>{title}</Link>}
        extra={
          <div className="flex flex-row items-center justify-between space-x-4 rtl:space-x-reverse">
            {showFilters ? (
              <div className="flex flex-row space-x-3 rtl:space-x-reverse">
                <Button
                  onClick={onCategoryFilter(null)}
                  label={t('all')}
                  variant={categoryFilter ? 'flat' : 'primary'}
                  size="sm"
                />
                {categories.map(categoryItem => (
                  <Button
                    key={categoryItem.id}
                    label={categoryItem.title}
                    onClick={onCategoryFilter(categoryItem.id)}
                    variant={
                      categoryItem.id === categoryFilter ? 'primary' : 'flat'
                    }
                    size="sm"
                  />
                ))}
              </div>
            ) : (
              listPagePath && (
                <Link
                  className="font-semibold uppercase"
                  to={listPagePath}
                  basic
                >
                  {t('readMore')}
                </Link>
              )
            )}
          </div>
        }
      />
      <LoadingWrap loading={loading}>
        <Articles
          items={items}
          direction={direction}
          displayMode={displayMode}
          columns={columns}
          featuredItemsCount={featuredItemsCount}
          showDate={showDate}
          showDescription={showDescription}
          showImage={showImage}
        />
      </LoadingWrap>
    </div>
  )
}
ArticleTeaser.propTypes = {
  articlesTeaser: PropTypes.shape({
    items: PropTypes.array,
    count: PropTypes.number,
    categories: PropTypes.array,
    listPagePath: PropTypes.string,
  }),
  columns: PropTypes.object,
  detailPageId: PropTypes.string,
  direction: PropTypes.object,
  displayMode: PropTypes.oneOf([
    'cards',
    'cards-with-list',
    'list',
    'posters',
    'featured',
    'featured-with-list',
    'masonry',
  ]),
  featuredItemsCount: PropTypes.number,
  limit: PropTypes.number,
  pageData: PropTypes.object,
  showCategoryFilter: PropTypes.bool,
  showDate: PropTypes.bool,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
  sortField: PropTypes.string,
  sortOrder: PropTypes.oneOf(['asc', 'desc']),
  title: PropTypes.string,
}
