import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Articles = dynamic(() => import('./shared/Articles'))

export default function ArticleList({
  articles = { items: [], count: 0 },
  columns,
  displayMode,
  showDate,
  showDescription,
  showImage,
  limit,
  pagination,
}) {
  return (
    <Articles
      items={articles?.items}
      count={articles?.count}
      limit={limit}
      displayMode={displayMode}
      showDate={showDate}
      showDescription={showDescription}
      showImage={showImage}
      columns={columns}
      pagination={pagination}
    />
  )
}
ArticleList.propTypes = {
  articles: PropTypes.shape({
    items: PropTypes.array,
    count: PropTypes.number,
  }),
  columns: PropTypes.object,
  displayMode: PropTypes.string,
  limit: PropTypes.number,
  showDate: PropTypes.bool,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
}
