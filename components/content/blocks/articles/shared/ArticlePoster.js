import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { FormatDate } from 'utils/datetime'
import { getImageUrl } from 'utils/images'

const Link = dynamic(() => import('ui/navigation/Link'))

export default function ArticlePoster({ article, showDate, showImage }) {
  const { startsAt } = article.site || {}

  const imageUrl = showImage ? getImageUrl(article.image?.file, 'w:800') : null

  return (
    <div
      className="aspect-w-3 aspect-h-4 relative flex flex-col rounded-md border border-gray-100 bg-color1-400 bg-cover bg-center shadow-md"
      style={showImage ? { backgroundImage: `url(${imageUrl})` } : undefined}
    >
      <div className="flex flex-grow flex-col justify-end rounded-md bg-gradient-to-b from-transparent via-gray-900/40 to-gray-900/90 p-6 pb-12 text-white">
        <div className="space-y-2">
          {showDate && (
            <p className="font-semibold text-color1-300 text-sm">
              <FormatDate date={startsAt || article.createdAt} format="PP" />
            </p>
          )}
          <h3 className="font-bold text-2xl">
            <Link to={article.url}>{article.title}</Link>
          </h3>
        </div>
      </div>
    </div>
  )
}
ArticlePoster.propTypes = {
  article: PropTypes.object.isRequired,
  showDate: PropTypes.bool,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
}
