import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { FormatDate } from 'utils/datetime'

const Image = dynamic(() => import('ui/data-display/Image'))
const Link = dynamic(() => import('ui/navigation/Link'))

export default function ArticleItem({
  article,
  showDate,
  showDescription,
  showImage,
}) {
  return (
    <div className="flex space-x-4 rtl:space-x-reverse">
      {showImage && (
        <div className="h-16 w-16 shrink-0">
          <Link to={article.url}>
            <Image
              alt={article.title}
              file={article.image?.file}
              className="rounded-lg"
              aspectRatio="1/1"
              sizes="192px"
            />
          </Link>
        </div>
      )}
      <div className="flex-grow space-y-0">
        <div>
          {showDate && article.site?.startsAt && (
            <p className="-mt-1 font-semibold text-gray-400 text-xs">
              <FormatDate date={article.site.startsAt} format="PP" />
            </p>
          )}
          <h3 className="font-semibold text-lg">
            <Link to={article.url}>{article.title}</Link>
          </h3>
        </div>
        {showDescription && (
          <p className="text-sm">{article.abstract || article.subtitle}</p>
        )}
      </div>
    </div>
  )
}
ArticleItem.propTypes = {
  article: PropTypes.object.isRequired,
  showDate: PropTypes.bool,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
}
