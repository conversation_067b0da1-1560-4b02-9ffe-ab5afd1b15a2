import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import useColumns from 'ui/helpers/useColumns'
import useFlex, { findDirectionAtBreakpoint } from 'ui/helpers/useFlex'
import useSpacing from 'ui/helpers/useSpacing'

const List = dynamic(() => import('ui/data-display/List'))
const Pagination = dynamic(() => import('ui/navigation/Pagination'))

const ArticleCard = dynamic(() => import('./ArticleCard'))
const ArticleItem = dynamic(() => import('./ArticleItem'))
const ArticleFeatured = dynamic(() => import('./ArticleFeatured'))
const ArticlePoster = dynamic(() => import('./ArticlePoster'))
const ArticleListItem = dynamic(() => import('./ArticleListItem'))

const featuredItemDisplayModes = {
  'cards-with-list': ArticleCard,
  'featured-with-list': ArticleFeatured,
}

const itemDisplayModes = {
  'cards': ArticleCard,
  'cards-with-list': ArticleItem,
  'featured': ArticleFeatured,
  'featured-with-list': ArticleItem,
  'list': ArticleListItem,
  'posters': ArticlePoster,
  'masonry': ArticleFeatured,
}

export default function Articles({
  items,
  count,
  columns,
  direction,
  displayMode,
  featuredItemsCount,
  showDate,
  showDescription,
  showImage,
  limit,
  pagination,
}) {
  const wrapClass = useFlex(direction || { xs: 'y' })
  const spacingClasses = useSpacing({ xs: 'lg' })
  const featuredArticlesWidthClass = useFeaturedArticlesWidth(direction)
  const columnsClass = useColumns(columns, { xs: 1, md: 2, lg: 4 })

  if (!items?.length) return null

  if (displayMode === 'list') {
    return (
      <div className="flex flex-col space-y-4">
        <List>
          {items?.map(article => (
            <ArticleListItem
              article={article}
              key={article.id}
              showDate={showDate}
              showDescription={showDescription}
              showImage={showImage}
            />
          ))}
        </List>
        {count && <Pagination total={count} pageSize={limit} />}
      </div>
    )
  } else {
    let featuredArticles = null
    let articles = items || []

    if (['cards-with-list', 'featured-with-list'].includes(displayMode)) {
      featuredArticles = items.slice(0, featuredItemsCount)
      articles = articles.slice(featuredItemsCount, items.length)
    }

    return (
      <div className={`${wrapClass} ${spacingClasses}`}>
        {featuredArticles && (
          <div
            className={`grid w-1/2 shrink-0 grid-cols-1 gap-4 ${featuredArticlesWidthClass}`}
          >
            {featuredArticles.map(article =>
              React.createElement(featuredItemDisplayModes[displayMode], {
                article,
                showDate,
                showImage,
                showDescription,
                key: article.id,
              })
            )}
          </div>
        )}
        <div className={`grid grid-cols-1 gap-4 md:gap-6 ${columnsClass}`}>
          {articles.map(article =>
            React.createElement(itemDisplayModes[displayMode] || ArticleItem, {
              article,
              showDate,
              showImage,
              showDescription,
              key: article.id,
            })
          )}
        </div>
        {pagination && count && <Pagination total={count} pageSize={limit} />}
      </div>
    )
  }
}
Articles.propTypes = {
  columns: PropTypes.object,
  count: PropTypes.number,
  direction: PropTypes.object,
  displayMode: PropTypes.oneOf([
    'cards',
    'cards-with-list',
    'list',
    'featured',
    'featured-with-list',
    'posters',
    'masonry',
  ]),
  featuredItemsCount: PropTypes.number,
  items: PropTypes.array,
  limit: PropTypes.number,
  showDate: PropTypes.bool,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
}

function useFeaturedArticlesWidth(direction) {
  if (typeof direction !== 'object') return ''

  const classes = []
  let prevDir = null

  for (const breakpoint of Object.keys(featuredItemsMap)) {
    const dirAtBreakpoint = findDirectionAtBreakpoint(
      direction,
      breakpoint,
      prevDir
    )
    prevDir = dirAtBreakpoint

    classes.push(featuredItemsMap[breakpoint][dirAtBreakpoint])
  }

  return classes.join(' ')
}

const featuredItemsMap = {
  xs: { x: 'w-1/2', y: 'w-full' },
  sm: { x: 'sm:w-1/2', y: 'sm:w-full' },
  md: { x: 'md:w-1/2', y: 'md:w-full' },
  lg: { x: 'lg:w-1/2', y: 'lg:w-full' },
  xl: { x: 'xl:w-1/2', y: 'xl:w-full' },
}
