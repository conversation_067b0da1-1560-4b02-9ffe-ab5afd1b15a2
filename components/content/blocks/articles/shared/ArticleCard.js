import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { FormatDate } from 'utils/datetime'

const Card = dynamic(() => import('ui/data-display/Card'))

export default function ArticleCard({
  article,
  showDate,
  showDescription = true,
  showImage,
}) {
  return (
    <Card
      kicker={
        showDate ? <FormatDate date={article.publishedAt} format="PP" /> : null
      }
      title={article.title}
      maxLinesTitle={2}
      description={showDescription ? article.abstract : null}
      image={showImage ? article.image?.file : null}
      maxLinesDescription={3}
      url={article.url}
      imageAspectRatio="16/9"
    />
  )
}

ArticleCard.propTypes = {
  article: PropTypes.object,
  showDate: PropTypes.bool,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
}
