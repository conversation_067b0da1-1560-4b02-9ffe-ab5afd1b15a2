import { useMemo } from 'react'

import clsx from 'clsx'
import dynamic from 'next/dynamic'
import { getBackgroundColor, getTextColor } from 'ui/helpers/getColor'
import useBorder from 'ui/helpers/useBorder'
import useBorderRadius from 'ui/helpers/useBorderRadius'
import useDivider from 'ui/helpers/useDivider'
import useFlex from 'ui/helpers/useFlex'
import useTextSize from 'ui/helpers/useTextSize'
import usePadding from 'ui/helpers/usePadding'
import useSpacing from 'ui/helpers/useSpacing'
import useTextAlign from 'ui/helpers/useTextAlign'
import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'
import useWidth from 'ui/helpers/useWidth'
import { useFontFamily } from 'ui/helpers/useFontFamily'
import useFontWeight from 'ui/helpers/useFontWeight'

const DropdownMenu = dynamic(() => import('ui/navigation/DropdownMenu'))
const UIMenu = dynamic(() => import('ui/navigation/Menu'))

/**
 * Menu component that can render as a standard menu or a dropdown menu
 *
 * @param {object} props - The component props.
 * @param {object} [props.align] - Alignment of items within the menu
 * @param {object} [props.border] - Border properties for the menu
 * @param {object} [props.borderRadius] - Border radius for the menu
 * @param {string} [props.className] - Additional CSS classes for the menu container
 * @param {object} [props.direction] - Direction of the menu items
 * @param {object} [props.dropdownAlignment] - Alignment of the dropdown panel
 * @param {string} [props.fontFamily] - Font family for menu items
 * @param {string} [props.fontWeight] - Font weight for menu items
 * @param {string} [props.icon='bars'] - Icon name for the dropdown toggle
 * @param {object} [props.iconColor] - Color of the dropdown toggle icon
 * @param {object} [props.iconSize='md'] - Size of the dropdown toggle icon
 * @param {string} [props.id] - ID attribute for the menu
 * @param {object} [props.itemActiveBgColor] - Background color for active menu items
 * @param {object} [props.itemActiveColor] - Text color for active menu items
 * @param {object} [props.itemBgColor] - Background color for menu items
 * @param {object} [props.itemColor] - Text color for menu items
 * @param {object} [props.itemDivider] - Divider properties between menu items
 * @param {object} [props.itemJustify] - Justification of content within menu items
 * @param {object} [props.itemPadding] - Padding for menu items
 * @param {object} [props.itemTextAlign] - Text alignment for menu items
 * @param {object} [props.justify] - Justification of menu items within the container
 * @param {Array<object>} props.menu - Array of menu item objects
 * @param {object} [props.menuBgColor] - Background color for the menu
 * @param {object} [props.offset] - Offset for the dropdown panel
 * @param {string} [props.openedIcon='xmark'] - Icon name for the opened dropdown toggle
 * @param {object} [props.openedIconColor] - Color of the opened dropdown toggle icon
 * @param {object} [props.padding] - Padding for the menu container
 * @param {object} [props.presentation] - Presentation mode ('dropdown' or standard)
 * @param {boolean} [props.showSubmenuChevron] - Whether to show chevrons for submenus
 * @param {object} [props.spacing] - Spacing between menu items
 * @param {object} [props.submenuBgColor] - Background color for submenus
 * @param {object} [props.submenuBorderRadius] - Border radius for submenus
 * @param {object} [props.submenuItemActiveBackgroundColor] - Background color for active submenu items
 * @param {object} [props.submenuItemActiveColor] - Text color for active submenu items
 * @param {object} [props.submenuItemActiveFontWeight] - Font weight for active submenu items
 * @param {object} [props.submenuItemBorderRadius] - Border radius for submenu items
 * @param {object} [props.submenuItemColor] - Text color for submenu items
 * @param {object} [props.submenuItemDivider] - Divider properties between submenu items
 * @param {object} [props.submenuItemFontWeight] - Font weight for submenu items
 * @param {object} [props.submenuItemHoverColor] - Text color for submenu items on hover
 * @param {object} [props.submenuItemPadding] - Padding for submenu items
 * @param {object} [props.submenuItemSpacing] - Spacing between submenu items
 * @param {object} [props.submenuItemTextAlign] - Text alignment for submenu items
 * @param {object} [props.submenuItemTextSize] - Text size for submenu items
 * @param {object} [props.submenuPadding] - Padding for submenus
 * @param {object} [props.textCase] - Text case for menu items (e.g., 'uppercase')
 * @param {object} [props.textSize] - Text size for menu items
 * @param {object} [props.width] - Width of the menu
 * @param {object} [props.pageData] - Page data, potentially containing site information
 * @returns {React.ReactElement} The rendered menu component or null if no menu items are provided
 */
export default function Menu({
  align,
  border,
  borderRadius,
  className,
  direction,
  dropdownAlignment,
  fontFamily,
  fontWeight,
  icon = 'bars',
  iconColor,
  iconSize = 'md',
  id,
  itemActiveBgColor,
  itemActiveColor,
  itemBgColor,
  itemColor,
  itemDivider,
  itemJustify,
  itemPadding,
  itemTextAlign,
  justify,
  menu,
  menuBgColor,
  offset,
  openedIcon = 'xmark',
  openedIconColor,
  padding,
  presentation,
  showSubmenuChevron,
  spacing,
  submenuBgColor,
  submenuBorderRadius,
  submenuItemActiveBackgroundColor,
  submenuItemActiveColor,
  submenuItemActiveFontWeight,
  submenuItemBorderRadius,
  submenuItemColor,
  submenuItemDivider,
  submenuItemFontWeight,
  submenuItemHoverColor,
  submenuItemPadding,
  submenuItemSpacing,
  submenuItemTextAlign,
  submenuItemTextSize,
  submenuPadding,
  textCase,
  textSize,
  width,
  pageData,
}) {
  const { site } = pageData || {}
  const bgColor = useValueAtBreakpoint(menuBgColor)
  const iconColorValue = useValueAtBreakpoint(iconColor)
  const openedIconColorValue = useValueAtBreakpoint(openedIconColor)
  const itemActiveBgColorValue = useValueAtBreakpoint(itemActiveBgColor)
  const itemActiveColorValue = useValueAtBreakpoint(itemActiveColor)
  const itemBgColorValue = useValueAtBreakpoint(itemBgColor)
  const itemColorValue = useValueAtBreakpoint(itemColor)
  const presentationValue = useValueAtBreakpoint(presentation)
  const offsetValue = useValueAtBreakpoint(offset)
  const directionValue = useValueAtBreakpoint(direction)

  const borderClasses = useBorder(border)
  const borderRadiusClasses = useBorderRadius(borderRadius)
  const dividerClasses = useDivider(itemDivider, directionValue)
  const flexClasses = useFlex(direction, align, justify)
  const spacingClasses = useSpacing(spacing)
  const paddingClasses = usePadding(padding)
  const bgColorClass = getBackgroundColor(bgColor)
  const iconColorClass =
    getTextColor(iconColorValue) ?? getTextColor(itemColorValue)
  const openedIconColorClass =
    getTextColor(openedIconColorValue) ??
    iconColorClass ??
    getTextColor(itemActiveColorValue) ??
    getTextColor(itemColorValue)
  const textSizeClass = useTextSize(textSize)
  const textCaseClass = useValueAtBreakpoint(textCase)
  const alignClass = useTextAlign(itemTextAlign)
  const widthClass = useWidth(width, '')
  const fontFamilyClass = useFontFamily(fontFamily)
  const fontWeightClass = useFontWeight(fontWeight)

  const classNames = useMemo(
    () =>
      clsx(
        {
          [bgColorClass]: presentationValue !== 'dropdown',
          [borderClasses]: borderClasses && presentationValue !== 'dropdown',
          [borderRadiusClasses]:
            borderRadiusClasses && presentationValue !== 'dropdown',
          [dividerClasses]: dividerClasses && presentationValue !== 'dropdown',
        },
        className,
        flexClasses,
        paddingClasses,
        spacingClasses,
        widthClass
      ),
    [
      bgColorClass,
      borderClasses,
      borderRadiusClasses,
      className,
      dividerClasses,
      flexClasses,
      paddingClasses,
      presentationValue,
      spacingClasses,
      widthClass,
    ]
  )

  const itemClassName = useMemo(
    () =>
      clsx(
        {
          [getBackgroundColor(itemBgColorValue)]: itemBgColorValue,
          [`hover:${getBackgroundColor(itemBgColorValue)}`]:
            itemActiveBgColorValue,
          [`hover:${getTextColor(itemActiveColorValue)}`]: itemActiveColorValue,
        },
        getTextColor(itemColorValue),
        alignClass,
        textSizeClass,
        textCaseClass,
        fontFamilyClass,
        fontWeightClass
      ),
    [
      itemActiveBgColorValue,
      itemActiveColorValue,
      itemBgColorValue,
      itemColorValue,
      alignClass,
      textSizeClass,
      textCaseClass,
      fontFamilyClass,
      fontWeightClass,
    ]
  )

  const activeItemClassName = useMemo(
    () =>
      clsx(
        getBackgroundColor(itemActiveBgColorValue),
        getTextColor(itemActiveColorValue),
        alignClass,
        textSizeClass,
        textCaseClass,
        fontFamilyClass,
        fontWeightClass
      ),
    [
      itemActiveBgColorValue,
      itemActiveColorValue,
      alignClass,
      textSizeClass,
      textCaseClass,
      fontFamilyClass,
      fontWeightClass,
    ]
  )

  const commonProps = {
    activeItemClassName,
    align,
    bgColor,
    border,
    className: classNames,
    itemColor: itemColorValue,
    itemPadding,
    direction,
    fontFamily,
    id,
    itemClassName,
    items: menu,
    justify,
    padding,
    spacing,
    site,
    showSubmenuChevron,
    submenuItemActiveColor,
    submenuItemActiveFontWeight,
    submenuItemColor,
    submenuItemDivider,
    submenuItemFontWeight,
    submenuItemHoverColor,
    submenuItemPadding,
    submenuItemSpacing,
    submenuItemTextAlign,
    submenuItemTextSize,
    submenuPadding,
  }

  if (!menu) return null

  if (presentationValue === 'dropdown') {
    return (
      <DropdownMenu
        {...commonProps}
        alignment={dropdownAlignment}
        bgColor={bgColor}
        borderRadius={borderRadius}
        direction={directionValue}
        divider={itemDivider}
        icon={icon}
        iconClassName={iconColorClass}
        iconSize={iconSize}
        justify={itemJustify}
        offset={offsetValue}
        openedIcon={openedIcon}
        openedIconClassName={openedIconColorClass}
        panelClass={widthClass}
      />
    )
  }

  return (
    <UIMenu
      {...commonProps}
      submenuBgColor={submenuBgColor}
      submenuBorderRadius={submenuBorderRadius}
      submenuItemActiveBackgroundColor={submenuItemActiveBackgroundColor}
      submenuItemBorderRadius={submenuItemBorderRadius}
    />
  )
}
