import PropTypes from 'prop-types'
import BlockResourcesProvider from '../BlockResourceSourceProvider'
import React from 'react'

export function EpisodeRepeater({ children, episodes, show }) {
  if (episodes?.count === 0) return null

  return episodes?.items?.map(episode => (
    <BlockResourcesProvider key={episode._id} value={{ episode, show }}>
      {children}
    </BlockResourcesProvider>
  ))
}

EpisodeRepeater.propTypes = {
  children: PropTypes.node,
  episodes: PropTypes.shape({
    count: PropTypes.number,
    items: PropTypes.arrayOf(PropTypes.object),
  }),
}

export default EpisodeRepeater
