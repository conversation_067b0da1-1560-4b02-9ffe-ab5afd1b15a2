import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const Player = dynamic(() => import('ui/data-display/Player'))

export default function EpisodePlayer({ episodePlayer, pageData }) {
  const { t } = useTranslation('media-library')

  const { error, id, provider, playlist, playlistIndex, poster } =
    episodePlayer ?? {}

  if (!episodePlayer) return null

  if (error) {
    return (
      <div className="w-full py-8 text-center font-extralight text-gray-300 text-2xl">
        {t('videoMissing')}
      </div>
    )
  }

  return (
    <Player
      id={id}
      key={id}
      provider={provider}
      playlist={playlist}
      playlistIndex={playlistIndex}
      sources={[{ src: id }]}
      poster={poster}
      pageData={pageData}
    />
  )
}
EpisodePlayer.propTypes = {
  episodePlayer: PropTypes.object,
}
