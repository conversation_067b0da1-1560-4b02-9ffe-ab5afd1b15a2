import PropTypes from 'prop-types'
import React, { useRef } from 'react'

import BlockResourcesProvider from '../BlockResourceSourceProvider'

import VariableCarousel, {
  VariableCarouselItem,
} from 'ui/data-display/VariableCarousel'

export function CarouselEpisodeRepeater({ children, episodes, show, color }) {
  const firstItemRef = useRef() // This is to center the navButtons on the image (first item in the carousel)

  return (
    <VariableCarousel color={color} firstItemRef={firstItemRef}>
      {episodes?.items?.map((episode, index) => (
        <VariableCarouselItem
          key={episode._id}
          index={index}
          length={episodes?.items?.length}
          firstItemRef={firstItemRef}
        >
          <BlockResourcesProvider value={{ episode, show }}>
            {children}
          </BlockResourcesProvider>
        </VariableCarouselItem>
      ))}
    </VariableCarousel>
  )
}
CarouselEpisodeRepeater.propTypes = {
  children: PropTypes.node,
  episodes: PropTypes.shape({
    count: PropTypes.number,
    items: PropTypes.arrayOf(PropTypes.object),
  }),
}

export default CarouselEpisodeRepeater
