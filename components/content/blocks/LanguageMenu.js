import React, { useEffect, useRef, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
// import { i18n } from 'next-i18next'
import { useAvailableLanguages } from 'hooks/useAvailableLanguages'

const Dropdown = dynamic(() => import('ui/buttons/Dropdown'))
const DropdownItem = dynamic(() =>
  import('ui/buttons/Dropdown').then(mod => mod.DropdownItem)
)
const DropdownSearch = dynamic(() =>
  import('ui/buttons/Dropdown').then(mod => mod.DropdownSearch)
)

export default function LanguageMenu({
  alignment,
  borderRadius,
  className = '',
  id,
  label,
  searchPlaceholder,
  noResultsLabel = '',
  pageData,
  size = 'sm',
  variant = 'flat',
}) {
  const { availableLanguages, language, site } = pageData

  const { languages, onSearch } = useLanguageItems({
    availableLanguages,
    language,
    siteLanguage: site.language,
  })

  return (
    <>
      <Dropdown
        className={`space-y-2 ${className}`}
        itemsClass="min-w-[200px]"
        name="language-menu"
        id={id}
        label={label}
        labelClass="hidden md:block"
        iconClass="text-xl"
        secondaryIconClass="hidden md:block"
        icon={<LanguageIcon />}
        alignment={alignment}
        borderRadius={borderRadius}
        size={size}
        variant={variant || 'flat'}
        color="none"
      >
        <div className="sticky top-0 z-30 bg-white/50 backdrop-blur-sm">
          <DropdownSearch placeholder={searchPlaceholder} onSearch={onSearch} />
        </div>

        {languages.length === 0 && (
          <DropdownItem label={noResultsLabel} disabled />
        )}

        {languages.map(({ label, href, help, selected, value }) => {
          return (
            <DropdownItem
              className="h-14"
              label={label}
              help={help}
              href={href}
              selected={selected}
              key={`language-switch-${value}`}
              // disableLangPrefix // Current language prefix won't be added to the href
              CustomLink={LanguageMenuLink}
            />
          )
        })}
      </Dropdown>

      {/* Render hidden language links for crawlers */}
      {languages.map(({ label, href, value }) => (
        <a className="sr-only" href={href} key={`language-link-${value}`}>
          {label}
        </a>
      ))}
    </>
  )
}
LanguageMenu.propTypes = {
  borderRadius: PropTypes.object,
  className: PropTypes.string,
  id: PropTypes.string,
  label: PropTypes.string,
  searchPlaceholder: PropTypes.string,
  pageData: PropTypes.object,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
}

function useLanguageItems({ availableLanguages, language, siteLanguage }) {
  const prevPathRef = useRef()
  const router = useRouter()
  const [search, setSearch] = useState('')

  const languages = useAvailableLanguages({ availableLanguages, search })

  // Nextjs uses SSR so window.location.pathname is not available, use router.asPath instead
  const currentPath = router.asPath

  // We use a useRef to keep track of the previous path and reset the search when the path changes
  useEffect(() => {
    if (prevPathRef.current !== currentPath) {
      setSearch('')
      prevPathRef.current = currentPath
    }
  }, [currentPath])

  // Pathname is router.asPath without the locale prefix
  const pathname = currentPath.replace(`/${language}`, '') || ''

  const languageItems = languages.map(
    ({ name, nativeName, locale, current }) => {
      return {
        label: nativeName || name?.[`${language}`] || name?.en || name,
        help:
          locale !== language
            ? name?.[language] || name?.en || name
            : undefined,
        href: `${locale === siteLanguage ? (pathname && pathname[0] !== '#' ? '' : '/') : `/${locale}`}${
          pathname ? pathname : ''
        }`,
        selected: current || locale === language,
        value: locale,
      }
    }
  )

  const onSearch = React.useCallback(value => {
    setSearch(value)
  }, [])

  return {
    languages: languageItems,
    onSearch,
  }
}

// TODO: Replace this with design icons assets when available
function LanguageIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      width="16"
      height="16"
    >
      <path d="M2 16c.1 0 8-5 9-7 .6-1.3 1-5 1-5h3H1h7V1M4 8l8 8M15 19h6l2 4-5-12-5 12z" />
    </svg>
  )
}

/**
 * Simple custom link for the language menu. This link will only add the new language prefix to the href when clicked. It is used in the DropdownItem component, from wich it recieves props and styles. It will prompt a new page load when clicked, and will prevent the slug being added as query params.
 * @param {string} className - The class name for the link.
 * @param {node} children - The children of the link.
 * @param {string} to - The href of the link.
 * @param {function} onClick - The function to be called when the link is clicked.
 */
export function LanguageMenuLink({ className, children, to, onClick }) {
  return (
    <a className={className} href={to} onClick={onClick}>
      {children}
    </a>
  )
}
LanguageMenuLink.propTypes = {
  className: PropTypes.string,
  children: PropTypes.node,
  to: PropTypes.string,
  onClick: PropTypes.func,
}
