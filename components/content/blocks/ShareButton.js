import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import {
  EmailShareButton,
  EmailIcon,
  FacebookShareButton,
  FacebookIcon,
  LineShareButton,
  LineIcon,
  PinterestShareButton,
  PinterestIcon,
  TelegramShareButton,
  TelegramIcon,
  TwitterShareButton,
  TwitterIcon,
  WhatsappShareButton,
  WhatsappIcon,
} from 'react-share'

const Dropdown = dynamic(() => import('ui/buttons/Dropdown'))
const Icon = dynamic(() => import('ui/icons/Icon'))

const socialButtons = {
  email: { Button: EmailShareButton, Icon: EmailIcon, titleProp: 'subject' },
  Facebook: {
    Button: FacebookShareButton,
    Icon: FacebookIcon,
    titleProp: 'quote',
  },
  Line: { Button: LineShareButton, Icon: LineIcon },
  Pinterest: { Button: PinterestShareButton, Icon: PinterestIcon },
  Telegram: { Button: TelegramShareButton, Icon: TelegramIcon },
  Twitter: { Button: TwitterShareButton, Icon: TwitterIcon },
  Whatsapp: { Button: WhatsappShareButton, Icon: WhatsappIcon },
}

export default function ShareButton({
  className = '',
  id,
  title,
  pageData,
  iconSize = 32,
  ...props
}) {
  const activeButtons = Object.entries(props) // look in the props object
    .filter(([key, value]) => socialButtons[key] && value === true) // only supported buttons that are enabled
    .map(([key]) => key) // and get only the key

  return (
    <Dropdown
      button={
        <div className="flex h-12 w-12 cursor-pointer items-center justify-center gap-2 rounded-full border border-gray-300 bg-transparent p-3 font-semibold uppercase text-color1-800 hover:bg-color1-50">
          <Icon name="share" className="text-color1-500 text-xl" />
          {title && <span className="text-color1-600">{title}</span>}
        </div>
      }
      itemsClass="p-2 gap-4"
      name="share-publication"
      alignment={{ xs: 'top-end' }}
      variant="flat"
      color="none"
      className={className}
      id={id}
    >
      {activeButtons.map(button => {
        const { Button, Icon, titleProp = 'title' } = socialButtons[button]
        const buttonProps = {}
        buttonProps[titleProp] = pageData.title

        return (
          <Button key={button} url={pageData.absoluteUrl} {...buttonProps}>
            <Icon size={iconSize} round />
          </Button>
        )
      })}
    </Dropdown>
  )
}
ShareButton.propTypes = {
  className: PropTypes.string,
  iconSize: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  id: PropTypes.string,
  pageData: PropTypes.object,
  title: PropTypes.string,
}
