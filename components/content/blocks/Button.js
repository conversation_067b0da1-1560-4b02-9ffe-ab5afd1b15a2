import dynamic from 'next/dynamic'
import { usePathname } from 'next/navigation'
import { useRouter } from 'next/router'

import useFieldResource from 'ui/helpers/useFieldResource'

const UIButton = dynamic(() => import('ui/buttons/Button'))

/**
 * The Button Block component
 * @param {Object} props Component props
 * @param {Object} props.url The URL to navigate to
 * @param {Object} props.urlSource The source of the URL
 * @returns {React.ReactElement} The button component
 */
export default function Button({
  url,
  urlSource,
  linkType,
  anchor,
  query,
  ...rest
}) {
  // Get the URL value from the source
  const urlValue = useFieldResource(urlSource, url)
  const router = useRouter()
  const pathname = usePathname()

  const handleClick = () => {
    // If the anchor doesn't have a hash, add one
    if (anchor && !anchor.startsWith('#')) {
      anchor = `#${anchor}`
    }

    // If the query doesn't have a question mark, add one
    if (query && !query.startsWith('?')) {
      query = `?${query}`
    }

    let url = `${pathname}${query}`
    if (anchor) url += anchor
    // Scroll is set to false if an anchor is not present
    router.push(url, undefined, { scroll: !!anchor })
  }

  return (
    <UIButton
      {...rest}
      url={linkType !== 'custom' ? urlValue : undefined}
      onClick={linkType === 'custom' ? handleClick : undefined}
    />
  )
}
