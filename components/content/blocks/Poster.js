import React from 'react'

import dynamic from 'next/dynamic'

const UIPoster = dynamic(() => import('ui/data-display/Poster'))

export default function Poster({
  image,
  alt,
  title,
  description,
  primaryButton,
  secondaryButton,
  borderRadius,
  enableAnimation,
  animationOrigin,
}) {
  return (
    <UIPoster
      image={image}
      alt={alt}
      title={title}
      description={description}
      primaryButton={primaryButton}
      secondaryButton={secondaryButton}
      enableAnimation={enableAnimation}
      animationOrigin={animationOrigin}
      borderRadius={borderRadius}
    />
  )
}
