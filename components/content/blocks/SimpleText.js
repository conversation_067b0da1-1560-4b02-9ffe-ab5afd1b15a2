import PropTypes from 'prop-types'
import { useMemo } from 'react'

import dynamic from 'next/dynamic'

import clsx from 'clsx'
import getOpacity from 'ui/helpers/getOpacity'
import useFieldResource from 'ui/helpers/useFieldResource'
import useWidth from 'ui/helpers/useWidth'
import Link from 'ui/navigation/Link'

const Blockquote = dynamic(() => import('ui/typography/Blockquote'))
const Heading = dynamic(() => import('ui/typography/Heading'))
const Paragraph = dynamic(() => import('ui/typography/Paragraph'))

export default function SimpleText({
  align,
  as,
  className = '',
  color,
  fontFamily,
  fontWeight,
  id,
  noWrap,
  opacity,
  source,
  text,
  textCase,
  textSize,
  textSizeCustom,
  url,
  urlSource,
  width,
}) {
  const widthClass = useWidth(width)
  const opacityClass = getOpacity(opacity)

  const commonProps = useMemo(
    () => ({
      align,
      className: clsx(widthClass, opacityClass, className),
      color,
      fontFamily,
      fontWeight,
      id,
      noWrap,
      textCase,
      textSize,
      textSizeCustom,
    }),
    [
      align,
      className,
      color,
      fontFamily,
      fontWeight,
      id,
      noWrap,
      opacityClass,
      textCase,
      textSize,
      textSizeCustom,
      widthClass,
    ]
  )

  const displayText = useFieldResource(source, text)
  const urlValue = useFieldResource(urlSource, url)

  switch (as) {
    case 'h1':
    case 'h2':
    case 'h3':
    case 'h4':
      return (
        <Heading as={as} {...commonProps}>
          {urlValue ? <Link to={urlValue}>{displayText}</Link> : displayText}
        </Heading>
      )

    case 'blockquote':
      return (
        <Blockquote {...commonProps}>
          {urlValue ? <Link to={urlValue}>{displayText}</Link> : displayText}
        </Blockquote>
      )

    default:
      return (
        <Paragraph {...commonProps}>
          {urlValue ? <Link to={urlValue}>{displayText}</Link> : displayText}
        </Paragraph>
      )
  }
}
SimpleText.propTypes = {
  align: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.oneOf(['left', 'center', 'right', 'justify']),
  ]),
  as: PropTypes.oneOf(['h1', 'h2', 'h3', 'p', 'blockquote']),
  className: PropTypes.string,
  color: PropTypes.string,
  fontFamily: PropTypes.string,
  fontWeight: PropTypes.string,
  id: PropTypes.string,
  noWrap: PropTypes.bool,
  opacity: PropTypes.string,
  text: PropTypes.string,
  textCase: PropTypes.string,
  textSize: PropTypes.object,
  url: PropTypes.string,
  urlSource: PropTypes.object,
  width: PropTypes.object,
}
