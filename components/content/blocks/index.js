import dynamic from 'next/dynamic'

const Root = dynamic(() => import('./Root'))
const Section = dynamic(() => import('./Section'))
const Container = dynamic(() => import('./Container'))
const ContentPlaceholder = dynamic(() => import('./ContentPlaceholder'))
const Grid = dynamic(() => import('./Grid'))
const Slider = dynamic(() => import('./Slider'))
const Accordion = dynamic(() => import('./Accordion'))
const AccordionItem = dynamic(() =>
  import('./Accordion').then(m => m.AccordionItem)
)
const Avatar = dynamic(() => import('ui/data-display/Avatar'))
const Header = dynamic(() => import('./Header'))
const Hero = dynamic(() => import('./Hero'))
const HeroCarousel = dynamic(() => import('./HeroCarousel'))
const SimpleText = dynamic(() => import('./SimpleText'))
const LabelIcon = dynamic(() => import('./LabelIcon'))
const HtmlEmbed = dynamic(() => import('./HtmlEmbed'))
const HubSpotForm = dynamic(() => import('./HubSpotForm'))
const DisqusComments = dynamic(() => import('./DisqusComments'))
const ShareButton = dynamic(() => import('./ShareButton'))
const SocialMediaLinks = dynamic(() => import('./SocialMediaLinks'))
const Map = dynamic(() => import('./Map'))
const Menu = dynamic(() => import('./Menu'))
const MainMenu = dynamic(() => import('./MainMenu'))
const LanguageMenu = dynamic(() => import('./LanguageMenu'))
const Button = dynamic(() => import('./Button'))
const Box = dynamic(() => import('./Box'))
const Modal = dynamic(() => import('./Modal'))
const Card = dynamic(() => import('ui/data-display/Card'))
const Carousel = dynamic(() => import('ui/data-display/ImageCarousel'))
const QuoteCarousel = dynamic(() => import('ui/data-display/QuoteCarousel'))
const Image = dynamic(() => import('./Image'))
const ImageGallery = dynamic(() => import('./ImageGallery'))
const Poster = dynamic(() => import('./Poster'))
const Player = dynamic(() => import('./Player'))
const Breadcrumbs = dynamic(() => import('./Breadcrumbs'))
const LinkList = dynamic(() => import('ui/navigation/LinkList'))
const RichText = dynamic(() => import('./RichText'))
const NewsletterSubscription = dynamic(() => import('./NewsletterSubscription'))
const CookieBlockedContent = dynamic(
  () => import('ui/feedback/CookieBlockedContent')
)

const Form = dynamic(() => import('./Form/Form'))
const FormCaptcha = dynamic(() => import('./Form/FormCaptcha'))
const FormCheckbox = dynamic(() => import('./Form/FormCheckbox'))
const FormInput = dynamic(() => import('./Form/FormInput'))
const FormSelect = dynamic(() => import('./Form/FormSelect'))
const FormTextArea = dynamic(() => import('./Form/FormTextArea'))

const ArticleList = dynamic(() => import('./articles/List'))
const ArticleRepeater = dynamic(() => import('./articles/Repeater'))
const ArticleDetail = dynamic(() => import('./articles/Detail'))
const ArticleTeaser = dynamic(() => import('./articles/Teaser'))
const LatestArticles = dynamic(() => import('./articles/LatestArticles'))

const PublicationsList = dynamic(() => import('./publications/List'))
const PublicationsSearch = dynamic(() => import('./publications/Search'))
const PublicationDetail = dynamic(() => import('./publications/Detail'))
const PublicationDownloads = dynamic(() => import('./publications/Downloads'))
const PublicationsReligionsMenu = dynamic(
  () => import('./publications/ReligionsMenu')
)
const PublicationStatistics = dynamic(() => import('./publications/Statistics'))
const PublishingHouses = dynamic(
  () => import('./publications/PublishingHouses')
)
const PublishingHousesMap = dynamic(() => import('./publications/Map'))

const EpisodeRepeater = dynamic(() => import('./media-library/EpisodeRepeater'))
const EpisodeDetail = dynamic(() => import('./media-library/EpisodeDetail'))
const CarouselEpisodeRepeater = dynamic(
  () => import('./media-library/CarouselEpisodeRepeater')
)
const EpisodePlayer = dynamic(() => import('./media-library/EpisodePlayer'))
const EpisodeDownloads = dynamic(
  () => import('./media-library/EpisodeDownloads')
)
const OfflineVideos = dynamic(() => import('./media-library/OfflineVideos'))

const PWAInstallPrompt = dynamic(() => import('./pwa/PWAInstallPrompt'))
const PWAOfflineBanner = dynamic(() => import('./pwa/PWAOfflineBanner'))

function NoWrap({ children }) {
  return children
}

const blocks = {
  Root,
  Section,
  Container,
  ContentPlaceholder,
  Box,
  Modal,
  Grid,
  Slider,
  Card,
  Header,
  Hero,
  HeroCarousel,
  Avatar,
  SimpleText,
  LabelIcon,
  Button,
  Breadcrumbs,
  LinkList,
  Map,
  Menu,
  MainMenu,
  LanguageMenu,
  HubSpotForm,
  DisqusComments,
  Image,
  ImageGallery,
  Poster,
  Player,
  Accordion,
  AccordionItem,
  AccordionItemContent: NoWrap,
  CookieBlockedContent,
  Form,
  FormCaptcha,
  FormCheckbox,
  FormInput,
  FormSelect,
  FormTextArea,
  Carousel,
  QuoteCarousel,
  RichText,
  NewsletterSubscription,
  HtmlEmbed,
  ShareButton,
  SocialMediaLinks,
  ArticleList,
  ArticleRepeater,
  ArticleDetail,
  ArticleTeaser,
  LatestArticles,
  PublicationsList,
  PublicationsSearch,
  PublicationDetail,
  PublicationDownloads,
  PublishingHouses,
  PublishingHousesMap,
  PublicationsReligionsMenu,
  PublicationStatistics,
  EpisodeRepeater,
  EpisodeDetail,
  CarouselEpisodeRepeater,
  EpisodePlayer,
  EpisodeDownloads,
  OfflineVideos,
  PWAOfflineBanner,
  PWAInstallPrompt,
}

export default blocks
