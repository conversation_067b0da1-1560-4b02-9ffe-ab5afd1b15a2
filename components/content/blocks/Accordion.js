import React, { useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import useSpacing from 'ui/helpers/useSpacing'
import useWidth from 'ui/helpers/useWidth'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function Accordion({
  children,
  className = '',
  id,
  spacing,
  width,
}) {
  const widthClasses = useWidth(width)
  const spacingClass = useSpacing(spacing, 'sm')

  return (
    <div
      className={`relative flex flex-col ${className} ${spacingClass} ${widthClasses}`}
      id={id}
    >
      {children}
    </div>
  )
}
Accordion.propTypes = {
  children: PropTypes.array,
  className: PropTypes.string,
  id: PropTypes.string,
  spacing: PropTypes.string,
  width: PropTypes.object,
}

export function AccordionItem({ title, content, defaultOpen }) {
  const [open, setOpen] = useState(defaultOpen)

  const onToggle = useCallback(e => {
    setOpen(e.target.open)
  }, [])

  return (
    <details
      className="rounded-lg border border-gray-300 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
      onToggle={onToggle}
      open={open}
    >
      <summary
        className={`flex cursor-pointer flex-row items-center justify-between rounded-t-lg border-b px-6 py-4 ${
          open ? 'border-gray-300 dark:border-gray-700' : 'border-b-transparent'
        }`}
      >
        <div className="font-bold text-gray-700 text-xl dark:text-gray-300">
          {title}
        </div>
        <div className="select-none text-gray-500 text-base">
          <Icon name={open ? 'chevron-up' : 'chevron-down'} />
        </div>
      </summary>
      <div className="space-y-2 p-6">{content}</div>
    </details>
  )
}
AccordionItem.propTypes = {
  title: PropTypes.string,
  content: PropTypes.node,
  defaultOpen: PropTypes.bool,
}
