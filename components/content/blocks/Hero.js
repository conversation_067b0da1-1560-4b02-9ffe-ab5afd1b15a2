import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { usePageContext } from 'components/PageProvider'
import useFieldResource from 'ui/helpers/useFieldResource'

const UIHero = dynamic(() => import('ui/data-display/Hero'))

const positionMap = {
  'top left': 'left-top',
  'top': 'top',
  'top right': 'right-top',
  'center left': 'left',
  'center': 'center',
  'center right': 'right',
  'bottom left': 'left-bottom',
  'bottom': 'bottom',
  'bottom right': 'right-bottom',
}

export function Hero({
  callToAction,
  callToActionVariant,
  description,
  height,
  image,
  imageAlign,
  imageSource,
  kicker,
  kickerSource,
  padding,
  spacing,
  textPosition,
  textWidth,
  title,
  titleSource,
  url,
  urlSource,
  usePageTitle,
}) {
  const pageContext = usePageContext()

  const titleValue = useFieldResource(
    titleSource,
    usePageTitle ? pageContext?.title : title?.value
  )
  const kickerValue = useFieldResource(kickerSource, kicker?.value)
  const imageFile = useFieldResource(imageSource, image)
  const urlValue = useFieldResource(urlSource, url)

  if (!imageFile && !titleValue) return null

  return (
    <UIHero
      callToAction={callToAction}
      callToActionVariant={callToActionVariant}
      description={description}
      height={height}
      image={imageFile}
      imageAlign={positionMap[imageAlign]}
      kicker={{ ...kicker, value: kickerValue }}
      padding={padding}
      spacing={spacing}
      textPosition={textPosition}
      textWidth={textWidth}
      title={{ ...title, value: titleValue }}
      url={urlValue}
    />
  )
}

Hero.propTypes = {
  callToAction: PropTypes.string,
  description: PropTypes.string,
  height: PropTypes.object,
  image: PropTypes.object,
  imageAlign: PropTypes.string,
  imageSource: PropTypes.object,
  kicker: PropTypes.object,
  kickerSource: PropTypes.object,
  padding: PropTypes.object,
  spacing: PropTypes.object,
  textPosition: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  textWidth: PropTypes.object,
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  titleSource: PropTypes.object,
  url: PropTypes.string,
  urlSource: PropTypes.object,
  usePageTitle: PropTypes.bool,
}

export default Hero
