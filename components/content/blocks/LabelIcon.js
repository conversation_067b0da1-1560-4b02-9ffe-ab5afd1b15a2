import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import useWidth from 'ui/helpers/useWidth'
import useTextSize from 'ui/helpers/useTextSize'
import useFontWeight from 'ui/helpers/useFontWeight'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function LabelIcon({
  id,
  className,
  label,
  icon,
  iconPosition,
  fontWeight,
  textSize,
  width,
}) {
  const fontWeightClass = useFontWeight(fontWeight)
  const positionClass = useIconPositionClass(iconPosition)
  const textSizeClass = useTextSize(textSize)
  const widthClass = useWidth(width)

  return (
    <div
      className={`flex items-center justify-center ${textSizeClass} ${positionClass} ${widthClass} ${className}`}
      id={id}
    >
      <div>
        <Icon name={icon} />
      </div>
      <div className={fontWeightClass}>{label}</div>
    </div>
  )
}
LabelIcon.propTypes = {
  className: PropTypes.string,
  label: PropTypes.string,
  icon: PropTypes.string,
  iconPosition: PropTypes.object,
  id: PropTypes.string,
  fontWeight: PropTypes.string,
  textSize: PropTypes.object,
  width: PropTypes.object,
}

function useIconPositionClass(position, defaultPosition = 'left') {
  if (!position) return position.xs[defaultPosition] || position.xs.full

  const classes = []

  for (const [breakpoint, value] of Object.entries(position)) {
    if (breakpointKeys.includes(breakpoint)) {
      const positionAtBreakpoint = positions[breakpoint]
      const vertical = ['top', 'bottom'].includes(value)
      const reverse = ['bottom', 'right'].includes(value)

      if (positionAtBreakpoint) {
        const classAtBreakpoint =
          positionAtBreakpoint?.[vertical ? 'vertical' : 'horizontal']
        const reverseClassAtBreakpoint = reverse
          ? positionAtBreakpoint[value]
          : ''

        classes.push(`${classAtBreakpoint} ${reverseClassAtBreakpoint}`)
      }
    }
  }

  return classes.join(' ')
}

const positions = {
  xs: {
    horizontal: 'flex-row space-x-2 rtl:space-x-reverse',
    vertical: 'flex-col space-y-2',
    right: 'flex-row-reverse space-x-reverse',
    bottom: 'flex-col-reverse space-y-reverse',
  },
  sm: {
    horizontal: 'sm:flex-row sm:space-x-2 rtl:sm:space-x-reverse sm:space-y-0',
    vertical: 'sm:flex-col sm:space-y-2 sm:space-x-0',
    right: 'sm:flex-row-reverse sm:space-x-reverse',
    bottom: 'sm:flex-col-reverse sm:space-y-reverse',
  },
  md: {
    horizontal: 'md:flex-row md:space-x-2 rtl:md:space-x-reverse md:space-y-0',
    vertical: 'md:flex-col md:space-y-2 md:space-x-0',
    right: 'md:flex-row-reverse md:space-x-reverse',
    bottom: 'md:flex-col-reverse md:space-y-reverse',
  },
  lg: {
    horizontal: 'lg:flex-row lg:space-x-2 rtl:lg:space-x-reverse lg:space-y-0',
    vertical: 'lg:flex-col lg:space-y-2 lg:space-x-0',
    right: 'lg:flex-row-reverse lg:space-x-reverse',
    bottom: 'lg:flex-col-reverse lg:space-y-reverse',
  },
  xl: {
    horizontal: 'xl:flex-row xl:space-x-2 rtl:xl:space-x-reverse xl:space-y-0',
    vertical: 'xl:flex-col xl:space-y-2 xl:space-x-0',
    right: 'xl:flex-row-reverse xl:space-x-reverse',
    bottom: 'xl:flex-col-reverse xl:space-y-reverse',
  },
}

const breakpointKeys = Object.keys(positions)
