import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))

const iconColors = {
  facebook: '#0c60dd',
  twitter: '#1da1f2',
  instagram: '#c13584',
  youtube: '#ff0000',
  pinterest: '#bd081c',
  linkedin: '#0077b5',
  whatsapp: '#25d366',
  telegram: '#0088cc',
  snapchat: '#fffc00',
  tiktok: '#000000',
  vimeo: '#1ab7ea',
}

export default function SocialMediaLinks({ pageData }) {
  const { socialLinks } = pageData?.site || {}

  if (!socialLinks?.length) return null

  return (
    <div className="flex flex-row flex-wrap justify-center space-x-4 text-6xl rtl:space-x-reverse">
      {socialLinks
        ?.filter(item => item.url)
        .map((item, i) => {
          if (item.type) {
            const iconColor = iconColors[item.type]
            return (
              <Link
                to={item.url}
                external
                key={`social-link-${item.type}-${i}`}
                className="mb-2"
              >
                <Icon
                  className="text-black"
                  name={item.icon || item.type}
                  style={iconColor ? { color: iconColor } : undefined}
                />
              </Link>
            )
          }
          return null
        })}
    </div>
  )
}
SocialMediaLinks.propTypes = {
  pageData: PropTypes.object,
}
