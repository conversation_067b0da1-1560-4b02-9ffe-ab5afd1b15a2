import PropTypes from 'prop-types'

import UIMainMenu, {
  ItemLevel1,
  ItemLevel2,
  ItemLevel3,
} from 'ui/navigation/MainMenu'

export default function MainMenu({ className = '', items = [] }) {
  if (!Array.isArray(items) || !items?.length) return null
  return (
    <UIMainMenu className={className}>
      {items?.map((level1, i) => (
        <ItemLevel1
          label={level1.label}
          url={level1.url}
          key={`${level1.url}-${i}`}
        >
          {level1.items?.map((level2, i) => (
            <ItemLevel2
              label={level2.label}
              url={level2.url}
              key={`${level2.url}-${i}`}
            >
              {level2.items?.map((level3, i) => (
                <ItemLevel3
                  label={level3.label}
                  url={level3.url}
                  key={`${level3.url}-${i}`}
                />
              ))}
            </ItemLevel2>
          ))}
        </ItemLevel1>
      ))}
    </UIMainMenu>
  )
}
MainMenu.propTypes = {
  className: PropTypes.string,
  items: PropTypes.array,
}
