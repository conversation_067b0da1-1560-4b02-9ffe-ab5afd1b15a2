import clsx from 'clsx'
import { useMemo } from 'react'

import { getBackgroundGradientStyle } from 'ui/helpers/getBackgroundGradientStyle'
import { getBackgroundColor } from 'ui/helpers/getColor'
import useBackgroundImage from 'ui/helpers/useBackroundImage'
import useFieldResource from 'ui/helpers/useFieldResource'
// import useFlex from 'ui/helpers/useFlex'
import { useHeight } from 'ui/helpers/useHeight'
import usePadding from 'ui/helpers/usePadding'
import { usePosition } from 'ui/helpers/usePosition'
import { usePositionOffset } from 'ui/helpers/usePositionOffset'
import useSpacing from 'ui/helpers/useSpacing'
import useZIndex from 'ui/helpers/useZIndex'

/**
 * @typedef {import('ui/helpers/getBackgroundGradientStyle').BgGradient} BgGradient The background gradient of the container.
 * @typedef {import('ui/helpers/usePositionOffset').PositionOffset} PositionOffset The position offset of the container.
 * @typedef {import('ui/helpers/useTransformTranslate').TransformTranslate} TransformTranslate The translation of the container.
 */

/**
 * A Section component that can be used to create a section in the page.
 * @param {Object} props
 * @param {Object} props.align The alignment of the children inside the section.
 * @param {String} props.bgColor The background color of the section.
 * @param {BgGradient} props.bgGradient The background gradient of the section.
 * @param {Object} props.bgImage The background image of the section.
 * @param {String} props.bgImageAlign The alignment of the background image.
 * @param {Object} props.bgImageSource The source of the background image.
 * @param {'solid'|'gradient'} props.bgType The type of the background.
 * @param {React.ReactNode} props.children The children of the section.
 * @param {String} props.className The class name of the section.
 * @param {Object} props.direction The direction of the section.
 * @param {Object} props.height The height of the section.
 * @param {String} props.id The id of the section.
 * @param {Object} props.justify The justification of the children inside the section.
 * @param {Object} props.position The position of the section.
 * @param {PositionOffset} props.positionOffset The position offset of the section.
 * @param {Object} props.spacing The spacing of the section.
 * @param {Object} props.stackOrder The stack order of the section.
 * @returns {React.ReactElement} The Section component.
 */
export default function Section({
  // align,
  bgColor,
  bgGradient,
  bgImage,
  bgImageAlign,
  bgImageSource,
  bgType,
  children,
  className = '',
  // direction,
  height,
  id,
  // justify,
  padding,
  position,
  positionOffset,
  spacing,
  stackOrder,
}) {
  const bgColorClass = getBackgroundColor(bgColor)
  const spacingClass = useSpacing(spacing)
  const paddingClass = usePadding(padding)
  const positionClass = usePosition(position)
  const zIndexClass = useZIndex(stackOrder)

  // const flexClasses = useFlex(direction ?? { xs: 'y' }, align, justify)

  const bgImageFile = useFieldResource(bgImageSource, bgImage)

  const heightStyle = useHeight(height)

  const positionOffsetStyles = usePositionOffset(position, positionOffset)

  const bgImageStyle = useBackgroundImage(bgImageFile, bgImageAlign, 1920)

  const bgGradientStyle = useMemo(() => {
    if (!bgGradient || bgType === 'solid') return null

    return getBackgroundGradientStyle(bgGradient)
  }, [bgGradient, bgType])

  const containerStyle = useMemo(() => {
    if (position === 'static' && bgType === 'solid') return null

    return {
      ...(positionOffsetStyles || {}),
      ...((!bgImageFile && bgGradientStyle) || {}),
      ...(bgImageStyle || {}),
      ...(heightStyle || {}),
    }
  }, [
    position,
    bgType,
    positionOffsetStyles,
    bgImageFile,
    bgGradientStyle,
    bgImageStyle,
    heightStyle,
  ])

  return (
    <div
      data-type="Section"
      className={clsx(
        {
          'bg-cover bg-no-repeat': bgImageFile,
          'relative': positionClass === 'static',
          [positionClass]: positionClass !== 'static',
        },
        // flexClasses,
        bgColorClass,
        zIndexClass,
        paddingClass,
        spacingClass,
        className
      )}
      id={id}
      style={containerStyle}
    >
      {children}
    </div>
  )
}
