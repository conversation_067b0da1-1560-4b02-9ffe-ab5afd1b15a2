import React, { useState } from 'react'
import { InputField } from 'ui/data-entry/Input'
import Icon from 'ui/icons/Icon'
import Link from 'ui/navigation/Link'

export default function PublishingHouses({
  publishingHouses,
  locationLabel = 'Location',
  languagesLabel = 'Languages',
  emailLabel = 'Email',
  websiteLabel = 'Website',
  filterPlaceholder = 'Filter by name, languages, or location',
  noResultsLabel,
} = {}) {
  const [filter, setFilter] = useState('')

  // If publishing houses items count is less than 0 or filter is empty, we return null
  if (!publishingHouses || publishingHouses.items.length <= 0) {
    return null
  }

  const filteredPublishingHouses = publishingHouses.items.filter(
    publishingHouse => {
      const { name, languages, location } = publishingHouse
      const lowerCaseFilter = filter.toLowerCase()

      return (
        name.toLowerCase().includes(lowerCaseFilter) ||
        languages.some(language => {
          if (!language) {
            return false
          }
          return language.toLowerCase().includes(lowerCaseFilter)
        }) ||
        location.toLowerCase().includes(lowerCaseFilter)
      )
    }
  )

  const handleFilterChange = event => {
    setFilter(event.target.value)
  }

  return (
    <div className="text-gray-700 py-2 px-1">
      <InputField
        name="filter"
        className="sticky top-0 z-10 bg-white pb-2"
        placeholder={filterPlaceholder}
        value={filter}
        onChange={handleFilterChange}
      />

      {filteredPublishingHouses.length === 0 && (
        <p className="text-md py-6 text-center text-gray-500">
          {noResultsLabel}
        </p>
      )}
      {filteredPublishingHouses.length > 0 &&
        filteredPublishingHouses.map((publishingHouse, index) => (
          <PublishingHouse
            key={index}
            {...publishingHouse}
            languagesLabel={languagesLabel}
            locationLabel={locationLabel}
            emailLabel={emailLabel}
            websiteLabel={websiteLabel}
          />
        ))}
    </div>
  )
}

function PublishingHouse({
  className,
  name,
  languages,
  location,
  email,
  website,
  languagesLabel,
  locationLabel,
  emailLabel,
  websiteLabel,
}) {
  const [expandLanguages, setExpandLanguages] = useState(false)
  //? HANDLERS
  const handleExpandLanguages = () => setExpandLanguages(() => true)

  //? translation hook

  return (
    <div className={`border-b-line-divider border-b py-6 ${className}`}>
      <span className="font-semibold">{name}</span>
      <div className="flex flex-col gap-y-2">
        <div className="flex gap-x-2 gap-y-3">
          {languagesLabel}:
          <span>
            {languages?.sort()?.slice(0, 5).join(', ') +
              `${expandLanguages ? ', ' : ''}`}
            {languages?.slice(5).length !== 0 && !expandLanguages ? (
              <button
                onClick={handleExpandLanguages}
                className="ml-1 ms-2 inline-flex items-center gap-x-1 border-b border-b-black text-black"
              >
                +<span>{languages?.slice(5).length}</span>
              </button>
            ) : (
              languages?.slice(5).join(', ')
            )}
          </span>
        </div>

        <div className="flex gap-1.5">
          {locationLabel}:<span>{location}</span>
        </div>

        <div className="flex items-center gap-4">
          {email && (
            <span className="inline-flex items-center gap-2">
              <Icon name="email" />
              <Link href={`mailto:${email}`}>{emailLabel}</Link>
            </span>
          )}

          {website && (
            <span className="inline-flex items-center gap-2">
              <Icon name="globe" />
              <Link to={website}>{websiteLabel}</Link>
            </span>
          )}
        </div>
      </div>
    </div>
  )
}
