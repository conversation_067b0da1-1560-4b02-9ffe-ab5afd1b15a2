import React from 'react'
import PropTypes from 'prop-types'
import Icon from 'ui/icons/Icon'
import { nFormatter } from 'utils/strings'

export default function PublicationStatistics({
  publicationStatistics,
  publicationLabel = 'Publications',
  languagesLabel = 'Languages',
  downloadsLabel = 'Downloads',
  showDownloadCount,
  showLanguages,
  showPublications = true,
  variant = 'sharingHope',
} = {}) {
  const { publicationCount, languageCount, downloadCount } =
    publicationStatistics || {}

  const renderSharingHope = () => (
    <div className="flex w-full flex-col items-start gap-6 md:w-auto md:flex-row md:flex-wrap md:items-center">
      {showPublications && (
        <StatsItem
          count={publicationCount}
          label={publicationLabel}
          icon="book"
        />
      )}
      {showLanguages && (
        <StatsItem
          count={languageCount}
          label={languagesLabel}
          icon="speech-bubble"
        />
      )}
      {showDownloadCount && (
        <StatsItem
          count={nFormatter(downloadCount, 1)}
          label={downloadsLabel}
          icon="download-arrow"
        />
      )}
    </div>
  )

  // Great controversy variant allows the parent container to control the layout.
  const renderGreatControversy = () => (
    <div className="grid w-full grid-cols-2 gap-4 md:grid-cols-1 md:grid-rows-2 xl:flex xl:flex-wrap xl:gap-6">
      {showDownloadCount && (
        <GreatControversyStatsItem
          count={nFormatter(downloadCount, 1)}
          label={downloadsLabel}
          icon="download-arrow-cloud"
        />
      )}
      {showLanguages && (
        <GreatControversyStatsItem
          count={languageCount}
          label={languagesLabel}
          icon="translate-bubble"
        />
      )}
    </div>
  )

  const variantRenderers = {
    sharingHope: renderSharingHope,
    greatControversy: renderGreatControversy,
  }

  return variantRenderers[variant]()
}
PublicationStatistics.propTypes = {
  publicationStatistics: PropTypes.object,
}

function StatsItem({ count, label, icon }) {
  return (
    <div className="flex flex-row items-center gap-4">
      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-50 text-color1-400 md:h-14 md:w-14 md:text-xl">
        <Icon name={icon} />
      </div>
      <div className="flex flex-col">
        <strong className="text-lg font-bold">{count}</strong>
        <p className="text-gray-400">{label}</p>
      </div>
    </div>
  )
}

function GreatControversyStatsItem({ count, label, icon }) {
  return (
    <div className="flex flex-row items-center gap-4">
      <div className="flex h-12 w-12 items-center justify-center">
        <Icon name={icon} size={'40'} />
      </div>
      <div className="flex flex-col">
        <strong className="text-lg font-bold text-gray-700">{count}</strong>
        <p className="text-gray-800">{label}</p>
      </div>
    </div>
  )
}
