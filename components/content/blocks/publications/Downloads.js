import React from 'react'
import dynamic from 'next/dynamic'

import { usePageContext } from 'components/PageProvider'

import useFlex from 'ui/helpers/useFlex'
import useSpacing from 'ui/helpers/useSpacing'

import VariableCarousel, {
  VariableCarouselItem,
} from 'ui/data-display/VariableCarousel'
import useDownloadCountIncrement from './shared/useDownloadCountIncrement'
import { PublicationDownloadLink } from './shared/DownloadButtons'

const Dropdown = dynamic(() => import('ui/buttons/Dropdown'))
const DropdownItem = dynamic(() =>
  import('ui/buttons/Dropdown').then(mod => mod.DropdownItem)
)
const Icon = dynamic(() => import('ui/icons/Icon'))
const PublicationPoster = dynamic(() => import('./shared/Poster'))
const NotFound = dynamic(() => import('ui/feedback/NotFound'))

const iconMap = {
  pdfDigital: 'file-pdf',
  pdfPrinting: 'file-pdf',
  epub: 'file-epub',
  promotionalMaterials: 'grid',
  studyGuide: 'open-book',
  readingPlan: 'closed-book',
  mp3: 'headphones',
}

export default function PublicationDownloads({
  Publication,
  // title,
  languageLabel,
  downloadLabel,
  notFoundTitle,
  notFoundDescription,
  publicationDownloads,
  direction,
  showCount,
  promotionalMaterialsLabel,
  studyGuideLabel,
  readingPlanLabel,
  audioLabel,
  noResultsLabel,
  color = 'color1-600',
}) {
  const firstItemRef = React.useRef()

  const labelMap = {
    epub: 'EPUB',
    pdfDigital: 'PDF',
    pdfPrinting: 'PDF/X',
    promotionalMaterials: promotionalMaterialsLabel,
    studyGuide: studyGuideLabel,
    readingPlan: readingPlanLabel,
    mp3: audioLabel,
  }

  const page = usePageContext()
  const { publications } = publicationDownloads || {}

  const wrapClass = useFlex(direction || { xs: 'y' })
  const spacingClasses = useSpacing({ xs: 'lg' })

  const handleDownloadCount = useDownloadCountIncrement()

  if ((!Publication || Publication.notFound) && !publicationDownloads)
    return (
      <div className="grow">
        <NotFound
          title={notFoundTitle}
          description={notFoundDescription}
          headTitle={`${notFoundTitle} | ${page.site.title}`}
          code="404"
        />
      </div>
    )

  return (
    <div className={`${wrapClass} ${spacingClasses} w-full`}>
      {publications?.length === 0 && (
        <div className="flex h-24 items-center justify-center">
          <p className="text-xl text-gray-500">{noResultsLabel}</p>
        </div>
      )}

      <VariableCarousel
        color={color}
        firstItemRef={firstItemRef}
        gradientColor="from-transparent to-white"
      >
        {publications?.map((publication, i) => {
          const {
            epub,
            pdfPrinting,
            pdfDigital,
            readingPlan,
            promotionalMaterials,
            studyGuide,
            mp3,
            language, // i.e. 'English', 'Spanish', etc.
            locale, // i.e. 'en', 'es', etc.
            id: publicationId,
          } = publication

          // we create an object with the downloads that are not undefined
          const downloads = {
            ...(pdfDigital !== undefined ? { pdfDigital } : {}),
            ...(pdfPrinting !== undefined ? { pdfPrinting } : {}),
            ...(epub !== undefined ? { epub } : {}),
            ...(readingPlan !== undefined ? { readingPlan } : {}),
            ...(promotionalMaterials !== undefined
              ? { promotionalMaterials }
              : {}),
            ...(studyGuide !== undefined ? { studyGuide } : {}),
            ...(mp3 !== undefined ? { mp3 } : {}),
          }

          return (
            <VariableCarouselItem
              key={`publication-item-${i}`}
              index={i}
              length={publications?.length}
              firstItemRef={firstItemRef}
              className={`min-w-[200px] max-w-[300px] touch-auto snap-start scroll-mx-10 space-y-4 xl:min-w-[calc(25%-40px)]`}
              firstMargin={`ml-0`}
              lastMargin={`mr-0`}
            >
              <PublicationPoster
                publication={publication}
                showTitle={false}
                showHover={false}
              />
              <p className="text-md">{`${languageLabel}: ${language}`}</p>

              <Dropdown
                button={
                  <div className="flex min-w-[180px] cursor-pointer items-center justify-between self-start rounded-full border border-color1-600 px-4 py-2 font-semibold text-color1-600">
                    <span>{downloadLabel}</span>
                    <Icon
                      name="chevron-down"
                      className="text-sm text-color1-500"
                    />
                  </div>
                }
                name="language-download"
                itemsClass="min-w-[100px] rounded-lg"
                alignment={{ xs: 'bottom-start' }}
                variant="flat"
                color="none"
              >
                {Object.keys(downloads).map(downloadType => {
                  return (
                    <DropdownItem
                      key={downloadType}
                      onClick={() =>
                        handleDownloadCount({
                          language: locale,
                          publicationId,
                        })
                      }
                      icon={iconMap[downloadType]}
                      label={labelMap[downloadType]}
                      href={downloads[downloadType].url}
                      CustomLink={PublicationDownloadLink}
                    />
                  )
                })}
              </Dropdown>
              {showCount && (
                <p>
                  {publication.downloadCount?.current
                    ? `${publication.downloadCount?.current} downloads`
                    : ''}
                </p>
              )}
            </VariableCarouselItem>
          )
        })}
      </VariableCarousel>
    </div>
  )
}
