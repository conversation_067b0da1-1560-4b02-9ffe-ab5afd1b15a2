import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { usePageContext } from 'components/PageProvider'

import Link from 'ui/navigation/Link'
import ShareButton from '../ShareButton'

const DetailItem = dynamic(() => import('./shared/DetailItem'))
const DownloadButtons = dynamic(() => import('./shared/DownloadButtons'))
const Image = dynamic(() => import('ui/data-display/Image'))
const NotFound = dynamic(() => import('ui/feedback/NotFound'))
const RichText = dynamic(() => import('ui/typography/RichText'))

const categoryStyles = {
  'doctrine': 'text-[#ca8a04]',
  'felt-need': 'text-[#ea580c]',
  'default': 'text-gray-500',
}

export default function PublicationDetail({
  Publication,
  pageData,
  summaryLabel,
  typeLabel,
  authorLabel,
  publisherLabel,
  availableLanguagesLabel,
  availableLanguages,
  pagesLabel,
  categoriesLabel,
  downloadLabel,
  languageLabel,
  showPdfDigital,
  pdfDigitalLabel,
  pdfDigitalTooltip,
  showPdfPrinting,
  pdfPrintingLabel,
  pdfPrintingTooltip,
  showEpub,
  epubLabel,
  epubTooltip,
  showPromotionalMaterials,
  promotionalMaterialsLabel,
  promotionalMaterialsTooltip,
  showStudyGuide,
  studyGuideLabel,
  studyGuideTooltip,
  showReadingPlan,
  readingPlanLabel,
  readingPlanTooltip,
  showAudio,
  audioLabel,
  audioTooltip,
  notFoundTitle = 'Publication not found',
  notFoundDescription = 'The requested publication could not be found.',
  showAuthor,
  showBody = true,
  showFullContent = false,
  fullContentLabel = '',
  showCategories,
  showDescription = true,
  showDownloads,
  showPublisher,
  sharing,
  showImage = true,
  showReligion,
  showSubtitle,
  showType,
  showPages,
  showLanguages,
  showTitle = true, // Since this is a new flag this has to be enabled for those that did not have it.
  variant = 'sharingHope',
  modalButtonLabel,
  modalName,
  modalButtonVariant,
  languageButtonVariant,
  downloadButtonVariant,
  publicationDetail,
} = {}) {
  const page = usePageContext()

  const { religion, downloadLinks } = publicationDetail || {}

  // Get the publication from the publicationDetail prop (a chery-picked one) or the Publication prop (the one from the resource fetcher)
  Publication = publicationDetail?.Publication || Publication

  if (!Publication || Publication.notFound)
    return (
      <div className="grow">
        <NotFound
          title={notFoundTitle}
          description={notFoundDescription}
          headTitle={`${notFoundTitle} | ${page.site.title}`}
          code="404"
        />
      </div>
    )

  const {
    _id: publicationId,
    title,
    categories,
    subtitle,
    description,
    author,
    location,
    body,
    fullContent,
    cover,
    publisher,
    pages,
    type,
  } = Publication

  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col gap-8 md:flex-row lg:gap-12 lg:p-0">
        {showImage && cover?.file && (
          <div className="flex w-full items-start justify-center md:w-2/5 md:min-w-[320px]">
            <Image
              file={cover.file}
              alt={title}
              className="rounded-lg sm:w-2/3 md:w-full md:rounded-2xl"
              priority
              sizes="lg:640px md:975px sm:720px 592px"
              lazy={false}
            />
          </div>
        )}
        <div
          className={`space-y-10 pt-4 md:w-3/5 ${
            variant === 'sharingHope' ? 'md:pt-2' : 'md:pt-0'
          }`}
        >
          {(showTitle || showSubtitle || showDescription) && (
            <div className="space-y-2">
              <div className="space-y-1">
                {showTitle && (
                  <h1 className="text-2xl font-medium md:text-4xl">{title}</h1>
                )}
                {showSubtitle && subtitle && (
                  <h3 className="text-xl font-normal text-gray-600">
                    {subtitle}
                  </h3>
                )}
              </div>
              {showDescription && (
                <p className="text-xl italic leading-6 text-gray-600">
                  {description}
                </p>
              )}
            </div>
          )}

          <DetailItem
            className="space-y-4"
            label={summaryLabel}
            variant={variant} // TODO: create customization for this component, like text
          >
            {showBody && (
              <RichText
                doc={body}
                decreaseHeaders
                // TODO: This is a temporary fix for the great controversy page. We should add custom body text size.
                textSize={
                  variant === 'greatControversy' ? { xs: 'md' } : undefined
                }
              />
            )}
          </DetailItem>
          <div className="grid w-full grid-cols-2 gap-x-6 gap-y-10 lg:grid-cols-9 lg:gap-6">
            {showType && type && (
              <DetailItem
                className="col-span-1 lg:col-span-2"
                label={typeLabel}
              >
                <p className="text-lg">{type.title}</p>
              </DetailItem>
            )}
            {showAuthor && (author || location) && (
              <DetailItem
                className="col-span-1 lg:col-span-3"
                label={authorLabel}
              >
                <p className="text-lg">{author || location}</p>
              </DetailItem>
            )}

            {showPublisher && publisher && (
              <DetailItem
                className={`col-span-1 ${
                  showAuthor && author ? 'lg:col-span-4' : 'lg:col-span-3'
                }`}
                label={publisherLabel}
              >
                {showPublisher && publisher && (
                  <p className="text-lg">{publisher}</p>
                )}
              </DetailItem>
            )}
            {showLanguages && downloadLinks && (
              <DetailItem
                className="col-span-1 lg:col-span-4"
                label={availableLanguagesLabel}
              >
                <p className="text-lg">
                  {`${downloadLinks?.length} ${availableLanguages}`}
                </p>
              </DetailItem>
            )}
            {showPages && pages && (
              <DetailItem
                className="col-span-1 col-start-1 row-start-2 lg:col-span-2"
                label={pagesLabel}
              >
                <p className="text-lg">{pages}</p>
              </DetailItem>
            )}

            {(showCategories || showReligion) && (
              <DetailItem
                className="col-span-2 lg:col-span-7"
                label={categoriesLabel}
              >
                <div className="flex flex-wrap items-center gap-x-3 gap-y-1 text-sm">
                  {showReligion && religion && (
                    <Link
                      className="inline-flex items-center gap-2"
                      to={religion.url}
                    >
                      <span className="text-sm font-semibold text-color1-500">
                        #{religion.title}
                      </span>
                    </Link>
                  )}
                  {showCategories &&
                    categories.map(category => (
                      <Link
                        className={`${
                          categoryStyles[category.type] ||
                          categoryStyles.default
                        } `}
                        // to="/publications" // TODO: Add the correct link?
                        key={`category-${category.name}`}
                      >
                        #{category.title}
                      </Link>
                    ))}
                </div>
              </DetailItem>
            )}
          </div>

          <div className="flex flex-col gap-4">
            {showDownloads && downloadLinks && variant === 'sharingHope' && (
              <DetailItem label={downloadLabel} />
            )}
            <div className="flex flex-row items-center justify-between gap-2">
              {showDownloads && downloadLinks && (
                <DownloadButtons
                  downloadLinks={downloadLinks}
                  variant={variant}
                  currentLanguage={page.language}
                  availableLanguages={pageData.availableLanguages}
                  languageLabel={languageLabel}
                  publicationId={publicationId}
                  downloadLabel={downloadLabel}
                  showPdfDigital={showPdfDigital}
                  pdfDigitalLabel={pdfDigitalLabel}
                  pdfDigitalTooltip={pdfDigitalTooltip}
                  showPdfPrinting={showPdfPrinting}
                  pdfPrintingLabel={pdfPrintingLabel}
                  pdfPrintingTooltip={pdfPrintingTooltip}
                  showEpub={showEpub}
                  epubLabel={epubLabel}
                  epubTooltip={epubTooltip}
                  showPromotionalMaterials={showPromotionalMaterials}
                  promotionalMaterialsLabel={promotionalMaterialsLabel}
                  promotionalMaterialsTooltip={promotionalMaterialsTooltip}
                  showStudyGuide={showStudyGuide}
                  studyGuideLabel={studyGuideLabel}
                  studyGuideTooltip={studyGuideTooltip}
                  showReadingPlan={showReadingPlan}
                  readingPlanLabel={readingPlanLabel}
                  readingPlanTooltip={readingPlanTooltip}
                  showAudio={showAudio}
                  audioLabel={audioLabel}
                  audioTooltip={audioTooltip}
                  modalButtonLabel={modalButtonLabel}
                  modalName={modalName}
                  modalButtonVariant={modalButtonVariant}
                  downloadButtonVariant={downloadButtonVariant}
                  languageButtonVariant={languageButtonVariant}
                />
              )}
              {Object.keys(sharing).length > 0 && (
                <div
                  className={`flex items-center justify-end self-end ${
                    variant === 'sharingHope' ? 'lg:self-center' : ''
                  }`}
                >
                  <ShareButton pageData={pageData} {...sharing} />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {showFullContent && (
        <DetailItem
          className="space-y-4"
          label={fullContentLabel}
          variant={variant}
        >
          <RichText
            doc={fullContent}
            decreaseHeaders
            textSize={variant === 'greatControversy' ? { xs: 'md' } : undefined}
          />
        </DetailItem>
      )}
    </div>
  )
}
PublicationDetail.propTypes = {
  Publication: PropTypes.object,
  downloadLabel: PropTypes.string,
  pageData: PropTypes.object,
  notFoundTitle: PropTypes.string,
  notFoundDescription: PropTypes.string,
  languageLabel: PropTypes.string,
  showAuthor: PropTypes.bool,
  showBody: PropTypes.bool,
  showCategories: PropTypes.bool,
  showDescription: PropTypes.bool,
  showDownloads: PropTypes.bool,
  showImage: PropTypes.bool,
  showPublisher: PropTypes.bool,
  showReligion: PropTypes.bool,
  showType: PropTypes.bool,
  showPages: PropTypes.bool,
  showSubtitle: PropTypes.bool,
}
