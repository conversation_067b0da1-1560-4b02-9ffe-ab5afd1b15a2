import PropTypes from 'prop-types'
import { useMemo } from 'react'
import Dropdown from 'ui/buttons/Dropdown'

import dynamic from 'next/dynamic'

const Image = dynamic(() => import('ui/data-display/Image'))
const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))
const LinkList = dynamic(() => import('ui/navigation/LinkList'))
const LinkItem = dynamic(() =>
  import('ui/navigation/LinkList').then(m => m.LinkItem)
)

const variantsMap = {
  simple: ReligionMenuSimple,
  dropdown: ReligionMenuDropdown,
  large: ReligionMenuLarge,
}

export default function PublicationReligionsMenu({
  borderRadius,
  publicationsReligions = { items: [], count: 0 },
  label,
  id,
  className,
  alignment,
  size = 'sm',
  variant,
  showAllLink,
  showAllLabel,
  showIcons = true,
}) {
  const ComponentMenu = variantsMap[variant] || variantsMap.simple
  const { items, publicationsUrl } = publicationsReligions || {}

  return (
    <ComponentMenu
      items={items || []}
      label={label}
      id={id}
      className={className}
      borderRadius={borderRadius}
      alignment={alignment}
      showIcons={showIcons}
      size={size}
      showAllLink={showAllLink}
      showAllLabel={showAllLabel}
      publicationsUrl={publicationsUrl}
    />
  )
}
PublicationReligionsMenu.propTypes = {
  borderRadius: PropTypes.object,
  publicationsReligions: PropTypes.shape({
    items: PropTypes.array,
    count: PropTypes.number,
    publicationsUrl: PropTypes.string,
  }),
  label: PropTypes.string,
  limit: PropTypes.number,
  showIcons: PropTypes.bool,
  size: PropTypes.string,
}

function ReligionMenuSimple({ id, className, items, label, showIcons }) {
  const menuItems = useMemo(
    () =>
      items?.map(({ title, url, icon }) => ({
        label: title,
        url,
        icon: showIcons && <Image file={icon} width="10" />,
      })),
    [items, showIcons]
  )

  return (
    <LinkList title={label} className={className} id={id}>
      {menuItems.map((item, i) => (
        <LinkItem key={`menu-item-${i}`} icon="page" {...item} />
      ))}
    </LinkList>
  )
}

function ReligionMenuDropdown({
  className,
  borderRadius,
  alignment,
  id,
  label,
  size,
  showIcons,
  items,
}) {
  return (
    <Dropdown
      className={`space-y-2 ${className}`}
      alignment={alignment || { xs: 'bottom-end' }}
      borderRadius={borderRadius}
      id={id}
      icon="bars"
      name="language-menu"
      label={label}
      labelClass="hidden md:block"
      iconClass="block md:hidden text-xl"
      secondaryIconClass="hidden md:block"
      size={size}
      itemsClass="max-w-[280px]"
      variant="flat"
      color="none"
      hasMaxHeight={false}
    >
      {label && (
        <div className="px-4 pt-4 text-sm font-semibold md:hidden">{label}</div>
      )}
      <div className="flex flex-col gap-2 p-2">
        {items?.map(({ title, name, icon, description, url }) => (
          <Link
            activeClass="bg-color1-100 hover:bg-color1-200/75"
            className="flex items-center gap-4 rounded-md px-3 py-2 transition-colors duration-300 ease-out hover:bg-gray-50"
            key={`religion-${name}`}
            to={url}
          >
            {showIcons && (
              <div className="flex w-16 items-center justify-center">
                <Image
                  className="h-12 w-12"
                  file={icon}
                  alt={title || name}
                  priority
                  lazy={false}
                />
              </div>
            )}
            <div className="flex flex-col">
              <strong className="font-semibold">{title}</strong>
              <span className="text-sm text-gray-500 ">{description}</span>
            </div>
          </Link>
        ))}
      </div>
    </Dropdown>
  )
}

function ReligionMenuLarge({
  id,
  className,
  items,
  label,
  showIcons,
  showAllLink,
  showAllLabel,
  publicationsUrl,
}) {
  return (
    <div id={id} className={className}>
      {label && <h3 className="text-lg font-semibold">{label}</h3>}

      <div className=" overflow-hidden">
        <div className="-m-[1px] grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          {items?.map(item => (
            <LargeItem
              item={item}
              showIcon={showIcons}
              key={`religion-${item.name}`}
            />
          ))}
          {showAllLink && publicationsUrl && (
            <LargeItemLink to={publicationsUrl}>
              <span className="flex w-full items-center justify-center gap-2 text-color1-700">
                <span className="font-semibold">
                  {showAllLabel || 'All publications'}
                </span>
                <Icon name="arrow" className="pt-px" />
              </span>
            </LargeItemLink>
          )}
        </div>
      </div>
    </div>
  )
}

function LargeItemLink({ children, to }) {
  return (
    <Link
      className="flex items-center justify-start gap-4 border border-gray-200 p-12 transition-colors duration-300 ease-in-out hover:bg-gray-100/50"
      to={to}
    >
      {children}
    </Link>
  )
}

function LargeItem({ item, showIcon }) {
  return (
    <LargeItemLink to={item.url}>
      {showIcon && (
        <div className="flex w-16 items-center justify-center">
          {item.icon ? (
            <Image
              className="h-12 w-12"
              file={item.icon}
              alt={item.title || item.name}
              priority
              lazy={false}
            />
          ) : (
            <div className="flex h-12 w-12 items-center justify-center text-xl text-gray-400">
              <Icon name="chevron-right" />
            </div>
          )}
        </div>
      )}
      <div className="flex flex-col">
        <strong className="text-xl font-semibold lg:text-2xl lg:leading-6">
          {item.title}
        </strong>
      </div>
    </LargeItemLink>
  )
}
