import PropTypes from 'prop-types'
import React, { useMemo, useState } from 'react'

import clsx from 'clsx'
import { useTranslation } from 'next-i18next'

import Button from 'ui/buttons/Button'
import { default as UIMap } from 'ui/data-display/Map'
import useAspectRatio from 'ui/helpers/useAspectRatio'
import useWidth from 'ui/helpers/useWidth'

function getBoundsFromPublishingHouses(publishingHouses) {
  const initialCoordinates =
    publishingHouses.items[0].locationPicker.coordinates
  const initialBounds = [
    initialCoordinates[0],
    initialCoordinates[1],
    initialCoordinates[0],
    initialCoordinates[1],
  ]

  return publishingHouses.items.reduce((acc, { locationPicker }) => {
    return [
      Math.min(acc[0], locationPicker.coordinates[0]),
      Math.max(acc[1], locationPicker.coordinates[1]),
      Math.max(acc[2], locationPicker.coordinates[0]),
      Math.min(acc[3], locationPicker.coordinates[1]),
    ]
  }, initialBounds)
}

export default function PublishingHousesMap({
  publishingHouses,
  className,
  aspectRatio,
  width,
  showSearch,
  showControls,
  showGeolocate,
  ctrlToZoom,
  resetZoom,
  resetZoomButton,
  mapStyle,
}) {
  const { t } = useTranslation()

  const [viewport, setViewport] = useState()

  const markers = useMemo(
    () =>
      publishingHouses.items.map(
        ({
          address,
          location,
          locationPicker,
          languages,
          name,
          emails,
          phoneNumber,
          website,
        }) => ({
          address,
          location: {
            center: true,
            coordinates: locationPicker.coordinates,
            placeName: locationPicker.placeName,
          },
          country: location,
          languages,
          emails,
          phoneNumber,
          website,
          name,
        })
      ),
    [publishingHouses]
  )

  return (
    <UIMap
      className={clsx(
        'min-h-[320px] w-full',
        useAspectRatio(aspectRatio),
        useWidth(width),
        className
      )}
      boundingBox={getBoundsFromPublishingHouses(publishingHouses)}
      markers={markers}
      mapStyle={mapStyle || undefined}
      viewport={viewport}
      onMove={setViewport}
      showSearch={showSearch}
      showControls={showControls}
      showGeolocate={showGeolocate}
      cooperativeGestures={ctrlToZoom}
      resetZoom={resetZoom}
      resetZoomButton={resetZoomButton}
      renderItemPopover={marker => (
        <>
          <div className="flex flex-row w-max max-w-64 md:max-w-96 p-1">
            <div className="flex flex-col items-start pr-3 border-r border-gray-300">
              <h3 className="font-bold xs:text-xs md:text-base text-left">
                {marker.name}
              </h3>
              <p className="italic">{marker.country}</p>
              <p className="text-left">{marker.address}</p>
              <p className="text-left pt-2">{marker.languages.join(', ')}</p>
            </div>

            <div className="flex flex-col justify-between pl-3">
              <div className="flex flex-col flex-shrink-0 items-start">
                <p className="text-left">{t('contactInformation')}:</p>
                {marker.phoneNumber ? (
                  <Button
                    label={marker.phoneNumber}
                    url={`tel:${marker.phoneNumber}`}
                    className="w-auto"
                    variant="flat"
                  />
                ) : null}
                {marker.emails?.map((email, index) => (
                  <Button
                    key={`email-${index}`}
                    label={email}
                    url={`mailto:${email}`}
                    variant="flat"
                    //break email if its too long
                    labelClass="text-wrap break-all text-left"
                  />
                ))}
              </div>
              <div className="flex flex-col items-start">
                {marker.website ? (
                  <Button
                    label={t('visitWebsite')}
                    url={marker.website}
                    variant="link"
                  />
                ) : null}
              </div>
            </div>
          </div>
        </>
      )}
    />
  )
}

Map.propTypes = {
  className: PropTypes.string,
  locations: PropTypes.arrayOf(PropTypes.object),
  showSearch: PropTypes.bool,
  showControls: PropTypes.bool,
  showGeolocate: PropTypes.bool,
}
