import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Publications = dynamic(() => import('./shared/Publications'))

export default function PublicationList({
  publications = { items: [], count: 0 },
  columns,
  variant,
  displayMode,
  label,
  showDescription,
  showImage,
  pagination,
  limit,
  showFilters,
  pageData,
  detailPageId,
  allLabel,
  filtersLabel,
  religionFilterLabel,
  feltNeedFilterLabel,
  doctrineFilterLabel,
  languageFilterLabel,
  noResultsLabel,
  hoverBackgroundColor,
  hoverBorderColor,
}) {
  return (
    <Publications
      items={publications?.items}
      count={publications?.count}
      religions={publications?.religions}
      categories={publications?.categories}
      limit={limit}
      variant={variant}
      displayMode={displayMode}
      label={label}
      showDescription={showDescription}
      showImage={showImage}
      pagination={pagination}
      columns={columns}
      showFilters={showFilters}
      pageData={pageData}
      detailPageId={detailPageId}
      allLabel={allLabel}
      filtersLabel={filtersLabel}
      religionFilterLabel={religionFilterLabel}
      feltNeedFilterLabel={feltNeedFilterLabel}
      doctrineFilterLabel={doctrineFilterLabel}
      languageFilterLabel={languageFilterLabel}
      noResultsLabel={noResultsLabel}
      downloadLanguages={publications?.downloadLanguages}
      hoverBackgroundColor={hoverBackgroundColor}
      hoverBorderColor={hoverBorderColor}
    />
  )
}
PublicationList.propTypes = {
  publications: PropTypes.shape({
    items: PropTypes.array,
    count: PropTypes.number,
  }),
  columns: PropTypes.object,
  variant: PropTypes.string,
  displayMode: PropTypes.string,
  label: PropTypes.string,
  limit: PropTypes.number,
  showDescription: PropTypes.bool,
  pagination: PropTypes.bool,
  showImage: PropTypes.bool,
}
