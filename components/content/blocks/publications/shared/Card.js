import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Card = dynamic(() => import('ui/data-display/Card'))

export default function PublicationCard({
  publication,
  showDescription = true,
  showImage,
}) {
  return (
    <Card
      title={publication.title}
      maxLinesTitle={2}
      description={showDescription ? publication.description : null}
      image={showImage ? publication.cover?.file : null}
      maxLinesDescription={3}
      url={publication.url}
      imageAspectRatio="16/9"
    />
  )
}

PublicationCard.propTypes = {
  publication: PropTypes.object,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
}
