import PropTypes from 'prop-types'
import { useCallback, useMemo } from 'react'

import dynamic from 'next/dynamic'

import { useRouter } from 'next/router'
import { usePageLoading } from 'ui/feedback/PageLoading'
import useColumns from 'ui/helpers/useColumns'
import useFlex from 'ui/helpers/useFlex'
import useSpacing from 'ui/helpers/useSpacing'
import { useAvailableLanguages } from 'hooks/useAvailableLanguages'

const Pagination = dynamic(() => import('ui/navigation/Pagination'))
const PublicationCard = dynamic(() => import('./Card'))
const PublicationPoster = dynamic(() => import('./Poster'))
const PublicationsFilters = dynamic(() => import('./PublicationsFilters'))
const LinkList = dynamic(() => import('ui/navigation/LinkList'))
const LinkItem = dynamic(() =>
  import('ui/navigation/LinkList').then(m => m.LinkItem)
)

const variantsMap = {
  list: PublicationsListVariant,
  menu: PublicationsMenu,
}

const itemDisplayModes = {
  cards: PublicationCard,
  posters: PublicationPoster,
}

export default function Publications({
  items,
  count,
  religions,
  categories,
  columns,
  direction,
  displayMode,
  variant,
  label,
  showDescription,
  showImage,
  pagination,
  limit,
  showFilters,
  pageData,
  allLabel,
  filtersLabel,
  religionFilterLabel,
  feltNeedFilterLabel,
  doctrineFilterLabel,
  languageFilterLabel,
  noResultsLabel,
  downloadLanguages,
  hoverBackgroundColor,
  hoverBorderColor,
}) {
  const router = useRouter()
  const { setPage } = usePageLoading()
  const { availableLanguages } = pageData

  const languages = useAvailableLanguages({ availableLanguages }) // This wont be used, we will use downloadLanguages instead

  const wrapClass = useFlex(direction || { xs: 'y' })
  const spacingClasses = useSpacing({ xs: 'lg' })
  const columnsClass = useColumns(columns, { xs: 1, md: 2, lg: 4 })

  const onFiltersChange = useCallback(
    filters => {
      const { religion, language, categories } = filters

      const query = {
        ...router.query,
      }

      if (religion) {
        if (religion === 'all') {
          delete query.filterReligion
        } else {
          query.filterReligion = religion
        }
      }

      if (language) {
        if (language === 'all') {
          delete query.filterLanguage
        } else {
          query.filterLanguage = language
        }
      }

      if (categories) {
        query.filterCategories = JSON.stringify(categories)
      }

      setPage(router.asPath)

      router.push({
        pathname: router.pathname,
        query,
      })
    },
    [router, setPage]
  )

  const Component = variantsMap[variant] || variantsMap.list

  return (
    <Component
      items={items || []}
      count={count}
      limit={limit}
      displayMode={displayMode}
      label={label}
      showDescription={showDescription}
      showImage={showImage}
      pagination={pagination}
      columns={columns}
      direction={direction}
      wrapClass={wrapClass}
      spacingClasses={spacingClasses}
      columnsClass={columnsClass}
      showFilters={showFilters}
      onFiltersChange={onFiltersChange}
      religions={religions || []}
      categories={categories || []}
      languages={languages || []}
      allLabel={allLabel}
      filtersLabel={filtersLabel}
      religionFilterLabel={religionFilterLabel}
      feltNeedFilterLabel={feltNeedFilterLabel}
      doctrineFilterLabel={doctrineFilterLabel}
      languageFilterLabel={languageFilterLabel}
      noResultsLabel={noResultsLabel}
      downloadLanguages={downloadLanguages}
      hoverBackgroundColor={hoverBackgroundColor}
      hoverBorderColor={hoverBorderColor}
      pageData={pageData}
    />
  )
}
Publications.propTypes = {
  columns: PropTypes.object,
  count: PropTypes.number,
  direction: PropTypes.object,
  variant: PropTypes.oneOf(['list', 'menu']),
  displayMode: PropTypes.oneOf(['cards', 'posters']),
  label: PropTypes.string,
  items: PropTypes.array,
  limit: PropTypes.number,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
  pagination: PropTypes.bool,
  showFilters: PropTypes.bool,
}

function PublicationsListVariant({
  items,
  count,
  limit,
  displayMode,
  showDescription,
  showImage,
  pagination,
  wrapClass,
  spacingClasses,
  columnsClass,
  showFilters,
  onFiltersChange,
  religions,
  categories,
  languages,
  allLabel,
  filtersLabel,
  religionFilterLabel,
  feltNeedFilterLabel,
  doctrineFilterLabel,
  languageFilterLabel,
  noResultsLabel,
  downloadLanguages,
  hoverBackgroundColor,
  hoverBorderColor,
  pageData,
}) {
  const ItemComponent = itemDisplayModes[displayMode] || itemDisplayModes.cards

  return (
    <div className={`${wrapClass} ${spacingClasses}`}>
      {showFilters && (
        <PublicationsFilters
          religions={religions}
          categories={categories}
          languages={languages}
          onFiltersChange={onFiltersChange}
          allLabel={allLabel}
          filtersLabel={filtersLabel}
          religionFilterLabel={religionFilterLabel}
          feltNeedFilterLabel={feltNeedFilterLabel}
          doctrineFilterLabel={doctrineFilterLabel}
          languageFilterLabel={languageFilterLabel}
          downloadLanguages={downloadLanguages}
          pageData={pageData}
        />
      )}
      {items?.length === 0 && (
        <div className="flex h-24 items-center justify-center">
          <p className="text-xl text-gray-500">{noResultsLabel}</p>
        </div>
      )}
      <div className={`grid grid-cols-1 gap-6 sm:gap-10 ${columnsClass}`}>
        {items?.map((publication, i) => (
          <ItemComponent
            publication={publication}
            showImage={showImage}
            showDescription={showDescription}
            key={`publication-item-${i}`}
            hoverBackgroundColor={hoverBackgroundColor}
            hoverBorderColor={hoverBorderColor}
          />
        ))}
      </div>
      {pagination && count && <Pagination total={count} pageSize={limit} />}
    </div>
  )
}

function PublicationsMenu({ items, label, className }) {
  const menuItems = useMemo(
    () =>
      items?.map(({ title, url }) => ({
        label: title,
        url,
        icon: '',
      })),
    [items]
  )

  return (
    <LinkList title={label} className={className}>
      {menuItems.map((item, i) => (
        <LinkItem key={`menu-item-${i}`} icon="page" {...item} />
      ))}
    </LinkList>
  )
}
