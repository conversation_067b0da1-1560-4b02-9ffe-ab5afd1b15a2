import React from 'react'

const variantClasses = {
  sharingHope: 'uppercase tracking-widest text-gray-600 text-sm',
  greatControversy: 'text-gray-800 text-3xl',
}

export default function DetailItem({
  label,
  children,
  className,
  variant = 'sharingHope',
} = {}) {
  return (
    <div className={`flex flex-col gap-1 ${className}`}>
      <p className={variantClasses[variant]}>{label}</p>
      {children}
    </div>
  )
}
