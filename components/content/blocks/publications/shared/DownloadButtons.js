import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import React from 'react'
import { Tooltip } from 'react-tooltip'
import Button from 'ui/buttons/Button'
import useDownloadCountIncrement from './useDownloadCountIncrement'

const Dropdown = dynamic(() => import('ui/buttons/Dropdown'))
const DropdownItem = dynamic(() =>
  import('ui/buttons/Dropdown').then(mod => mod.DropdownItem)
)
const Icon = dynamic(() => import('ui/icons/Icon'))

const iconMap = {
  pdfDigital: 'file-pdf',
  pdfPrinting: 'file-pdf',
  epub: 'file-epub',
  promotionalMaterials: 'grid',
  studyGuide: 'open-book',
  readingPlan: 'closed-book',
  mp3: 'headphones',
}

const downloadFields = [
  'pdfPrinting',
  'pdfDigital',
  'epub',
  'promotionalMaterials',
  'studyGuide',
  'readingPlan',
  'mp3',
]

export default function DownloadButtons({
  downloadLinks,
  variant = 'sharingHope',
  availableLanguages,
  currentLanguage,
  languageLabel = 'Language',
  publicationId,
  downloadLabel = 'Download',
  showPdfDigital = true,
  pdfDigitalLabel = 'PDF',
  pdfDigitalTooltip,
  showPdfPrinting = true,
  pdfPrintingLabel = 'PDF/X',
  pdfPrintingTooltip,
  showEpub = true,
  epubLabel = 'EPUB',
  epubTooltip,
  showPromotionalMaterials = false,
  promotionalMaterialsLabel,
  promotionalMaterialsTooltip,
  showStudyGuide = false,
  studyGuideLabel,
  studyGuideTooltip,
  showReadingPlan = false,
  readingPlanLabel,
  readingPlanTooltip,
  showAudio = false,
  audioLabel,
  audioTooltip,
  modalButtonLabel,
  modalName,
  modalButtonVariant = 'flat',
  downloadButtonVariant = 'secondary',
  languageButtonVariant = 'flat',
} = {}) {
  const showMap = {
    pdfDigital: showPdfDigital,
    pdfPrinting: showPdfPrinting,
    epub: showEpub,
    promotionalMaterials: showPromotionalMaterials,
    studyGuide: showStudyGuide,
    readingPlan: showReadingPlan,
    mp3: showAudio,
  }

  const labelMap = {
    epub: epubLabel || 'EPUB',
    pdfDigital: pdfDigitalLabel || 'PDF',
    pdfPrinting: pdfPrintingLabel || 'PDF/X',
    promotionalMaterials: promotionalMaterialsLabel || 'Promotional Materials',
    studyGuide: studyGuideLabel || 'Study Guide',
    readingPlan: readingPlanLabel || 'Reading Plan',
    mp3: audioLabel || 'Audio',
  }

  const tooltipMap = {
    pdfDigital: pdfDigitalTooltip,
    pdfPrinting: pdfPrintingTooltip,
    epub: epubTooltip,
    promotionalMaterials: promotionalMaterialsTooltip,
    studyGuide: studyGuideTooltip,
    readingPlan: readingPlanTooltip,
    mp3: audioTooltip,
  }

  // If current language is in the available languages, we set the selected locale to the current language, otherwise we set it to english
  const [selectedLocale, setSelectedLocale] = React.useState(
    downloadLinks.find(({ language }) => language === currentLanguage)
      ? currentLanguage
      : 'en'
  )

  // Get the original url object from the links array that matches the selected locale
  const originalLink = React.useMemo(
    () =>
      downloadLinks.filter(({ language }) => language === selectedLocale)[0] ||
      {},
    [downloadLinks, selectedLocale]
  )

  // we create an object with the downloads that are not undefined
  const downloads = downloadFields.reduce((acc, name) => {
    // If the original object has a value for the key and the showMap is true for the key, we add the key to the downloads object
    if (originalLink?.[name] && showMap[name] === true) {
      acc.push({
        name,
        label: labelMap[name],
        icon: iconMap[name],
        tooltip: tooltipMap[name],
        url: originalLink[name].url,
      })
    }
    return acc
  }, [])

  const handleDownloadCount = useDownloadCountIncrement()

  // we find the languages in the available languages matching those in links, and we return the one from available languages
  const dropdownLanguages = availableLanguages.filter(({ locale }) => {
    return downloadLinks
      .map(({ language }) => ({
        value: language,
        label: language,
      }))
      .find(({ value }) => value === locale)
  })

  const selectedLanguage = availableLanguages.find(
    ({ locale }) => locale === selectedLocale
  )

  if (dropdownLanguages.length === 0) {
    return null
  }

  if (variant === 'sharingHope') {
    return (
      <SharingHopeVariant
        languageLabel={languageLabel}
        dropdownLanguages={dropdownLanguages}
        downloads={downloads}
        handleDownloadCount={() =>
          handleDownloadCount({ language: selectedLocale, publicationId })
        }
        selectedLanguage={selectedLanguage}
        selectedLocale={selectedLocale}
        setSelectedLocale={setSelectedLocale}
      />
    )
  }

  if (variant === 'greatControversy') {
    return (
      <GreatControversyVariant
        languageLabel={languageLabel}
        dropdownLanguages={dropdownLanguages}
        downloads={downloads}
        handleDownloadCount={() =>
          handleDownloadCount({ language: selectedLocale, publicationId })
        }
        selectedLanguage={selectedLanguage}
        selectedLocale={selectedLocale}
        setSelectedLocale={setSelectedLocale}
        downloadLabel={downloadLabel}
        modalButtonLabel={modalButtonLabel}
        modalName={modalName}
        modalButtonVariant={modalButtonVariant}
        downloadButtonVariant={downloadButtonVariant}
        languageButtonVariant={languageButtonVariant}
      />
    )
  }

  // If no variant is found, we return null (no download buttons!)
  return null
}

function SharingHopeVariant({
  languageLabel = 'Language',
  dropdownLanguages,
  downloads,
  handleDownloadCount,
  selectedLanguage,
  selectedLocale,
  setSelectedLocale,
} = {}) {
  return (
    <div className="flex-col gap-2">
      <div className="flex flex-col items-start gap-5 lg:flex-row lg:items-center">
        <div className="flex">
          <Dropdown
            button={
              <div className="flex cursor-pointer items-center justify-center gap-2 rounded-lg border border-color1-500 bg-gray-50 px-4 py-3 font-semibold text-color1-800">
                <span>{languageLabel}:</span>
                <span className="text-color1-600">
                  {selectedLanguage?.nativeName ||
                    selectedLanguage?.name?.['en'] ||
                    selectedLanguage?.name}
                </span>
                <Icon name="chevron-down" className="text-sm text-color1-500" />
              </div>
            }
            name="language-download"
            itemsClass="min-w-[200px]"
            alignment={{ xs: 'bottom-end' }}
            variant="flat"
            color="none"
          >
            {dropdownLanguages.map(({ name, nativeName, locale }) => (
              <DropdownItem
                className="h-14"
                label={nativeName ? nativeName : name?.['en'] || name}
                help={locale !== 'en' ? name?.['en'] || name : ''}
                onClick={() => setSelectedLocale(locale)}
                selected={selectedLocale === locale}
                key={`language-switch-${locale}`}
              />
            ))}
          </Dropdown>
        </div>
        <div className="inline-flex">
          {downloads?.map(({ label, tooltip, name, url }) => {
            return (
              <PublicationDownloadLink
                data-tooltip-id={`download-${name}`}
                className="inline-flex border border-color1-700 bg-color1-600 px-4 py-3 font-semibold text-color1-100 transition-colors duration-200 ease-in-out first:rounded-s-lg last:rounded-e-lg hover:border-color1-800 hover:bg-color1-800 hover:text-white"
                onClick={handleDownloadCount}
                to={url}
                key={name}
              >
                {label}
                {tooltip && (
                  <Tooltip
                    id={`download-${name}`}
                    content={tooltip}
                    effect="solid"
                    place="top"
                  />
                )}
              </PublicationDownloadLink>
            )
          })}
        </div>
      </div>
    </div>
  )
}

function GreatControversyVariant({
  languageLabel = 'Language',
  dropdownLanguages,
  downloads,
  handleDownloadCount,
  selectedLanguage,
  selectedLocale,
  setSelectedLocale,
  downloadLabel = 'Download',
  modalButtonLabel = 'More Options',
  modalName = 'great-controversy-modal',
  modalButtonVariant = 'flat',
  downloadButtonVariant = 'secondary',
  languageButtonVariant = 'flat',
} = {}) {
  const router = useRouter()

  const handleModalClick = () => {
    // We add the modal to the url query
    router.push(
      {
        pathname: router.pathname,
        query: {
          ...router.query,
          modal: modalName,
        },
      },
      null,
      {
        shallow: true,
      }
    )
  }

  return (
    <div className="flex-col gap-2">
      <div className="flex flex-col items-start gap-5">
        <div className="flex items-center gap-4 align-middle">
          <p className="text-gray-600">{`${languageLabel}:`}</p>
          <Dropdown
            label={
              selectedLanguage?.nativeName ||
              selectedLanguage?.name?.['en'] ||
              selectedLanguage?.name
            }
            name="language-download"
            itemsClass="min-w-[200px]"
            alignment={{ xs: 'bottom-start' }}
            variant={languageButtonVariant}
            color="none"
          >
            {dropdownLanguages.map(({ name, nativeName, locale }) => (
              <DropdownItem
                className="h-14"
                label={nativeName ? nativeName : name?.['en'] || name}
                help={locale !== 'en' ? name?.['en'] || name : ''}
                onClick={() => setSelectedLocale(locale)}
                selected={selectedLocale === locale}
                key={`language-switch-${locale}`}
              />
            ))}
          </Dropdown>
        </div>
        <div className="inline-flex items-center gap-8">
          <Dropdown
            label={downloadLabel}
            labelClass={'pe-10'}
            name="language-download"
            itemsClass="min-w-[200px] rounded-lg"
            alignment={{ xs: 'bottom-start' }}
            variant={downloadButtonVariant}
          >
            {downloads.map(({ label, icon, url, name }) => {
              return (
                <DropdownItem
                  key={name}
                  href={url}
                  triggerOnClick
                  onClick={handleDownloadCount}
                  CustomLink={PublicationDownloadLink}
                  icon={icon}
                  label={label}
                />
              )
            })}
          </Dropdown>
          <Button
            variant={modalButtonVariant}
            label={modalButtonLabel}
            onClick={() => handleModalClick()}
          />
        </div>
      </div>
    </div>
  )
}

/**
 * Custom link for the download button. This link will download the file when clicked. It is used in the DropdownItem component, from wich it recieves props and styles.
 * @param {object} props
 * @param {string} props.className - The class name for the link
 * @param {React.ReactNode} props.children - The children of the link
 * @param {string} props.to - The href of the link
 * @param {function} props.onClick - The onClick function for the link
 * @returns {React.ReactElement}
 */
export function PublicationDownloadLink({ className, children, to, onClick }) {
  return (
    <a className={className} href={to} onClick={onClick} download>
      {children}
    </a>
  )
}
