import React from 'react'

export default function useDownloadCountIncrement() {
  return React.useCallback(async ({ language, publicationId }) => {
    try {
      // The api call to increment the count of downloads
      fetch('/api/publications/incrementPublicationCount', {
        method: 'post',
        body: JSON.stringify({ language, publicationId }),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      })
    } catch (error) {
      console.error('Error incrementing download count', error) // eslint-disable-line no-console
    }
  }, [])
}
