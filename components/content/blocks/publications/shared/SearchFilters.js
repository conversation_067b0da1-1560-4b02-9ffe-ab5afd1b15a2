import React from 'react'

import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { DropdownDivider } from 'ui/buttons/Dropdown'
import { usePageLoading } from 'ui/feedback/PageLoading'
const Dropdown = dynamic(() => import('ui/buttons/Dropdown'))
const DropdownItem = dynamic(() =>
  import('ui/buttons/Dropdown').then(m => m.DropdownItem)
)

export function SearchFilters({
  className,
  religions,
  categories,
  allLabel = 'All',
  filtersLabel = 'Filters',
  languages,
  religionFilterLabel = 'Religion',
  feltNeedFilterLabel = 'Felt-Need',
  doctrineFilterLabel = 'Doctrine',
  languageFilterLabel = 'Language',
  downloadLanguages = [],
}) {
  const router = useRouter()
  const { setPage } = usePageLoading()
  const { query, locale } = router
  const currentLocale = locale || 'en' // if nothing is selected then currentLocale is 'en'
  const { filterReligion, filterCategories, filterLanguage } = query

  // The available languages are those that are added to the site, and have a download available.
  const availableDownloadLanguages = React.useMemo(() => {
    return languages.filter(lang =>
      downloadLanguages.some(
        downloadLang => downloadLang.locale === lang.locale
      )
    )
  }, [languages, downloadLanguages])

  const selectedReligion = religions.find(item => item.name === filterReligion)
  const selectedLanguage = availableDownloadLanguages.find(
    item => item.locale === filterLanguage
  )

  const selectedCategories = React.useMemo(() => {
    // if there are no categories selected, return an empty object
    if (!filterCategories) return {}

    // Otherwise, parse the stringified object
    return JSON.parse(filterCategories)
  }, [filterCategories])

  const handleReligionChange = React.useCallback(
    selectedReligion => {
      // if the selected religion is the same as the current one, then set it to null
      if (selectedReligion === filterReligion || selectedReligion === 'all') {
        delete query.filterReligion
      } else {
        query.filterReligion = selectedReligion
      }

      router.push({
        pathname: router.pathname,
        query,
      })

      setPage(router.asPath)
    },
    [router, query, filterReligion, setPage]
  )

  const handleLanguageChange = React.useCallback(
    lang => {
      if (lang === filterLanguage || lang === 'all') {
        delete query.filterLanguage
      } else {
        query.filterLanguage = lang
      }

      router.push({
        pathname: router.pathname,
        query,
      })

      setPage(router.asPath)
    },
    [router, query, filterLanguage, setPage]
  )

  // Sets selectd categories when category change
  const handleCategoriesChange = React.useCallback(
    (type, categoryName) => {
      const selectedTypeCategories = selectedCategories?.[type] || []

      const categories = {
        ...selectedCategories,
      }

      if (categoryName === 'all') {
        delete categories[type]
      } else {
        const updatedTypeCategories = selectedTypeCategories.includes(
          categoryName
        )
          ? selectedTypeCategories.filter(item => item !== categoryName)
          : [...selectedTypeCategories, categoryName]

        categories[type] = updatedTypeCategories
      }

      query.filterCategories = JSON.stringify(categories)

      router.push({
        pathname: router.pathname,
        query,
      })

      setPage(router.asPath)
    },
    [query, router, selectedCategories, setPage]
  )

  return (
    <div className={`flex flex-col gap-1 ${className}`}>
      <div className="text-sm">{filtersLabel}</div>

      <div className="flex flex-row flex-wrap gap-2">
        <Dropdown label={selectedReligion?.title || religionFilterLabel}>
          {({ close }) => (
            <>
              <DropdownItem
                label={allLabel}
                onClick={() => {
                  close()
                  handleReligionChange('all')
                }}
                selected={!filterReligion}
                disabled={!filterReligion}
              />

              <DropdownDivider />

              {religions.map(item => (
                <DropdownItem
                  key={`religion-option-${item.name}`}
                  label={item.title}
                  onClick={() => {
                    close()
                    handleReligionChange(item.name)
                  }}
                  selected={item.name === filterReligion}
                />
              ))}
            </>
          )}
        </Dropdown>

        {Object.entries(categories).map(([type, items]) => (
          <Dropdown
            label={
              type === 'doctrine' ? doctrineFilterLabel : feltNeedFilterLabel
            }
            key={`category-type-${type}`}
            className="capitalize"
          >
            {({ close }) => (
              <>
                <DropdownItem
                  label={allLabel}
                  onClick={() => {
                    close()
                    handleCategoriesChange(type, 'all')
                  }}
                  selected={!selectedCategories?.[type]?.length}
                  disabled={!selectedCategories?.[type]?.length}
                />
                {items?.map(item => (
                  <DropdownItem
                    key={`category-${type}-${item.name}`}
                    label={item.title}
                    onClick={() => {
                      close()
                      handleCategoriesChange(type, item.name)
                    }}
                    selected={selectedCategories?.[type]?.includes(item.name)}
                  />
                ))}
              </>
            )}
          </Dropdown>
        ))}

        <Dropdown
          label={
            selectedLanguage?.locale === currentLocale
              ? selectedLanguage?.nativeName
              : selectedLanguage?.name?.[`${currentLocale}`] ||
                selectedLanguage?.name?.['en'] || // if there is a current locale different than 'en' but it doesn't have a translation, use the english name
                selectedLanguage?.name || // TODO: Remove this when all languages have translations
                languageFilterLabel
          }
        >
          {({ close }) => (
            <>
              <DropdownItem
                label={allLabel}
                onClick={() => {
                  close()
                  handleLanguageChange('all')
                }}
                selected={!filterLanguage}
                disabled={!filterLanguage}
              />

              <DropdownDivider />
              {availableDownloadLanguages.map(item => (
                <DropdownItem
                  key={`language-${item.locale}`}
                  label={
                    item.locale === currentLocale
                      ? item.nativeName
                      : item.name?.[`${currentLocale}`] ||
                        item.name?.['en'] ||
                        item.name // TODO: Remove this when all languages have translations
                  }
                  onClick={() => {
                    close()
                    handleLanguageChange(item.locale)
                  }}
                  selected={item.locale === filterLanguage}
                />
              ))}
            </>
          )}
        </Dropdown>
      </div>
    </div>
  )
}
