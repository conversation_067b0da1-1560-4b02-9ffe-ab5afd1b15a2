import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'

import {
  offset,
  shift,
  useDismiss,
  useFloating,
  useInteractions,
  useTransitionStyles,
} from '@floating-ui/react'
import { usePublicationSearch } from 'hooks/usePublicationSearch'

import { SearchFilters } from './shared/SearchFilters'
import { useDebounceSearch } from 'utils/useDebounce'
import { useRouter } from 'next/router'
import { useAvailableLanguages } from 'hooks/useAvailableLanguages'

const Button = dynamic(() => import('ui/buttons/Button'))
const Image = dynamic(() => import('ui/data-display/Image'))
const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))

const variantsMap = {
  simple: PublicationSearchSimple,
  dropdown: PublicationSearchDropdown,
}

export default function PublicationsSearch({
  publicationsSearch = { items: [] },
  label,
  id,
  placeholder,
  className,
  alignment = 'bottom-start',
  variant,
  showAllLink,
  showAllLabel,
  showImages = true,
  showFilters = true,
  limit,
  detailPageId,
  pageData,
  filtersLabel,
  filterAllLabel,
  religionFilterLabel,
  feltNeedFilterLabel,
  doctrineFilterLabel,
  languageFilterLabel,
  noResultsLabel,
}) {
  const router = useRouter()

  const {
    search: searchTerm,
    filterReligion,
    filterLanguage,
    filterCategories,
  } = router.query

  const ComponentMenu = variantsMap[variant] || variantsMap.simple
  const { items, religions, categories, publicationsUrl, downloadLanguages } =
    publicationsSearch || {}

  const { availableLanguages, language } = pageData

  const languages = useAvailableLanguages({ availableLanguages })

  const { data: searchResults, isLoading } = usePublicationSearch({
    term: searchTerm,
    language,
    filterLanguage,
    categories: filterCategories ? JSON.parse(filterCategories) : {},
    religion: filterReligion,
    limit: limit ?? 4,
    detailPageId,
    enabled:
      !!searchTerm ||
      !!filterReligion ||
      !!filterCategories?.length ||
      !!filterLanguage,
  })

  return (
    <ComponentMenu
      items={searchResults?.results || (isLoading ? [] : items || [])}
      religions={religions || []}
      categories={categories || []}
      languages={languages || []}
      label={label}
      id={id}
      className={className}
      alignment={alignment}
      showFilters={showFilters}
      showImages={showImages}
      placeholder={placeholder}
      showAllLink={showAllLink}
      showAllLabel={showAllLabel}
      isLoading={isLoading}
      publicationsUrl={publicationsUrl}
      filtersLabel={filtersLabel}
      filterAllLabel={filterAllLabel}
      religionFilterLabel={religionFilterLabel}
      feltNeedFilterLabel={feltNeedFilterLabel}
      doctrineFilterLabel={doctrineFilterLabel}
      languageFilterLabel={languageFilterLabel}
      downloadLanguages={downloadLanguages}
      noResultsLabel={noResultsLabel}
    />
  )
}
PublicationsSearch.propTypes = {
  alignment: PropTypes.string,
  publicationsSearch: PropTypes.shape({
    items: PropTypes.array,
    religions: PropTypes.array,
    categories: PropTypes.object, //
    publicationsUrl: PropTypes.string,
    downloadLanguages: PropTypes.array,
  }),
  label: PropTypes.string,
  limit: PropTypes.number,
  showImages: PropTypes.bool,
}

function PublicationSearchSimple({ id, className, placeholder }) {
  return (
    <div className={`flex.flex-col ${className}`} id={id}>
      <SearchField placeholder={placeholder} />
    </div>
  )
}

const alignmentsStyles = {
  'bottom-start': 'start-0 origin-bottom-left',
  'bottom-end': 'end-0 origin-bottom-right',
  'top-start': 'bottom-12 start-0 origin-top-left',
  'top-end': 'bottom-12 end-0 origin-top-right',
}

function PublicationSearchDropdown({
  className,
  alignment = 'bottom-start',
  placeholder,
  id,
  showFilters,
  showImages,
  showAllLink,
  showAllLabel,
  publicationsUrl,
  isLoading,
  items,
  religions,
  categories,
  languages,
  onFiltersChange,
  noResultsLabel = 'No results found',
  filtersLabel,
  filterAllLabel,
  religionFilterLabel,
  feltNeedFilterLabel,
  doctrineFilterLabel,
  languageFilterLabel,
  downloadLanguages,
}) {
  const [isOpen, setIsOpen] = React.useState(false)

  const alignmentClass =
    alignmentsStyles[alignment] ?? alignmentsStyles['bottom-start']
  const fromBottom = alignment?.startsWith('bottom')

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    placement: 'bottom-start',
    middleware: [shift(), offset({ mainAxis: 16 })],
  })
  const { isMounted, styles } = useTransitionStyles(context, {
    initial: {
      opacity: 0,
      transform: fromBottom ? 'translateY(8px)' : 'translateY(-4px)',
    },
    open: {
      opacity: 1,
      transform: 'translateY(0)',
    },
  })

  const toggle = React.useCallback(() => {
    setIsOpen(prevIsOpen => !prevIsOpen)
  }, [])

  const close = React.useCallback(() => {
    setIsOpen(false)
  }, [])

  const dismiss = useDismiss(context)

  const { getReferenceProps, getFloatingProps } = useInteractions([dismiss])

  return (
    <div
      className={`flex flex-col ${className}`}
      id={id}
      ref={refs.setReference}
      {...getReferenceProps()}
    >
      <SearchField
        className="hidden md:flex"
        placeholder={placeholder}
        onClick={toggle}
      />

      <Button
        className="md:hidden"
        variant="flat"
        color="none"
        icon="search"
        onClick={() => setIsOpen(true)}
      />

      {isMounted && (
        <div
          className={`z-max ${alignmentClass}`}
          {...getFloatingProps()}
          ref={refs.setFloating}
          style={floatingStyles}
        >
          <div
            className="flex h-screen flex-col gap-4 border-gray-100 bg-white p-4 focus:outline-none md:fixed md:h-auto md:min-w-[640px] md:max-w-xl md:rounded-lg md:border md:p-6 md:shadow-lg md:drop-shadow-md"
            style={styles}
          >
            <div className="flex gap-4 md:hidden">
              <SearchField placeholder={placeholder} className="flex-grow" />

              <Button
                variant="flat"
                color="none"
                icon="times"
                onClick={close}
              />
            </div>

            {showFilters && (
              <SearchFilters
                onFiltersChange={onFiltersChange}
                religions={religions}
                categories={categories}
                languages={languages}
                filtersLabel={filtersLabel}
                allLabel={filterAllLabel}
                religionFilterLabel={religionFilterLabel}
                feltNeedFilterLabel={feltNeedFilterLabel}
                doctrineFilterLabel={doctrineFilterLabel}
                languageFilterLabel={languageFilterLabel}
                downloadLanguages={downloadLanguages}
                onClose={close}
              />
            )}

            <div className="-mx-3 flex-grow overflow-y-auto no-scrollbar md:grow-0">
              {isLoading && (
                <div className="flex items-center justify-center">
                  <Icon name="spinner" className="animate-spin" />
                </div>
              )}

              {!items?.length && !isLoading && (
                <div className="flex min-h-[20ch] items-center justify-center">
                  <p className="text-center text-gray-500">{noResultsLabel}</p>
                </div>
              )}

              <div className="grid grid-cols-2 md:grid-cols-4 md:overflow-x-scroll md:no-scrollbar">
                {items?.map(({ title, slug, religion, cover, url }) => (
                  <Link
                    className="flex flex-col items-center gap-2 rounded-md p-3 transition-colors duration-300 ease-out hover:bg-gray-50"
                    key={`publication-${religion}-${slug}`}
                    to={url}
                  >
                    {showImages && (
                      <div className="flex items-center justify-center">
                        <Image
                          className="rounded-md"
                          file={cover.file}
                          alt={title || slug}
                          priority
                          lazy={false}
                          sizes="200px"
                        />
                      </div>
                    )}
                    <div className="text-center text-sm font-semibold leading-4">
                      {title}
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {showAllLink && showAllLabel && publicationsUrl && (
              <div className="flex items-center justify-center border-t px-2 pt-4 md:-mb-2">
                <Link
                  to={publicationsUrl}
                  className="group flex items-center justify-center gap-2 text-color1-600"
                  onClick={close}
                >
                  <span className=" font-semibold uppercase tracking-tight">
                    {showAllLabel}
                  </span>
                  <Icon
                    name="chevron-right"
                    className="-mt-px transition-transform duration-200 ease-in-out group-hover:translate-x-1"
                  />
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

function SearchField({
  className,
  onClick,
  placeholder = 'Search publications...',
}) {
  const router = useRouter()
  const { query } = router

  // Debounce search term to avoid triggering a new API request on every keystroke.
  const [searchTerm, handleSearch, debouncedTerm] = useDebounceSearch({
    initialValue: query.search, // Use the search query from the URL as initial value.
    delay: 300, // Wait 300ms before triggering a new search.
    callback: () => {
      // Update the URL "search" query with the new search term.
      if (debouncedTerm) {
        query.search = debouncedTerm
      } else {
        // Or remove it if the search term is empty.
        delete query.search
      }

      router.push(
        {
          pathname: router.pathname,
          query,
        },
        null // Using null here to avoid a full reload of the page.
      )
    },
  })

  return (
    <div
      className={`flex items-center gap-2 rounded-lg border border-color1-300 p-2 focus-within:bg-color1-50 hover:bg-color1-50 ${className}`}
    >
      <Icon name="search" className="text-color1-300" />
      <input
        className="w-64 bg-transparent text-color1-700 placeholder:text-gray-300 focus-visible:outline-none"
        placeholder={placeholder}
        onChange={handleSearch}
        value={searchTerm}
        onClick={onClick}
      />
    </div>
  )
}
SearchField.propTypes = {
  className: PropTypes.string,
  onClick: PropTypes.func,
  placeholder: PropTypes.string,
}
