import PropTypes from 'prop-types'

import clsx from 'clsx'
import dynamic from 'next/dynamic'

import { getTextColor } from 'ui/helpers/getColor'
import useBorderRadius from 'ui/helpers/useBorderRadius'
import useFieldResource from 'ui/helpers/useFieldResource'
import { useHeight } from 'ui/helpers/useHeight'
import useWidth from 'ui/helpers/useWidth'
import Link from 'ui/navigation/Link'

const Picture = dynamic(() => import('ui/data-display/Picture'))
const SvgInline = dynamic(() => import('ui/data-display/SvgInline'))

export default function Image({
  aspectRatio,
  borderRadius,
  className,
  color,
  file,
  height,
  source,
  svgInline,
  url,
  urlSource,
  width = { xs: 'full' },
  ...props
}) {
  const widthClass = useWidth(width)
  const heightStyle = useHeight(height)
  const svgColor = getTextColor(color)
  const borderRadiusClasses = useBorderRadius(borderRadius)

  const imageFile = useFieldResource(source, file)
  const urlValue = useFieldResource(urlSource, url)

  if (svgInline && file?.extension === '.svg') {
    return urlValue ? (
      <Link
        to={urlValue}
        className={clsx(widthClass, borderRadiusClasses, className)}
      >
        <SvgInline
          className={clsx(svgColor)}
          file={imageFile}
          style={heightStyle}
          url={urlValue}
        />
      </Link>
    ) : (
      <SvgInline
        className={clsx(widthClass, borderRadiusClasses, svgColor, className)}
        file={imageFile}
        style={heightStyle}
        url={urlValue}
      />
    )
  }

  return (
    <Picture
      {...props}
      aspectRatio={aspectRatio}
      borderRadius={borderRadius}
      className={clsx(widthClass, className)}
      file={imageFile}
      hasCustomWidth
      style={heightStyle}
      url={urlValue}
    />
  )
}
Image.propTypes = {
  aspectRatio: PropTypes.oneOf(['auto', '1/1', '3/4', '4/3', '16/9']),
  borderRadius: PropTypes.object,
  className: PropTypes.string,
  color: PropTypes.string,
  file: PropTypes.object,
  height: PropTypes.object,
  svgInline: PropTypes.bool,
  url: PropTypes.string,
  urlFromSource: PropTypes.bool,
  width: PropTypes.object,
}
