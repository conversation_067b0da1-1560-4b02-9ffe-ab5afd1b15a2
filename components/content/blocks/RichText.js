import React from 'react'
import PropTypes from 'prop-types'

import useWidth from 'ui/helpers/useWidth'
import RichText from 'ui/typography/RichText'
import useFieldResource from 'ui/helpers/useFieldResource'

export default function RichTextBlock({
  className = '',
  doc,
  source,
  width,
  ...rest
}) {
  const widthClasses = useWidth(width)
  const displayedDoc = useFieldResource(source, doc)

  return (
    <RichText
      {...rest}
      className={`${widthClasses} ${className} `}
      doc={displayedDoc}
    />
  )
}
RichTextBlock.propTypes = {
  className: PropTypes.string,
  doc: PropTypes.object,
  source: PropTypes.object,
  width: PropTypes.object,
}
