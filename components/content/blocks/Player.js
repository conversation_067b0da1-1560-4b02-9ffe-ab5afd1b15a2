import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import clsx from 'clsx'
import useWidth from 'ui/helpers/useWidth'
import { getImageUrl } from 'utils/images'

const UIPlayer = dynamic(() => import('ui/data-display/Player'))

export default function Player({
  // id,
  className,
  link,
  provider,
  poster,
  alt,
  playIcon,
  playIconSize,
  playIconColor,
  playIconHoverColor,
  playIconText,
  variant,
  ctaLabel,
  ctaVariant,
  ctaSize,
  width,
  pageData,
}) {
  const widthClasses = useWidth(width)
  const posterSrc = poster ? getImageUrl(poster) : undefined

  if (!link || !provider) return null

  return (
    <UIPlayer
      className={clsx(widthClasses, className)}
      key={link}
      provider={provider}
      poster={posterSrc}
      alt={alt}
      showPosterOverlay={Boolean(poster)}
      playIcon={playIcon}
      playIconSize={playIconSize}
      playIconColor={playIconColor}
      playIconHoverColor={playIconHoverColor}
      playIconText={playIconText}
      ctaLabel={ctaLabel}
      ctaVariant={ctaVariant}
      ctaSize={ctaSize}
      id={link}
      variant={variant}
      pageData={pageData}
    />
  )
}
Player.propTypes = {
  id: PropTypes.string,
  className: PropTypes.string,
  link: PropTypes.string,
  provider: PropTypes.oneOf([
    'youtube',
    'jetstream',
    'jetstream-live',
    'vimeo',
    'soundcloud',
    'hls',
    'mp4-hd',
    'mp4-sd',
  ]),
  poster: PropTypes.object,
  playIcon: PropTypes.object,
  playIconSize: PropTypes.object,
  playIconColor: PropTypes.string,
  playIconHoverColor: PropTypes.string,
  playIconText: PropTypes.string,
  variant: PropTypes.string,
  ctaLabel: PropTypes.string,
  ctaVariant: PropTypes.string,
  ctaSize: PropTypes.string,
  width: PropTypes.object,
}
