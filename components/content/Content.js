import { useContentClasses } from 'ui/helpers/useContentClasses'

import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'

import blocks from './blocks'

/**
 * Component to render the page content of web modules
 * @param {Object} props
 * @param {String} props.id Content id
 * @param {Object} props.page Current page object
 * @param {String} props.className Additional class to apply to the content
 * @returns {React.ReactElement}
 */
export default function Content({
  id = 'ROOT',
  page = {},
  className = '',
  ...otherProps
}) {
  const { content = {}, pageData = {} } = page || {}
  const node = content[id]

  const contentClass = useContentClasses(
    node?.props,
    content[node?.parent],
    className
  )

  const visibilityValue = useValueAtBreakpoint(
    node?.props?.visibility,
    'visible'
  )

  if (!node || node?.hidden || visibilityValue === 'hidden') {
    return null
  }

  const NodeRender = blocks[node.displayName]
  if (!NodeRender) {
    console.warn('Missing node render', node.displayName) // eslint-disable-line no-console
    return null
  }

  const props = {
    ...node.props,
    ...otherProps, // Include other props passed to the Content component directly (like when a parent component passes a prop to it's children, or with React.Children.map)
    pageData,
    blockId: id,
    className: contentClass,
  }

  for (const [nodeKey, nodeId] of Object.entries(node.linkedNodes)) {
    props[nodeKey] = <Content id={nodeId} key={nodeId} page={page} />
  }

  return (
    <NodeRender {...props} key={id}>
      {node.nodes.length > 0 &&
        node.nodes.map(childNodeId => (
          <Content id={childNodeId} key={childNodeId} page={page} />
        ))}
    </NodeRender>
  )
}
