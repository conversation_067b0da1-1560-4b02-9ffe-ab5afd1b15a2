import PropTypes from 'prop-types'

import { NextSeo } from 'next-seo'
import { getImageUrl } from 'utils/images'

import {
  parseImageResource,
  parsePageCanonicalUrl,
  parseTextResource,
} from './hooks'

export default function PageMeta({ page }) {
  const { site, resources, pageData, seo, language } = page
  const { title, description, keywords, absoluteUrl } = pageData || {}
  const { twitter, openGraph, canonicalUrl: seoCanonicalUrl } = seo || {}

  const locale = language || site.language || 'en'

  const ogTitle =
    parseTextResource(openGraph?.title, resources, locale) || title
  const ogDescription =
    parseTextResource(openGraph?.description, resources, locale) || description
  const ogImage = parseImageResource(openGraph?.image, resources)
  const canonicalUrl = parsePageCanonicalUrl(resources, seoCanonicalUrl)

  return (
    <NextSeo
      title={title}
      titleTemplate={`%s | ${site.title}`}
      defaultTitle={site.title}
      description={description}
      canonical={canonicalUrl ?? absoluteUrl}
      noindex={page.noIndex}
      nofollow={page.noFollow}
      additionalMetaTags={[{ name: 'keywords', content: keywords.join(',') }]}
      openGraph={{
        title: ogTitle || site.title,
        description: ogDescription,
        images: ogImage
          ? [
              {
                url: getImageUrl(ogImage, 'w:1200,h:630'),
                width: 1200,
                height: 630,
                alt: ogTitle,
              },
            ]
          : null,
        url: page.pageData.absoluteUrl,
      }}
      twitter={{
        handle: twitter?.creator || site.seo?.twitter?.creator,
        site: twitter?.site || site.seo?.twitter?.site,
        cardType: twitter?.cardType || site.seo?.twitter?.cardType || 'summary',
      }}
    />
  )
}
PageMeta.propTypes = {
  page: PropTypes.object,
}
