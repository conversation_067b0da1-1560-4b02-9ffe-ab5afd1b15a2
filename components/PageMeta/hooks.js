import get from 'lodash/get'
import uniq from 'lodash/uniq'
import isValid from 'date-fns/isValid'
import parse from 'date-fns/parse'

import { formatDate } from 'utils/datetime'

export function getPageMeta(page, locale) {
  if (!page) return null
  const { description, isHome, keywords, resources, site, title } = page

  return {
    title: isHome ? null : parseTextResource(title, resources, locale),
    description:
      parseTextResource(description, resources, locale) || site?.description,
    keywords: uniq([
      ...parseArrayResource(keywords, resources),
      ...[site?.keywords || []],
    ]),
  }
}

export function parsePageCanonicalUrl(resources, fallbackCanonicalUrl) {
  if (resources?.Article?.canonicalUrl) {
    return resources?.Article?.canonicalUrl
  }
  return fallbackCanonicalUrl
}

export function parseTextResource(text = '', resources = {}, locale) {
  return text.replace(/\{\w+\.?\w+(\.?\w+){0,}\}/g, (placholder = '') => {
    const [resourceName, ...fields] = placholder.replace(/[{}]/g, '').split('.')
    const resource = resources[resourceName] || {}
    if (['year', 'month', 'day'].includes(resourceName)) {
      return parseDateResource(resourceName, resource, locale)
    }
    if (resourceName === 'flag') {
      return parseFlagResource(resource, resources)
    }
    return get(resource, fields.join('.')) || ''
  })
}

function parseFlagResource(resource, resources) {
  return resources.site?.flags?.find(f => f.id === resource)?.name
}

function parseDateResource(resourceName, resource, locale) {
  if (resourceName === 'year') {
    const parsedYear = parse(resource, 'yyyy', new Date())
    if (isValid(parsedYear)) {
      return resource
    }
    return ''
  }
  if (resourceName === 'month') {
    const parsedMonth = parse(resource, 'MM', new Date())
    if (isValid(parsedMonth)) {
      return formatDate(parsedMonth, 'MMMM', { locale })
    }
    return ''
  }
  if (resourceName === 'day') {
    const parsedDay = parse(resource, 'dd', new Date(2023, 0, 1))
    if (isValid(parsedDay)) {
      return parseInt(resource, 10)
    }
    return ''
  }
  return parseInt(resource, 10)
}

function parseArrayResource(array = [], resources = {}) {
  const newArray = []

  for (const item of array) {
    if (item.match(/\{\w+\.\w+(\.\w+){0,}\}/)) {
      const [resourceName, ...fields] = item.replace(/[{}]/g, '').split('.')
      const resource = resources[resourceName] || {}
      newArray.push(...(get(resource, fields.join('.')) || []))
    } else {
      newArray.push(item)
    }
  }

  return newArray
}

export function parseImageResource(file, resources = {}) {
  if (!file || typeof file !== 'object') return null

  if (file.resource && file.field) {
    const resource = resources[file.resource] || {}
    return get(resource, file.field)
  } else {
    return file
  }
}
