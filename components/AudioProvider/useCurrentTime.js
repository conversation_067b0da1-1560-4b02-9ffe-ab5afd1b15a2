import { useCallback, useEffect, useState } from 'react'
import { useAudio } from './AudioProvider'

export function useCurrentTime() {
  const [currentTime, setCurrentTime] = useState(0)
  const { subscribeTimeUpdated, unsubscribeTimeUpdated } = useAudio()

  const onTimeUpdate = useCallback(t => {
    setCurrentTime(t)
  }, [])

  useEffect(() => {
    subscribeTimeUpdated(onTimeUpdate)
    return () => {
      unsubscribeTimeUpdated(onTimeUpdate)
    }
  }, [onTimeUpdate, subscribeTimeUpdated, unsubscribeTimeUpdated])

  return currentTime
}
