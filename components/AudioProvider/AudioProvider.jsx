import React, { useEffect, useRef } from 'react'
import PropTypes from 'prop-types'

import { useAudioContextValue } from './useAudioContextValue'
import { usePrevious } from 'hooks/usePrevious'
import { AudioContext } from './useAudio'

export function AudioProvider({ children, sources }) {
  const audioRef = useRef(null)
  const prevSources = usePrevious(sources)

  const audioContext = useAudioContextValue(audioRef)

  useEffect(() => {
    if (sources !== prevSources) {
      audioContext.load()
    }
  }, [audioContext, prevSources, sources])

  return (
    <AudioContext.Provider value={audioContext}>
      {/* eslint-disable-next-line jsx-a11y/media-has-caption */}
      <audio ref={audioRef} autoPlay={false} loop={false}>
        {sources.map(({ src, type }, index) => (
          <source key={`audio-source-${src}-${index}`} src={src} type={type} />
        ))}
      </audio>
      {children}
    </AudioContext.Provider>
  )
}

AudioProvider.propTypes = {
  children: PropTypes.node,
  sources: PropTypes.arrayOf(
    PropTypes.shape({
      src: PropTypes.string,
      type: PropTypes.string,
    })
  ),
}
