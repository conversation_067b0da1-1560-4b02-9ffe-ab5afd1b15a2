import { useCallback, useEffect, useState } from 'react'

export function useAudioContextValue(audioRef) {
  const [state, setState] = useState('INITIAL')
  //   const [currentTime, setCurrentTime] = useState(0)
  const [currentTimeSubscribers, setCurrentTimeSubscribers] = useState([])
  const [duration, setDuration] = useState(0)
  const [progress, setProgress] = useState(0)
  const [canPlay, setCanPlay] = useState(false)

  const [volume, setVolume] = useState(1)
  const [rate, setRate] = useState(1)

  const play = useCallback(async () => {
    try {
      await audioRef.current?.play()
    } catch (err) {
      setState('ERROR')
    }
  }, [audioRef])

  const pause = useCallback(() => {
    audioRef.current?.pause()
  }, [audioRef])

  const seek = useCallback(
    percent => {
      if (!audioRef.current) {
        return
      }
      audioRef.current.currentTime = audioRef.current.duration * percent
    },
    [audioRef]
  )

  const seekToOffset = useCallback(
    offset => {
      if (!audioRef.current) {
        return
      }
      const d = audioRef.current.duration
      const seconds = audioRef.current.currentTime + offset

      if (seconds > d) {
        audioRef.current.seek(d)
      }
      if (seconds < 0) {
        audioRef.current.seek(0)
      }
      audioRef.current.currentTime = seconds
    },
    [audioRef]
  )

  const onSetVolume = useCallback(
    value => {
      if (!audioRef.current) {
        return
      }
      audioRef.current.volume = value
    },
    [audioRef]
  )

  const onSetRate = useCallback(
    value => {
      if (!audioRef.current) {
        return
      }
      audioRef.current.playbackRate = value
    },
    [audioRef]
  )

  const load = useCallback(() => {
    audioRef.current.load()
  }, [audioRef])

  const subscribeTimeUpdated = useCallback(fn => {
    setCurrentTimeSubscribers(currentSubscribers => {
      return [...currentSubscribers, fn]
    })
  }, [])

  const unsubscribeTimeUpdated = useCallback(fn => {
    setCurrentTimeSubscribers(currentSubscribers => {
      return currentSubscribers.filter(f => fn !== f)
    })
  }, [])

  //   Event listeners
  const onError = useCallback(() => {
    setState('ERROR')
  }, [])

  const onLoadStart = useCallback(() => {
    setState('LOADSTARTED')
  }, [])

  const onLoadedMetaData = useCallback(() => {
    setDuration(audioRef.current?.duration ?? 0)
    setState('LOADEDMETADATA')
  }, [audioRef])

  const onLoadProgress = useCallback(() => {
    const duration = audioRef.current?.duration
    const audio = audioRef.current

    if (duration > 0) {
      for (let i = 0; i < audio?.buffered?.length; i++) {
        if (
          audio?.buffered?.start(audio?.buffered?.length - 1 - i) <
          audio?.currentTime
        ) {
          setProgress(
            (audio?.buffered?.end(audio?.buffered?.length - 1 - i) * 100) /
              duration
          )
          break
        }
      }
    }
  }, [audioRef])

  const onPlaying = useCallback(() => {
    setState('PLAYING')
  }, [])

  const onTimeUpdate = useCallback(() => {
    currentTimeSubscribers.forEach(fn => {
      fn(audioRef.current?.currentTime ?? 0)
    })
  }, [audioRef, currentTimeSubscribers])

  const onSeeked = useCallback(() => {
    setState(audioRef.current.paused ? 'PAUSED' : 'PLAYING')
  }, [audioRef])

  const onPause = useCallback(() => {
    setState('PAUSED')
  }, [])

  const onWaiting = useCallback(() => {
    setState('WAITING')
  }, [])

  const onCanPlay = useCallback(() => {
    setCanPlay(true)
  }, [])

  const onCanPlayThrough = useCallback(() => {
    setCanPlay(true)
  }, [])

  const onVolumeChange = useCallback(() => {
    setVolume(audioRef.current.volume)
  }, [audioRef])

  const onRateChange = useCallback(() => {
    setRate(audioRef.current.playbackRate)
  }, [audioRef])

  const onEnded = useCallback(() => {
    setState('ENDED')
  }, [])

  const onEmptied = useCallback(() => {
    setState('EMPTIED')
  }, [])

  useEffect(() => {
    const audio = audioRef.current

    if (audio) {
      // Hook up events
      audio.addEventListener('error', onError)
      audio.addEventListener('loadstart', onLoadStart)
      audio.addEventListener('loadedmetadata', onLoadedMetaData)
      audio.addEventListener('progress', onLoadProgress)
      audio.addEventListener('playing', onPlaying)
      audio.addEventListener('timeupdate', onTimeUpdate)
      audio.addEventListener('seeked', onSeeked)
      audio.addEventListener('pause', onPause)
      audio.addEventListener('waiting', onWaiting)
      audio.addEventListener('canplay', onCanPlay)
      audio.addEventListener('canplaythrough', onCanPlayThrough)
      audio.addEventListener('volumechange', onVolumeChange)
      audio.addEventListener('ratechange', onRateChange)
      audio.addEventListener('ended', onEnded)
      audio.addEventListener('emptied', onEmptied)
    }
    return () => {
      audio?.removeEventListener('error', onError)
      audio?.removeEventListener('loadstart', onLoadStart)
      audio?.removeEventListener('loadedmetadata', onLoadedMetaData)
      audio?.removeEventListener('progress', onLoadProgress)
      audio?.removeEventListener('playing', onPlaying)
      audio?.removeEventListener('timeupdate', onTimeUpdate)
      audio?.removeEventListener('seeked', onSeeked)
      audio?.removeEventListener('pause', onPause)
      audio?.removeEventListener('waiting', onWaiting)
      audio?.removeEventListener('canplay', onCanPlay)
      audio?.removeEventListener('canplaythrough', onCanPlayThrough)
      audio?.removeEventListener('volumechange', onVolumeChange)
      audio?.removeEventListener('ratechange', onRateChange)
      audio?.removeEventListener('ended', onEnded)
      audio?.removeEventListener('emptied', onEmptied)
    }
  }, [
    audioRef,
    onCanPlay,
    onCanPlayThrough,
    onEmptied,
    onEnded,
    onError,
    onLoadProgress,
    onLoadStart,
    onLoadedMetaData,
    onPause,
    onPlaying,
    onRateChange,
    onSeeked,
    onTimeUpdate,
    onVolumeChange,
    onWaiting,
  ])

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.load()
    }
  }, [audioRef])

  return {
    state,
    duration,
    progress,
    rate,
    volume,
    canPlay,

    play,
    pause,
    seek,
    seekToOffset,
    setVolume: onSetVolume,
    setRate: onSetRate,
    load,
    subscribeTimeUpdated,
    unsubscribeTimeUpdated,
  }
}
