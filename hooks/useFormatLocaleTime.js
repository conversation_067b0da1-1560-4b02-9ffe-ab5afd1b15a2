import {
  defaultTimeFormatOptions,
  formatTime,
  timeFormat,
  useDatetimeLocale,
} from 'utils/datetime'

/**
 * Format the time from seconds to H:M:SS. Other formats support through custom formatter fn.
 * @param  {Function} options formatter function options, e.g. `options` supported by `formatDuration` from `date-fns`
 * @return {Function} `datatime/formatTime` function with current locale
 */
export function useFormatLocaleTime(options = {}) {
  const locale = useDatetimeLocale()
  return function (seconds, formatter = timeFormat) {
    return formatTime(seconds, formatter, {
      ...defaultTimeFormatOptions,
      locale,
      ...options,
    })
  }
}
