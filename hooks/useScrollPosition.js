// Extracted from https://dev.to/gabe_ragland/debouncing-with-react-hooks-jci

import { useState, useEffect } from 'react'
import { debounce } from 'utils/func'

export default function useScrollPosition(ref) {
  const [scrollPosition, setScrollPosition] = useState(0)

  useEffect(() => {
    const scroll = debounce(() => {
      setScrollPosition(window.scrollY)
    }, 10)

    if (ref.current) {
      window.addEventListener('scroll', scroll)
    }

    return () => {
      window.removeEventListener('scroll', scroll)
    }
  }, [ref])

  return scrollPosition
}
