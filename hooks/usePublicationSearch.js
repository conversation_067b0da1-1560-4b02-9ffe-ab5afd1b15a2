import { useQuery } from 'react-query'
import { getRequest } from 'utils/fetcher'

const defaultOptions = {
  term: '',
  religion: null,
  language: 'en',
  categories: {},
  limit: 4,
  enabled: true,
  detailPageId: null,
}

export function usePublicationSearch(options = defaultOptions) {
  const {
    term,
    religion,
    categories,
    filterLanguage,
    language,
    limit,
    enabled,
    detailPageId,
  } = Object.assign({}, defaultOptions, options)

  return useQuery(
    [
      'PublicationsSearch',
      term,
      religion,
      filterLanguage,
      language,
      limit,
      categories,
      enabled,
      detailPageId,
    ],
    () =>
      getRequest('/publications/search', {
        params: {
          term,
          religion,
          categories,
          filterLanguage,
          language,
          limit,
          detailPageId,
        },
      }),
    {
      enabled,
      refetchOnWindowFocus: false, // Do not refetch on window focus to avoid unwanted refetches.
    }
  )
}
