import { useState, useEffect, useCallback } from 'react'
import {
  getAllVideos,
  deleteVideo as deleteVideoFromDB,
  getStorageUsage as getStorageUsageFromDB,
  clearAllVideos,
} from '../utils/videoStorage'

/**
 * Custom hook for managing video storage
 * @param {Function} t - Translation function
 */
export default function useVideoStorage(t) {
  const [videos, setVideos] = useState([])
  const [storageUsage, setStorageUsage] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load videos from IndexedDB
  const loadVideos = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if IndexedDB is available
      if (typeof window === 'undefined' || !window.indexedDB) {
        throw new Error(t('indexedDbNotAvailable'))
      }

      const storedVideos = await getAllVideos()
      setVideos(storedVideos)

      const usage = await getStorageUsageFromDB()
      setStorageUsage(usage)
    } catch (err) {
      const errorMessage = err.message || t('failedToLoadVideos')
      setError(errorMessage)

      // Set empty state on error to prevent crashes, but still functional
      setVideos([])
      setStorageUsage({
        used: 0,
        count: 0,
        quota: null,
        totalUsage: 0,
        available: null,
        videos: [],
      })
    } finally {
      setLoading(false)
    }
  }, [t])

  // Delete a video
  const deleteVideo = useCallback(
    async videoId => {
      try {
        await deleteVideoFromDB(videoId)

        // Update local state
        setVideos(prevVideos => {
          const updatedVideos = prevVideos.filter(video => video.id !== videoId)

          // Clean up blob URLs to prevent memory leaks
          const deletedVideo = prevVideos.find(video => video.id === videoId)
          if (deletedVideo) {
            if (deletedVideo.blobUrl) {
              URL.revokeObjectURL(deletedVideo.blobUrl)
            }
            if (deletedVideo.thumbnailBlobUrl) {
              URL.revokeObjectURL(deletedVideo.thumbnailBlobUrl)
            }
          }

          return updatedVideos
        })

        // Force refresh of storage usage including browser totals
        await loadVideos()
      } catch (err) {
        setError(err.message)
        throw err
      }
    },
    [loadVideos]
  )

  // Clear all videos
  const clearAll = useCallback(async () => {
    try {
      await clearAllVideos()

      // Clean up blob URLs
      videos.forEach(video => {
        if (video.blobUrl) {
          URL.revokeObjectURL(video.blobUrl)
        }
        if (video.thumbnailBlobUrl) {
          URL.revokeObjectURL(video.thumbnailBlobUrl)
        }
      })

      // Force refresh of storage usage including browser totals
      await loadVideos()
    } catch (err) {
      setError(err.message)
      throw err
    }
  }, [videos, loadVideos])

  // Get storage usage
  const getStorageUsage = useCallback(async () => {
    try {
      const usage = await getStorageUsageFromDB()
      setStorageUsage(usage)
      return usage
    } catch (err) {
      setError(err.message)
      throw err
    }
  }, [])

  // Refresh videos list
  const refresh = useCallback(() => {
    loadVideos()
  }, [loadVideos])

  // Load videos on mount
  useEffect(() => {
    loadVideos()
  }, [loadVideos])

  // Cleanup blob URLs on unmount
  useEffect(() => {
    return () => {
      videos.forEach(video => {
        if (video.blobUrl) {
          URL.revokeObjectURL(video.blobUrl)
        }
        if (video.thumbnailBlobUrl) {
          URL.revokeObjectURL(video.thumbnailBlobUrl)
        }
      })
    }
  }, [videos])

  // Listen for storage events (when videos are added from other tabs/components)
  useEffect(() => {
    const handleStorageChange = async () => {
      // Add a longer delay to ensure storage operations are complete
      await new Promise(resolve => setTimeout(resolve, 500))
      loadVideos()
    }

    // Listen for custom events when videos are added
    window.addEventListener('videoAdded', handleStorageChange)
    window.addEventListener('videoDeleted', handleStorageChange)

    return () => {
      window.removeEventListener('videoAdded', handleStorageChange)
      window.removeEventListener('videoDeleted', handleStorageChange)
    }
  }, [loadVideos])

  return {
    videos,
    storageUsage,
    loading,
    error,
    deleteVideo,
    clearAll,
    getStorageUsage,
    refresh,
  }
}

/**
 * Utility function to dispatch custom events for cross-component communication
 */
export function dispatchVideoEvent(eventType, data = null) {
  const event = new CustomEvent(eventType, { detail: data })
  window.dispatchEvent(event)
}
