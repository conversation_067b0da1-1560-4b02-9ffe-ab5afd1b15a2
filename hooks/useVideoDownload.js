import { useState, useCallback } from 'react'
import {
  downloadVideoBlob,
  downloadThumbnailBlob,
  extractThumbnailFromVideo,
  extractVideoDuration,
  storeVideo,
  isValidVideoUrl,
  getVideoSize,
  checkAvailableSpace,
} from '../utils/videoStorage'
import { formatBytes } from '../utils/strings'

/**
 * Custom hook for video download functionality
 * @param {Function} t - Translation function
 */
export default function useVideoDownload(t) {
  const [isDownloading, setIsDownloading] = useState(false)
  const [downloadProgress, setDownloadProgress] = useState(0)
  const [downloadError, setDownloadError] = useState(null)

  const downloadVideo = useCallback(
    async (url, thumbnailUrl = null, metadata = {}) => {
      if (!url || !url.trim()) {
        throw new Error(t('pleaseProvideValidUrl'))
      }

      const trimmedUrl = url.trim()
      const trimmedThumbnailUrl = thumbnailUrl?.trim() || null

      // Check if we're online
      if (typeof navigator !== 'undefined' && !navigator.onLine) {
        throw new Error(t('needOnlineToDownloadVideos'))
      }

      // Check if IndexedDB is available
      if (typeof window === 'undefined' || !window.indexedDB) {
        throw new Error(t('videoDownloadsNotSupported'))
      }

      // Basic URL validation
      if (!isValidVideoUrl(trimmedUrl)) {
        throw new Error(t('pleaseProvideValidVideoUrl'))
      }

      setIsDownloading(true)
      setDownloadProgress(0)
      setDownloadError(null)

      try {
        // Check video size and available storage space before downloading
        const estimatedSize = await getVideoSize(trimmedUrl)

        if (estimatedSize > 0) {
          const spaceCheck = await checkAvailableSpace(estimatedSize)

          if (!spaceCheck.hasSpace) {
            const requiredText = formatBytes(spaceCheck.required)
            const availableText =
              spaceCheck.available !== null
                ? formatBytes(spaceCheck.available)
                : 'Unknown'

            // Calculate how much more space is needed
            const shortfall =
              spaceCheck.available !== null
                ? spaceCheck.required - spaceCheck.available
                : spaceCheck.required
            const shortfallText = formatBytes(shortfall)

            throw new Error(
              t('insufficientStorageSpace', {
                required: requiredText,
                available: availableText,
                shortfall: shortfallText,
              })
            )
          }
        }

        // Download the video blob with progress tracking
        const blob = await downloadVideoBlob(trimmedUrl, progress => {
          // Use 80% of progress for video download
          setDownloadProgress(progress * 0.8)
        })

        // Validate that we got a video blob
        if (!blob || blob.size === 0) {
          throw new Error(t('failedToDownloadVideo'))
        }

        // Check if it's actually a video file
        if (
          !blob.type.startsWith('video/') &&
          !blob.type.startsWith('application/')
        ) {
          // Try to determine if it's a video based on content
          const arrayBuffer = await blob.slice(0, 12).arrayBuffer()
          const uint8Array = new Uint8Array(arrayBuffer)

          // Check for common video file signatures
          const isVideo = checkVideoSignature(uint8Array)

          if (!isVideo) {
            throw new Error(t('downloadedFileNotVideo'))
          }
        }

        setDownloadProgress(80)

        // Handle thumbnail
        let thumbnailBlob = null
        let finalThumbnailUrl = trimmedThumbnailUrl

        if (trimmedThumbnailUrl) {
          try {
            // Download thumbnail from provided URL
            thumbnailBlob = await downloadThumbnailBlob(trimmedThumbnailUrl)
            setDownloadProgress(85)
          } catch (thumbnailError) {
            finalThumbnailUrl = null
            // Continue without thumbnail rather than failing the entire download
          }
        }

        if (!thumbnailBlob) {
          try {
            // Try to extract thumbnail from video
            setDownloadProgress(87)
            thumbnailBlob = await extractThumbnailFromVideo(blob)
            setDownloadProgress(90)
          } catch (extractError) {
            // Continue without thumbnail - this is not a critical error
          }
        }

        // Extract video duration
        let duration = null
        try {
          setDownloadProgress(92)
          duration = await extractVideoDuration(blob)
          setDownloadProgress(95)
        } catch (durationError) {
          // Continue without duration - this is not a critical error
        }

        // Store the video in IndexedDB
        const videoData = {
          id: metadata.id || null,
          originalUrl: trimmedUrl,
          blob: blob,
          title: metadata.title || extractTitleFromUrl(trimmedUrl, t),
          subtitle: metadata.subtitle || null,
          body: metadata.body || null,
          channel: metadata.channel || null,
          show: metadata.show || null,
          duration: duration,
          thumbnailBlob: thumbnailBlob,
          thumbnailUrl: finalThumbnailUrl,
        }

        await storeVideo(videoData)

        setDownloadProgress(100)

        // Reset state after a short delay
        setTimeout(() => {
          setIsDownloading(false)
          setDownloadProgress(0)
        }, 1000)
      } catch (error) {
        setIsDownloading(false)
        setDownloadProgress(0)
        setDownloadError(error.message)
        throw error
      }
    },
    [t]
  )

  const clearError = useCallback(() => {
    setDownloadError(null)
  }, [])

  return {
    downloadVideo,
    isDownloading,
    downloadProgress,
    downloadError,
    clearError,
  }
}

/**
 * Check if the file has a video signature
 */
function checkVideoSignature(uint8Array) {
  // MP4 signature
  if (uint8Array.length >= 8) {
    const mp4Signature = uint8Array.slice(4, 8)
    const mp4Types = [
      [0x66, 0x74, 0x79, 0x70], // 'ftyp'
    ]

    for (const signature of mp4Types) {
      if (signature.every((byte, index) => byte === mp4Signature[index])) {
        return true
      }
    }
  }

  // WebM signature
  if (uint8Array.length >= 4) {
    const webmSignature = [0x1a, 0x45, 0xdf, 0xa3]
    if (webmSignature.every((byte, index) => byte === uint8Array[index])) {
      return true
    }
  }

  // AVI signature
  if (uint8Array.length >= 12) {
    const aviSignature1 = [0x52, 0x49, 0x46, 0x46] // 'RIFF'
    const aviSignature2 = [0x41, 0x56, 0x49, 0x20] // 'AVI '

    if (
      aviSignature1.every((byte, index) => byte === uint8Array[index]) &&
      aviSignature2.every((byte, index) => byte === uint8Array[index + 8])
    ) {
      return true
    }
  }

  // MOV/QuickTime signature
  if (uint8Array.length >= 8) {
    const movSignature = uint8Array.slice(4, 8)
    const movTypes = [
      [0x71, 0x74, 0x20, 0x20], // 'qt  '
      [0x6d, 0x6f, 0x6f, 0x76], // 'moov'
    ]

    for (const signature of movTypes) {
      if (signature.every((byte, index) => byte === movSignature[index])) {
        return true
      }
    }
  }

  return false
}

/**
 * Extract title from URL
 */
function extractTitleFromUrl(url, t) {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const filename = pathname.split('/').pop()

    if (filename && filename.includes('.')) {
      return filename.split('.').slice(0, -1).join('.')
    }

    return filename || t('videoFromHostname', { hostname: urlObj.hostname })
  } catch {
    return t('downloadedVideo')
  }
}
