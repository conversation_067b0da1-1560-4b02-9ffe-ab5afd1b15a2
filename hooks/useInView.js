import { useState, useEffect } from 'react'

/**
 * This hook uses the IntersectionObserver API to determine if an element is in view
 *
 * @param {React.Ref} ref - A ref to the element to observe
 * @param {object} options - Options object (See https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API for more details)
 * @returns {boolean} - True if the element is in view, false otherwise
 */
export default function useInView(
  ref,
  options = {
    threshold: 0.3, // Trigger when 30% of the element is in view
    rootMargin: '0px 0px -50px 0px', // Consider the element to be in view when it's 50px above the viewport
  }
) {
  const [isInView, setIsInView] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsInView(entry.isIntersecting)
    }, options)

    const currentElement = ref.current
    if (currentElement) {
      observer.observe(currentElement)
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement)
      }
    }
  }, [ref, options])

  return isInView
}
