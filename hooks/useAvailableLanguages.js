import { useMemo } from 'react'

export function useAvailableLanguages({ availableLanguages, search = '' }) {
  const languages = useMemo(() => {
    if (!search) return availableLanguages

    return availableLanguages.filter(({ name, nativeName, locale }) =>
      `${nativeName} ${name?.en || name} ${locale}`
        .toLowerCase()
        .includes(search.toLowerCase())
    )
  }, [availableLanguages, search])

  return languages
}
