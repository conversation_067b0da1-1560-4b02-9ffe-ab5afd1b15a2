/**
 * Calculate an RGB code based on the given HEX value
 * @param {string} hex
 * @returns {string}
 *
 * @example
 * hexToRGB('#FF0000') // 'rgb(255, 0, 0)'
 *
 * @example
 * hexToRGB('#FF0000', 0.5) // 'rgba(255, 0, 0, 0.5)'
 */
export function hexToRGB(hex, alpha = 1) {
  if (typeof hex !== 'string') return ''

  // Validate HEX
  hex = hex.replace('#', '')
  if (hex.length === 3)
    hex = hex
      .split('')
      .map(c => c + c)
      .join('')

  // Convert HEX to RGB
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)

  // Return RGB
  return `rgba(${r},${g},${b},${alpha})`
}
