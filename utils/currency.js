import Dinero from 'dinero.js'

import { useTranslation } from 'next-i18next'

/**
 * Returns a localized formated currency string for a given amount
 * @param {number} amount The value to format
 * @param {string} currency Currency to display, like `EUR`, `CHF`, `GBP` or `USD`. Default: `EUR`
 * @param {string} locale Locale code used for formating. Default: `de-DE`
 * @returns `string`
 */
export function getCurrencyFormat(amount, currency = 'EUR', locale = 'de-DE') {
  return Dinero({ amount: amount || 0, currency: currency || 'EUR' })
    .setLocale(locale || 'de-DE')
    .toFormat()
}

/**
 * Hook that returns a getCurrencyFormat with locale set automatically
 *
 * @returns function(amount, currency, locale)
 */
export function useCurrencyFormat() {
  const { i18n } = useTranslation()
  return (amount, currency, locale) => {
    // if (!currency) return null

    return getCurrencyFormat(amount, currency, locale || i18n.language)
  }
}

/**
 * Displays an amout in the specified currency with a locale format
 */
export function CurrencyFormat({ amount, currency, locale }) {
  const getCurrencyFormat = useCurrencyFormat()
  return getCurrencyFormat(amount, currency, locale)
}
