import { omit } from './objects'
import { isNumber, isFunction, isArray, isSet } from './types'

/**
 * Checks if an array is empty
 *
 * @param {array} array
 *
 * @return Boolean
 */
export function isEmpty(array) {
  return isArray(array) && array.length === 0
}

/**
 * Returns an array of numbers given a start and an end value
 * @param {number} start
 * @param {number} end
 * @returns `number[]`
 */
export function range(start, end) {
  if (!isNumber(start) || !isNumber(end)) {
    return []
  }

  if (start === end) {
    return [start]
  }

  return Array.from({ length: end + 1 - start }, (v, k) => k + start)
}

/**
 * Given an `array` of objects, returns a filtered version that excludes those where their `condition` attribute is `false`
 *
 * @param {array} items
 * @returns array
 */
export function getConditionalItems(items) {
  return isArray(items)
    ? items
        .filter(({ condition }) => !isSet(condition) || Boolean(condition))
        .map(item => ({
          ...omit(item, 'condition'),
        }))
    : []
}

/**
 * Returns an array with a separator inserted between each element in an Array.
 * @param {array} array items to intersperse
 * @param {any} separator separator item (supports a function: i.e. `(index, current) => "separator" )
 */
export function intersperse(array, separator) {
  const lastIndex = array.length - 1

  return array.reduce((acc, cur, index) => {
    acc.push(cur)
    if (lastIndex !== index) {
      acc.push(isFunction(separator) ? separator(index, cur) : separator)
    }
    return acc
  }, [])
}
