import isEmpty from 'lodash/isEmpty'
import isEqual from 'lodash/isEqual'
import omit from 'lodash/omit'
import pick from 'lodash/pick'

import { isFunction, isString, isObject } from 'utils/types'

export { pick, omit, isEmpty, isEqual }

export const setMap = (object, { setValue = (key, value) => value } = {}) =>
  isObject(object)
    ? new Map(
        Object.keys(object).map(key => [key, setValue(key, object?.[key])])
      )
    : null

/**
 * Returns an object's attibute using a key string or a funciton
 * @param object item
 * @param string|function key String used as a key, or Function called to retrive item's attribute
 * @returns `any`
 */
export function getAttribute(object, key) {
  return isFunction(key)
    ? key(object)
    : isString(key) && isObject(object)
    ? object?.[key]
    : null
}

export function removeUndefined(object = {}) {
  return Object.keys(object).reduce((acc, cur) => {
    if (object[cur] !== undefined) {
      acc[cur] = object[cur]
    }
    return acc
  }, {})
}

/**
 * Combines two objects, using the first as a base and the second as a fallback
 * @param object item
 * @param string|function key String used as a key, or Function called to retrive item's attribute
 * @returns `any`
 */
export function getDefaultValues(
  obj = {},
  defaults = {},
  test = v => v === undefined || v === ''
) {
  return Object.keys(defaults).reduce((acc, prop) => {
    acc[prop] = test(obj?.[prop]) ? defaults?.[prop] : obj?.[prop]
    return acc
  }, {})
}

/**
 * Merge source object with target object (custom deep merge).
 * - if values are objects, merge them too.
 * - if target value is undefined, use source value.
 * - if source value is undefined, use target value.
 * - if both values are undefined, use undefined.
 *
 * @param {object} source - source object
 * @param {object} target - target object
 * @returns {object} - merged object
 */
export function merge(source, target) {
  if (!isObject(source) || !isObject(target)) return {}

  const merged = {}

  Object.keys({ ...(source || {}), ...(target || {}) }).forEach(key => {
    const sourceValue = source[key]
    const targetValue = target[key]

    if (isObject(sourceValue) && isObject(targetValue)) {
      merged[key] = merge(sourceValue, targetValue)
    } else if (targetValue) {
      merged[key] = targetValue
    } else if (sourceValue) {
      merged[key] = sourceValue
    }
  })

  return merged
}
