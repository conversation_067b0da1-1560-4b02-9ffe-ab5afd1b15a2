/**
 * Validates a JSON string.
 *
 * @param {string} jsonString - The JSON string to validate.
 * @returns {string|undefined} - The validated JSON string, or undefined if the validation fails.
 */
export function validateJsonString(jsonString) {
  if (!jsonString) {
    return undefined
  }

  try {
    JSON.parse(jsonString)
  } catch (e) {
    console.log('Error parsing JSON string', e, jsonString) // eslint-disable-line no-console
    return undefined
  }
  return jsonString
}
