export function isArray(value) {
  return Array.isArray(value)
}

/**
 * Simple object check.
 * @param item
 * @returns {boolean}
 */
export function isObject(item) {
  return Boolean(
    item && typeof item === 'object' && !Array.isArray(item) && item !== null
  )
}

export function isFunction(value) {
  return typeof value === 'function'
}

export function isNumber(value) {
  return typeof value === 'number'
}

export function isString(value) {
  return typeof value === 'string'
}

export function isSet(value) {
  return value !== '' && value !== undefined && value !== null
}
