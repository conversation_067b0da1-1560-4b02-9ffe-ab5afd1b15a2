const imagesCDN = process.env.NEXT_PUBLIC_IMAGES_CDN
const imagesBucket = process.env.NEXT_PUBLIC_BUCKET_IMAGES

/**
 * Given an image path, returns the corresponding signature
 *
 * @param {string} path
 * @returns {string} signature
 */
function getImageSignature(path) {
  // Return base64 encoded path, removing no URL-friendly characters
  return Buffer.from(path)
    .toString('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
}

/**
 * Returns an Image's absolute url
 *
 * @param {object} file
 * @param {string} formatOptions Check here for processing options: https://github.com/hope-media/images-worker
 */
export function getImageUrl(file, formatOptions = 'w:800') {
  if (!file || !file.name || !file.containerId) return ''

  formatOptions = formatOptions.replace(/:/g, '_')
  formatOptions = formatOptions.replace(/,/g, '__')

  const path = `/${formatOptions}/${imagesBucket}/${file.containerId}/${file.name}`

  const signature = getImageSignature(path)

  return `${imagesCDN}/resize/${signature}${path}`
}
