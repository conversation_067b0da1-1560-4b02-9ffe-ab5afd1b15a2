// Extracted from https://dev.to/gabe_ragland/debouncing-with-react-hooks-jci
import React from 'react'

/**
 * Custom hook to debounce a value
 * @param {string} value - The value to debounce
 * @param {number} delay - The delay of the debounce
 * @returns `string` - The debounced value
 */
export default function useDebounce(value, delay = 300) {
  const [debouncedValue, setDebouncedValue] = React.useState(value)

  React.useEffect(() => {
    // Set debouncedValue to value (passed in) after the specified delay
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Custom hook to debounce search input
 * @param {object} options - The options to pass
 * @param {string} options.initialValue - The initial value of the search input
 * @param {number} options.delay - The delay of the debounce in ms (default: 300)
 * @param {function} options.callback - The callback to call when the debounced value changes
 * @returns `array` - The search value, the onChange handler and the debounced value
 */
export function useDebounceSearch({
  initialValue = '',
  delay = 300,
  callback = null,
}) {
  const prevValueRef = React.useRef(initialValue)
  const [value, setValue] = React.useState(initialValue)

  const debouncedValue = useDebounce(value, delay)

  const handleValue = React.useCallback(e => {
    setValue(e.target.value)
  }, [])

  React.useEffect(() => {
    if (debouncedValue !== prevValueRef.current) {
      prevValueRef.current = debouncedValue

      if (typeof callback === 'function') {
        callback(debouncedValue)
      }
    }
  }, [callback, debouncedValue])

  return [value, handleValue, debouncedValue]
}
