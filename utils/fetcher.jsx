import isEmpty from 'lodash/isEmpty'

const API_URL = process.env.NEXT_PUBLIC_API_URL
const API_CLIENT_TOKEN = process.env.NEXT_PUBLIC_API_CLIENT_TOKEN

const baseHeaders = {
  ClientToken: API_CLIENT_TOKEN,
}

/**
 * Convert an object to a query string
 * @param {object} object - The object to convert
 * @returns `string`
 */
function objectToUrl(object = {}) {
  if (isEmpty(object)) {
    return ''
  }

  // Convert the object to an array of key=value pairs
  return Object.entries(object)
    .map(([key, val]) => {
      // If the value is a number, a boolean or a string, return it as is (i.e. ?key=123 or ?key=123.45)
      if (
        typeof val === 'number' ||
        typeof val === 'boolean' ||
        typeof val === 'string'
      ) {
        return `${key}=${val}`
      }

      // If the value is undefined, return null
      if (isEmpty(val)) {
        return null
      }

      // If the value is an array, join it with a comma (i.e. ?key=val1,val2)
      if (Array.isArray(val)) {
        return `${key}=${val.join(',')}`
      }

      // If the value is an object, stringify it (as JSON string, i.e. ?key={"val1":"val2"})
      if (typeof val === 'object') {
        return `${key}=${JSON.stringify(val)}`
      }

      // Otherwise, in case of an unexpected value type, console.log it and return null
      console.log('👀 fetcher/objectToUrl', key, typeof val, val) // eslint-disable-line no-console
      return null
    })
    .filter(Boolean) // Remove all empty values
    .join('&') // Join all the key=value pairs with an ampersand
}

/**
 * Default fetch for "get" methods
 * @param {string} url - The url to fetch
 * @param {object} options - The options to send
 * @param {object} options.params - The params to send (query)
 * @param {object} options.headers - The headers to send
 * @returns `Promise`
 */
export async function getRequest(
  url,
  options = {
    params: {},
    headers: {},
    baseURL: '',
  }
) {
  // Get Base URL
  const baseURL = options?.baseURL || API_URL || ''

  if (!baseURL) throw new Error('postRequest: Base URL is undefined')

  if (!url) throw new Error('URL is undefined')

  // Get URL params
  const params = options?.params || {}
  const hasParams = Object.keys(params).length > 0
  const urlParams = hasParams ? `?${objectToUrl(params)}` : ''

  // Get headers
  const headers = {
    ...baseHeaders,
    'Content-Type': 'application/json',
    ...(options?.headers || {}),
  }

  // Attempt to send get request
  try {
    const response = await fetch(`${baseURL}${url}${urlParams}`, {
      headers,
    })

    return await response.json()
  } catch (error) {
    throw new Error('An unexpected error occurred')
  }
}

/**
 * Default fetch for "post" methods
 * @param {string} url - The url to fetch
 * @param {object} data - The data to post (body)
 * @param {object} options - The options to send
 * @param {object} options.params - The params to send (query)
 * @param {object} options.headers - The headers to send
 * @returns `Promise`
 */
export async function postRequest(
  url,
  body = {},
  options = {
    params: {},
    headers: {},
    baseURL: '',
  }
) {
  // Get Base URL
  const baseURL = options?.baseURL || API_URL || ''

  // Check if the baseUrl and url are defined, and throw an error if they aren't
  if (!baseURL) throw new Error('postRequest: Base URL is undefined')
  if (!url) throw new Error('postRequest: URL is undefined')

  // Get URL params
  const params = options?.params || {}
  const hasParams = Object.keys(params).length > 0
  const urlParams = hasParams ? `?${objectToUrl(params)}` : ''

  // Get headers
  const headers = { ...baseHeaders, ...(options?.headers || {}) }

  // Check if the content type is JSON
  const isJsonContentType = headers['Content-Type'] === 'application/json'

  // Attempt to send post request
  // If it fails to fetch it will throw the error "Failed to fetch"
  const response = await fetch(`${baseURL}${url}${urlParams}`, {
    method: 'POST',
    headers,
    body: isJsonContentType ? JSON.stringify(body) : body,
  })

  // If the response is not ok, throw an error with the response body
  if (!response.ok) {
    throw new Error(
      JSON.stringify({
        message: 'Bad Request',
        errors: await response.json(),
      })
    )
  }

  // If a json response is expected, return it as json, otherwise as text
  try {
    return isJsonContentType ? await response.json() : await response.text()
  } catch (error) {
    throw new Error('Failed to parse response body')
  }
}
