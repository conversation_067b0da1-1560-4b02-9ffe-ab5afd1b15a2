export function nextReplaceUrl(parameter, value) {
  if (typeof parameter !== 'string') {
    throw new Error('parameter is not a string')
  }

  if (typeof value !== 'string') {
    throw new Error('value is not a string')
  }

  const { as, url } = window.history.state

  const parts = as.split('?')
  const searchParams = new URLSearchParams(parts[1])
  searchParams.set(parameter, value)

  // Build the new state
  const newAs = `${parts[0]}?${searchParams.toString()}`
  const newUrl = url.replace(
    new RegExp(`${parameter}=.*?(?=&|$)`),
    `${parameter}=${value}`
  )

  // Apply the new state
  window.history.replaceState(
    { ...window.history.state, as: newAs, url: newUrl },
    '',
    newAs
  )
}
