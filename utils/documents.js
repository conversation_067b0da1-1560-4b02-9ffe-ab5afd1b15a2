const API_URL = process.env.NEXT_PUBLIC_API_URL

/**
 * Returns a Document's url
 *
 * @param {object} file
 * @param {object} entityId
 */
export function getDocumentUrl(file, entityId = '', customName = '') {
  if (!entityId || !file || !file.name) return ''

  return `${API_URL}/files/${entityId}/${file.name}/${
    customName || file.originalFilename
  }${file.extension}`
}

/**
 * Returns a proxied document url
 *
 * @param {object} file
 * @param {object} entityId
 */
export function getProxiedDocumentUrl(file, entityId = '', customName = '') {
  if (!entityId || !file || !file.name) return ''

  return `/api/files/${entityId}/${file.name}/${
    customName || file.originalFilename
  }${file.extension}`
}
