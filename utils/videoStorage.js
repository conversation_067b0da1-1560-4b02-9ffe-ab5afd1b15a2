/**
 * IndexedDB utilities for video storage
 */

const DB_NAME = 'VideoDownloadsDB'
const DB_VERSION = 5 // Increased version to support subtitle and body (rich text)
const STORE_NAME = 'videos'

/**
 * Initialize IndexedDB
 */
export function initDB() {
  return new Promise((resolve, reject) => {
    // Check if IndexedDB is available
    if (typeof window === 'undefined' || !window.indexedDB) {
      reject(new Error('IndexedDB is not available in this browser'))
      return
    }

    try {
      const request = indexedDB.open(DB_NAME, DB_VERSION)

      request.onerror = () => {
        const error = request.error || new Error('Failed to open IndexedDB')
        reject(error)
      }

      request.onsuccess = () => resolve(request.result)

      request.onupgradeneeded = event => {
        try {
          const db = event.target.result

          if (!db.objectStoreNames.contains(STORE_NAME)) {
            const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' })
            store.createIndex('downloadedAt', 'downloadedAt', { unique: false })
            store.createIndex('originalUrl', 'originalUrl', { unique: false })
          }

          // We don't need to modify the schema structure since we're just adding new fields
          // to existing records. The new fields will be added as videos are re-saved.
          // const oldVersion = event.oldVersion
        } catch (error) {
          reject(new Error(`Failed to upgrade database: ${error.message}`))
        }
      }
    } catch (error) {
      reject(new Error(`Failed to initialize IndexedDB: ${error.message}`))
    }
  })
}

/**
 * Store a video blob in IndexedDB
 */
export async function storeVideo(videoData) {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite')
    const store = transaction.objectStore(STORE_NAME)

    const videoRecord = {
      id: videoData.id || generateVideoId(),
      title: videoData.title || extractTitleFromUrl(videoData.originalUrl),
      subtitle: videoData.subtitle || null,
      body: videoData.body || null,
      channel: videoData.channel || null,
      show: videoData.show || null,
      originalUrl: videoData.originalUrl,
      blob: videoData.blob,
      size: videoData.blob.size,
      type: videoData.blob.type,
      duration: videoData.duration || null,
      thumbnailBlob: videoData.thumbnailBlob || null,
      thumbnailUrl: videoData.thumbnailUrl || null,
      downloadedAt: new Date().toISOString(),
    }

    const request = store.add(videoRecord)

    request.onerror = () => reject(request.error)
    request.onsuccess = () => {
      // Dispatch event to notify other components
      if (typeof window !== 'undefined') {
        window.dispatchEvent(
          new CustomEvent('videoAdded', { detail: videoRecord })
        )
      }
      resolve(videoRecord)
    }
  })
}

/**
 * Get all stored videos
 */
export async function getAllVideos() {
  try {
    const db = await initDB()

    return new Promise((resolve, reject) => {
      try {
        const transaction = db.transaction([STORE_NAME], 'readonly')
        const store = transaction.objectStore(STORE_NAME)
        const request = store.getAll()

        request.onerror = () => {
          const error = request.error || new Error('Failed to retrieve videos')
          reject(error)
        }

        request.onsuccess = () => {
          try {
            const videos = request.result.map(video => {
              let blobUrl = null
              let thumbnailBlobUrl = null

              try {
                // Create blob URL with Safari-compatible options
                if (video.blob) {
                  blobUrl = URL.createObjectURL(video.blob)
                }

                // Create thumbnail blob URL if thumbnail exists
                if (video.thumbnailBlob) {
                  thumbnailBlobUrl = URL.createObjectURL(video.thumbnailBlob)
                }
              } catch {
                // Continue without blob URLs - the blob data is still available
              }

              return {
                ...video,
                blobUrl,
                thumbnailBlobUrl,
                // Keep reference to original blob for Safari compatibility
                blob: video.blob,
              }
            })
            resolve(videos)
          } catch (error) {
            reject(new Error(`Failed to process video data: ${error.message}`))
          }
        }
      } catch (error) {
        reject(new Error(`Failed to access video storage: ${error.message}`))
      }
    })
  } catch {
    // If IndexedDB initialization fails, return empty array
    return []
  }
}

/**
 * Delete a video by ID
 */
export async function deleteVideo(videoId) {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.delete(videoId)

    request.onerror = () => reject(request.error)
    request.onsuccess = () => {
      // Dispatch event to notify other components
      if (typeof window !== 'undefined') {
        window.dispatchEvent(
          new CustomEvent('videoDeleted', { detail: { videoId } })
        )
      }
      resolve()
    }
  })
}

/**
 * Get storage usage statistics
 */
export async function getStorageUsage() {
  const videos = await getAllVideos()

  const totalSize = videos.reduce((sum, video) => sum + video.size, 0)

  // Get storage quota information
  let quota = null
  let usage = null
  let available = null

  try {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      // Try multiple times with increasing delays to get accurate storage estimate
      let attempts = 0
      const maxAttempts = 3

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 200 * (attempts + 1)))

        const estimate = await navigator.storage.estimate()
        const estimatedUsage = estimate.usage || 0

        // If the browser's estimate is significantly less than our video storage,
        // use our calculation as the minimum
        if (estimatedUsage < totalSize) {
          usage = Math.max(totalSize, estimatedUsage)
        } else {
          usage = estimatedUsage
        }

        quota = estimate.quota || null
        available = quota && usage ? quota - usage : null

        // If we got a reasonable estimate, break
        if (usage >= totalSize * 0.8) {
          // Allow some margin for metadata
          break
        }

        attempts++
      }

      // Fallback: if browser estimate is still unreliable, use our calculation
      if (!usage || usage < totalSize) {
        usage = totalSize
        available = quota ? quota - usage : null
      }
    }
  } catch {
    // Fallback to our own calculations
    usage = totalSize
  }

  return {
    used: totalSize,
    count: videos.length,
    quota: quota,
    totalUsage: usage,
    available: available,
    videos: videos.map(video => ({
      id: video.id,
      title: video.title,
      size: video.size,
      downloadedAt: video.downloadedAt,
    })),
  }
}

/**
 * Clear all stored videos
 */
export async function clearAllVideos() {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.clear()

    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve()
  })
}

/**
 * Download video from URL and return blob
 */
export async function downloadVideoBlob(url, onProgress) {
  try {
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const contentLength = response.headers.get('content-length')
    const total = contentLength ? parseInt(contentLength, 10) : 0

    // Call progress immediately to show 0%
    if (onProgress) {
      onProgress(0)
    }

    if (!response.body) {
      throw new Error('ReadableStream not supported')
    }

    const reader = response.body.getReader()
    const chunks = []
    let received = 0

    // eslint-disable-next-line no-constant-condition
    while (true) {
      const { done, value } = await reader.read()

      if (done) break

      chunks.push(value)
      received += value.length

      // Always call progress callback, even without content-length
      if (onProgress) {
        if (total > 0) {
          onProgress((received / total) * 100)
        } else {
          // If no content-length, show indeterminate progress
          onProgress(Math.min(90, (received / 1024 / 1024) * 10)) // Rough estimate based on MB
        }
      }
    }

    // Final progress update
    if (onProgress) {
      onProgress(100)
    }

    const blob = new Blob(chunks, {
      type: response.headers.get('content-type') || 'video/mp4',
    })

    return blob
  } catch (error) {
    throw new Error(`Failed to download video: ${error.message}`)
  }
}

/**
 * Download thumbnail from URL and return blob
 */
export async function downloadThumbnailBlob(url) {
  try {
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()

    // Validate that we got an image blob
    if (!blob.type.startsWith('image/')) {
      throw new Error('The downloaded file is not an image')
    }

    return blob
  } catch (error) {
    throw new Error(`Failed to download thumbnail: ${error.message}`)
  }
}

/**
 * Extract thumbnail from video blob using canvas
 */
export async function extractThumbnailFromVideo(videoBlob) {
  return new Promise((resolve, reject) => {
    let video = null
    let videoUrl = null
    let timeoutId = null

    const cleanup = () => {
      try {
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        if (video) {
          video.pause()
          video.src = ''
          video.load() // Force cleanup
        }
        if (videoUrl) {
          URL.revokeObjectURL(videoUrl)
        }
      } catch {
        // Ignore cleanup errors
      }
    }

    const handleError = errorMessage => {
      cleanup()
      reject(new Error(errorMessage))
    }

    try {
      video = document.createElement('video')
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        throw new Error('Failed to get canvas 2D context')
      }

      // Set up video element
      video.crossOrigin = 'anonymous'
      video.muted = true
      video.playsInline = true
      video.preload = 'metadata'

      // Set timeout to prevent hanging
      timeoutId = setTimeout(() => {
        handleError('Thumbnail extraction timed out after 30 seconds')
      }, 30000)

      video.onloadedmetadata = () => {
        try {
          // Check if video has valid dimensions
          if (!video.videoWidth || !video.videoHeight) {
            handleError('Video has invalid dimensions')
            return
          }

          // Set canvas dimensions (thumbnail size)
          const maxWidth = 320
          const maxHeight = 180
          const aspectRatio = video.videoWidth / video.videoHeight

          if (aspectRatio > maxWidth / maxHeight) {
            canvas.width = maxWidth
            canvas.height = maxWidth / aspectRatio
          } else {
            canvas.height = maxHeight
            canvas.width = maxHeight * aspectRatio
          }

          // Ensure minimum dimensions
          if (canvas.width < 1 || canvas.height < 1) {
            handleError('Calculated canvas dimensions are too small')
            return
          }

          // Seek to a good frame (10% into the video or 5 seconds, whichever is smaller)
          const seekTime = Math.min(video.duration * 0.1, 5)

          // If duration is invalid or very short, use first frame
          if (isNaN(seekTime) || seekTime < 0.1) {
            video.currentTime = 0.1
          } else {
            video.currentTime = seekTime
          }
        } catch (error) {
          handleError(`Failed to set up video metadata: ${error.message}`)
        }
      }

      video.onseeked = () => {
        try {
          // Additional check to ensure video is ready
          if (video.readyState < 2) {
            handleError('Video not ready for frame extraction')
            return
          }

          // Clear the canvas first
          ctx.clearRect(0, 0, canvas.width, canvas.height)

          // Draw the video frame to canvas
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

          // Convert canvas to blob
          canvas.toBlob(
            blob => {
              if (blob && blob.size > 0) {
                cleanup()
                resolve(blob)
              } else {
                handleError('Failed to create thumbnail blob or blob is empty')
              }
            },
            'image/jpeg',
            0.8
          )
        } catch (error) {
          handleError(`Failed to draw video frame: ${error.message}`)
        }
      }

      video.onerror = () => {
        const errorMsg = video.error
          ? `Video error: ${video.error.message} (code: ${video.error.code})`
          : 'Unknown video error occurred'
        handleError(errorMsg)
      }

      video.onabort = () => {
        handleError('Video loading was aborted')
      }

      video.onstalled = () => {
        // Don't immediately error on stalled, but log it
        // eslint-disable-next-line no-console
        console.warn('Video loading stalled during thumbnail extraction')
      }

      // Create a blob URL and load the video
      try {
        videoUrl = URL.createObjectURL(videoBlob)
        video.src = videoUrl

        // Trigger loading
        video.load()
      } catch (error) {
        handleError(`Failed to create or load video blob URL: ${error.message}`)
      }
    } catch (error) {
      handleError(`Failed to extract thumbnail: ${error.message}`)
    }
  })
}

/**
 * Generate a unique video ID
 */
function generateVideoId() {
  return `video_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

/**
 * Extract title from URL
 */
function extractTitleFromUrl(url) {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const filename = pathname.split('/').pop()

    if (filename && filename.includes('.')) {
      return filename.split('.').slice(0, -1).join('.')
    }

    return filename || `Video from ${urlObj.hostname}`
  } catch {
    return 'Downloaded Video'
  }
}

/**
 * Check if URL is a valid video URL
 */
export function isValidVideoUrl(url) {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname.toLowerCase()

    // Check for common video file extensions
    const videoExtensions = [
      '.mp4',
      '.webm',
      '.ogg',
      '.avi',
      '.mov',
      '.wmv',
      '.flv',
      '.mkv',
      '.m4v',
      '.3gp',
    ]
    const hasVideoExtension = videoExtensions.some(ext =>
      pathname.endsWith(ext)
    )

    // Allow any HTTP/HTTPS URL for now - we'll validate the content type during download
    const isHttpUrl =
      urlObj.protocol === 'http:' || urlObj.protocol === 'https:'

    return (
      isHttpUrl &&
      (hasVideoExtension ||
        pathname.includes('video') ||
        pathname.includes('media'))
    )
  } catch {
    return false
  }
}

/**
 * Extract video duration from blob
 */
export async function extractVideoDuration(videoBlob) {
  return new Promise((resolve, reject) => {
    let video = null
    let videoUrl = null
    let timeoutId = null

    const cleanup = () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      if (video) {
        video.removeEventListener('loadedmetadata', handleLoadedMetadata)
        video.removeEventListener('error', handleError)
        video.removeEventListener('abort', handleAbort)
        video.src = ''
        video = null
      }
      if (videoUrl) {
        try {
          URL.revokeObjectURL(videoUrl)
        } catch {
          // Ignore revocation errors
        }
        videoUrl = null
      }
    }

    const handleLoadedMetadata = () => {
      try {
        const duration = video.duration
        cleanup()

        // Check if duration is valid
        if (isNaN(duration) || duration <= 0) {
          resolve(null) // Return null for invalid duration
        } else {
          resolve(duration)
        }
      } catch (error) {
        cleanup()
        reject(new Error(`Failed to get video duration: ${error.message}`))
      }
    }

    const handleError = () => {
      cleanup()
      reject(new Error('Failed to load video for duration extraction'))
    }

    const handleAbort = () => {
      cleanup()
      reject(new Error('Video loading was aborted during duration extraction'))
    }

    try {
      video = document.createElement('video')

      // Set up video element
      video.crossOrigin = 'anonymous'
      video.muted = true
      video.playsInline = true
      video.preload = 'metadata'

      // Set timeout to prevent hanging (10 seconds should be enough for metadata)
      timeoutId = setTimeout(() => {
        cleanup()
        reject(new Error('Duration extraction timed out after 10 seconds'))
      }, 10000)

      video.addEventListener('loadedmetadata', handleLoadedMetadata)
      video.addEventListener('error', handleError)
      video.addEventListener('abort', handleAbort)

      // Create a blob URL and load the video
      videoUrl = URL.createObjectURL(videoBlob)
      video.src = videoUrl
      video.load()
    } catch (error) {
      cleanup()
      reject(new Error(`Failed to extract video duration: ${error.message}`))
    }
  })
}

/**
 * Check if there's enough available storage space for a video download
 * @param {number} requiredSize - The size in bytes required for the video
 * @returns {Promise<{hasSpace: boolean, available: number, required: number}>}
 */
export async function checkAvailableSpace(requiredSize) {
  const storageInfo = await getStorageUsage()

  // If we can't determine the quota, assume we have space (degraded experience)
  if (!storageInfo.quota || storageInfo.available === null) {
    return {
      hasSpace: true,
      available: null,
      required: requiredSize,
      quota: storageInfo.quota,
    }
  }

  const hasSpace = storageInfo.available >= requiredSize

  return {
    hasSpace,
    available: storageInfo.available,
    required: requiredSize,
    quota: storageInfo.quota,
  }
}

/**
 * Get the estimated size of a video from a URL without downloading it
 * @param {string} url - The video URL
 * @returns {Promise<number>} - The estimated size in bytes, or 0 if unknown
 */
export async function getVideoSize(url) {
  try {
    const response = await fetch(url, { method: 'HEAD' })

    if (!response.ok) {
      // If HEAD request fails, try with a partial GET request
      const partialResponse = await fetch(url, {
        headers: { Range: 'bytes=0-1' },
      })

      if (!partialResponse.ok) {
        throw new Error(`HTTP error! status: ${partialResponse.status}`)
      }

      const contentRange = partialResponse.headers.get('content-range')
      if (contentRange) {
        // Extract total size from content-range header (e.g., "bytes 0-1/12345")
        const match = contentRange.match(/\/(\d+)$/)
        if (match) {
          return parseInt(match[1], 10)
        }
      }
    }

    const contentLength = response.headers.get('content-length')
    if (contentLength) {
      return parseInt(contentLength, 10)
    }

    return 0 // Size unknown
  } catch (error) {
    return 0 // Size unknown
  }
}
