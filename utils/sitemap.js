/**
 * Outputs the Sitemap index wrapper
 * @param {String} xml XML string containing all sitemap items
 * @returns
 */
export function generateSitemapIndex(xml = '') {
  return `<?xml version="1.0" encoding="UTF-8"?>
  <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    ${xml}
  </sitemapindex>`
}
/**
 * Outputs the Sitemap index item
 * @param {String} url Sitemap index item's URL
 * @returns
 */
export function generateSitemapIndexItem(url = '') {
  return `<sitemap><loc>${url}</loc></sitemap>`
}

/**
 * Outputs the Sitemap wrapper
 * @param {String} xml XML string containing all sitemap items
 * @returns
 */
export function generateSitemapWrap(xml = '') {
  return `<?xml version="1.0" encoding="UTF-8"?>
  <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${xml}
  </urlset>`
}

/**
 * Outputs a single entry for a page in the sitemap
 * @param {String} domain site's domain
 * @param {String} path page path
 * @param {Data} date page last update
 * @returns `String`
 */
export function generateSitemapItem(loc = '', lastmod) {
  if (!loc || !lastmod) return ''

  return `    <url>
      <loc>${loc}</loc>
      <lastmod>${lastmod}</lastmod>
    </url>`
}
