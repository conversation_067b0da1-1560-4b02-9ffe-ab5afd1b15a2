export const breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl', '2xl']

export const breakpoints = {
  'xs': 350,
  'sm': 480,
  'md': 768,
  'lg': 976,
  'xl': 1100,
  '2xl': 1440,
}

export const media = {
  'sm': `(min-width: ${breakpoints.sm}px)`,
  'md': `(min-width: ${breakpoints.md}px)`,
  'lg': `(min-width: ${breakpoints.lg}px)`,
  'xl': `(min-width: ${breakpoints.xl}px)`,
  '2xl': `(min-width: ${breakpoints['2xl']}px)`,

  'max': {
    xs: `(max-width: ${breakpoints.sm - 1}px)`,
    sm: `(max-width: ${breakpoints.md - 1}px)`,
    md: `(max-width: ${breakpoints.lg - 1}px)`,
    lg: `(max-width: ${breakpoints.xl - 1}px)`,
    xl: `(max-width: ${breakpoints['2xl'] - 1}px)`,
  },

  'touch': '(any-hover: none) and (any-pointer: coarse)',
}
