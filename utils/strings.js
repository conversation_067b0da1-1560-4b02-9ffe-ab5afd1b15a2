import _ from 'lodash'
import { isNumber, isString } from './types'

/**
 * Encodes a given string in HEX
 *
 * @param {string} string
 * @returns {string} encoded string
 */
export function stringToHex(string = '') {
  return string
    .split('')
    .map(c => c.charCodeAt(0).toString(16).padStart(2, '0'))
    .join('')
}

/**
 * Format file size in human-readable format
 * @param {number} bytes - Size in bytes
 * @param {number} decimals - Number of decimal places
 * @returns {string} - Formatted size string
 */
export function formatBytes(bytes, decimals = 1) {
  if (bytes === 0) return '0 B'
  if (bytes === null || bytes === undefined) return 'Unknown'

  const kilo = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(kilo))

  return parseFloat((bytes / Math.pow(kilo, i)).toFixed(dm)) + ' ' + sizes[i]
}

export function truncate(string = '', length = 200) {
  return _.truncate(string, { length })
}

export function capitalize(string = '') {
  return _.capitalize(string)
}

export function nFormatter(num, digits) {
  const lookup = [
    { value: 1, symbol: '' },
    { value: 1e3, symbol: 'k' },
    { value: 1e6, symbol: 'M' },
    { value: 1e9, symbol: 'G' },
    { value: 1e12, symbol: 'T' },
    { value: 1e15, symbol: 'P' },
    { value: 1e18, symbol: 'E' },
  ]
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/
  var item = lookup
    .slice()
    .reverse()
    .find(function (item) {
      return num >= item.value
    })
  return item
    ? (num / item.value).toFixed(digits).replace(rx, '$1') + item.symbol
    : '0'
}

/**
 * Converts a string into a slug[ish] String
 *
 * @param {string} text The string to convert in a slug
 * @param {Object} options Options object
 * @param {number} options.maxLength - Maximum slug string lenght
 * @param {boolean} options.addDashBetweenCharAndNumber - Add a dash between a number and a character that is not a number (except -)
 * @returns {string} the slugified string
 *
 * @example
 * slugify('Hello World') // 'hello-world'
 * slugify('Hello World', { maxLength: 5 }) // 'hello'
 * slugify('Hello123 World') // 'hello123-world'
 * slugify('Hello123 World', { addDashBetweenCharAndNumber: true }) // 'hello-123-world'
 */
export function slugify(
  text,
  { maxLength, addDashBetweenCharAndNumber = false } = {}
) {
  if (!isString(text)) {
    return ''
  }

  let slug = text
    .normalize('NFKC') // Replaces special characters with their normal version
    .replace(/[°<>#%{}()|\\^~[\]`'?=:;]/g, '') // Removes invalid characters to make it a valid URL

    // Replace spaces with dashes
    .replace(/ /g, '-') // Replaces spaces with dashes

  if (addDashBetweenCharAndNumber) {
    // Adds a dash between a number and a character that is not a number (except -)
    slug.replace(/([^\d-])(?=\d)|(\d)(?![\d-]|$)(?=[^\d-])/g, '$1$2-')
  }

  // Cleans up the slug
  slug = slug
    .toLowerCase() // Converts to lowercase to avoid case sensitive issues
    .replace(/\/+/g, '') // Removes slashes
    .replace(/\s+/g, '-') // Replaces spaces with dashes
    .replace(/[,!"§$&_+*]/g, '-') // Replaces some special characters with dashes
    .replace(/--+/g, '-') // Replaces two or more dashes with a single one
    .replace(/^-+/, '') // Trim - from start of text
    .replace(/-+$/, '') // Trim - from end of text
    .replace(/\u{2D}\u{301}/gu, '') //Replaces acute accent when it's combined with a dash, beacuse this character appears when you type an acute accent without any other character

  // Truncates the slug if maxLength is provided
  if (isNumber(maxLength) && slug.length > maxLength) {
    slug = slug.slice(0, maxLength).replace(/-+$/, '') // Trim - from end of text
  }

  return slug
}
