import { NextResponse } from 'next/server'
import { getFetch } from './http'

// We cache the response we get from the api, so we don't have to query it every time
// The default time for the cache is 5 minutes

const PUBLIC_FILE = /\.(.*)$/

// Function to query the api for the available locales and default locale
export async function getLocaleData(origin) {
  const headers = {
    Origin: origin,
  }

  try {
    return await getFetch('/web/languages', {}, { headers })
  } catch (error) {
    console.error('Error getting locale data for origin:', origin, error) // eslint-disable-line no-console

    // If there is an error, we return the default locale as "en"
    return {
      defaultLocale: 'en',
      locales: ['en'],
    }
  }
}

export function getLanguagePrefix(language, defaultLocale) {
  return language === defaultLocale ? '' : `/${language}`
}

// This function gets the browser language from the request headers, with the highest priority taken into account.
// TODO: check what happens with 3 letter codes
export function getBrowserLanguage(req, locales) {
  return req.headers
    .get('accept-language')
    ?.split(',')
    .map(i => i.split(';'))
    ?.reduce((ac, lang) => [...ac, { code: lang[0], priority: lang[1] }], [])
    ?.sort((a, b) => (a.priority > b.priority ? -1 : 1))
    ?.find(i => locales.includes(i.code.substring(0, 2)))
    ?.code?.substring(0, 2)
}

export function getLanguageFromPath(path, locales) {
  // We check if the first part of the path is a language, against the locales
  const language = path.split('/')[1]
  return locales?.includes(language) ? language : null
}

export default async function i18nMiddleware(req) {
  // We don't want to redirect the user if they are trying to access the next.js files or the API
  if (
    req.nextUrl.pathname.startsWith('/_next') ||
    req.nextUrl.pathname.includes('/api/') ||
    PUBLIC_FILE.test(req.nextUrl.pathname)
  ) {
    return
  }

  // We get the locales and default locales for this site.
  const origin = req.headers?.get('host')

  const localeData = await getLocaleData(origin)

  const { defaultLocale, locales } = localeData

  // If there are no languages, we return the response
  if (locales?.length <= 1) {
    return getResponse({ localeData })
  }

  // This is the cookie that we set in the middleware when we redirect to a new locale
  const nextLocale = req.cookies.get('NEXT_LOCALE')?.value || null

  // Check if the locale from the path is valid
  const languageFromPath = getLanguageFromPath(req.nextUrl.pathname, locales)
  const localePath = locales.includes(languageFromPath)
    ? languageFromPath
    : null

  // This is the prefix that we will add to the path
  const localePrefix = localePath
    ? localePath === defaultLocale
      ? ''
      : `/${localePath}`
    : ''

  // This is the locale that we will use to check if the current route has a locale (if not, it is the default locale)
  const routeLocale = localePath || defaultLocale

  const plainPathname = localePath
    ? req.nextUrl.pathname.replace(`/${localePath}`, '') // This replaces the first instance of the locale in the path
    : req.nextUrl.pathname

  const prevUrl = new URL(req.url)
  const url = new URL(
    `${localePrefix}${plainPathname}${req.nextUrl.search}`,
    req.url.replace(req.nextUrl.pathname, '')
  )

  // Logic
  // If nextLocale exists and is in the list of available languages, we perform some checks
  if (locales.includes(nextLocale)) {
    if (!localePath && nextLocale === defaultLocale) {
      // If no localePath, we are in the default language, we add the cookie of the default language and return
      return getResponse({ language: defaultLocale, localeData })
    }

    // This localePrefix is what will go in the path.
    if (nextLocale === routeLocale) {
      // If the cookie locale is the same as the path locale, we remove the language path
      if (localePath === defaultLocale) {
        return getResponse({ redirectUrl: url, localeData })
      }

      return getResponse({ localeData })
    }

    // The language is different, so we redirect to the new language
    else if (localePath) {
      return getResponse({ redirectUrl: url, language: localePath, localeData })
    }

    // If there is no localePath at this point, we return. (This point is reached by the default language's localePath being removed from the path). So we update the cookie to the default language.
    return getResponse({
      redirectUrl: url,
      language: defaultLocale,
      localeData,
    })
  }

  // If language has not been set, we get the accept-language from the browser
  // If that browser language is not in the list of available languages, we redirect to the default language
  const language = getBrowserLanguage(req, locales) || defaultLocale
  const prefix = getLanguagePrefix(language, defaultLocale)

  const detectedLocaleUrl = new URL(
    `${prefix}${plainPathname}${req.nextUrl.search}`,
    req.url.replace(req.nextUrl.pathname, '/')
  )

  if (prevUrl.pathname === detectedLocaleUrl.pathname) {
    return getResponse({ language, localeData })
  }

  // In [[slug.js]] we will be checking if the path's locale and cookie locale match, to remove it before sending it to the api to get the page in this language.
  return getResponse({ redirectUrl: url, language, localeData })
}

function getResponse({ redirectUrl, language, localeData }) {
  const response = redirectUrl
    ? NextResponse.redirect(redirectUrl)
    : NextResponse.next()

  // We set the locale data in the headers, so we can use it in the getServerSideProps
  if (localeData) {
    response.headers.set('x-locale-data', JSON.stringify(localeData))
  }

  // We set the language cookie
  if (language) {
    response.cookies.set('NEXT_LOCALE', language)
  }

  return response
}
