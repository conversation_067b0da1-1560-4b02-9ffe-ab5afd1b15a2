export class APIError extends <PERSON>rror {
  constructor(message, status) {
    super(message)
    try {
      const parsedMessage = JSON.parse(message)
      if (Array.isArray(parsedMessage)) {
        this.code = 'VALIDATION_ERROR'
        this.parsed = parsedMessage
      } else {
        this.code = parsedMessage.code
        this.parsed = parsedMessage
      }
    } catch (error) {
      this.code = 'UNKNOWN_ERROR'
    } finally {
      this.name = 'APIError'
      this.status = status
    }
  }
}

/**
 * Default fetch for all methods
 * @param {string} method - HTTP method
 * @param {string} endpoint - API endpoint
 * @param {object} params - Query or body parameters
 * @param {object} customConfig - Custom configuration options
 * @returns `Promise`
 * @throws `APIError`
 */
export async function client(method, endpoint, params = {}, customConfig = {}) {
  const { baseURL = process.env.NEXT_PUBLIC_API_URL, ...customConfigOptions } =
    customConfig
  const headers = {
    ClientToken: process.env.NEXT_PUBLIC_API_CLIENT_TOKEN ?? '',
  }

  // Convert params object to query string
  const queryString =
    method === 'GET'
      ? Object.entries(params)
          .map(
            ([key, value]) =>
              `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
          )
          .join('&')
      : ''

  const url = `${baseURL}${endpoint}${queryString ? `?${queryString}` : ''}`

  const config = {
    method,
    ...customConfigOptions,
    headers: new Headers({
      ...headers,
      ...customConfigOptions.headers,
    }),
  }

  if (method !== 'GET') {
    if (!config.headers.has('Content-Type')) {
      config.headers.append('Content-Type', 'application/json')
      config.body = JSON.stringify(params)
    } else {
      if (config.headers?.get('Content-Type') === 'multipart/form-data') {
        config.headers.delete('Content-Type')
      }
      config.body = params
    }
  }

  const response = await fetch(url, config)

  if (response.ok) {
    // Only set the data when we have a 200 response
    if (response.status === 200) {
      return await response.json()
    }
    return Promise.resolve({})
  } else {
    const errorMessage = await response.text()
    return Promise.reject(new APIError(errorMessage, response.status))
  }
}

/**
 * Default fetch for "get" methods
 * @param {string} url - URL to fetch
 * @param {object} params - Query parameters
 * @param {object} config - Custom configuration options
 * @returns `Promise`
 */
export async function getFetch(url, params = {}, config = {}) {
  if (!url) {
    throw new Error('URL is undefined')
  }

  const res = await client('GET', url, params, config)
  return res
}

/**
 * Default fetch for "post" methods
 * @param {string} url - URL to post to
 * @param {object} data - Data to post
 * @param {object} config - Custom configuration options
 * @returns `Promise`
 */
export async function postFetch(url, data = {}, config = {}) {
  if (!url) {
    throw new Error('URL is undefined')
  }

  return await client('POST', url, data, config)
}

/**
 * Default fetch for "patch" methods
 * @param {string} url - URL to patch
 * @param {object} data - Data to patch
 * @param {object} config - Custom configuration options
 * @returns `Promise`
 */
export async function patchFetch(url, data = {}, config = {}) {
  if (!url) {
    throw new Error('URL is undefined')
  }

  return await client('PATCH', url, data, config)
}

/**
 * Default fetch for "delete" methods
 * @param {string} url - URL to post delete
 * @param {object} data - Data to pass to delete
 * @param {object} config - Custom configuration options
 * @returns `Promise`
 */
export async function deleteFetch(url, data = {}, config = {}) {
  if (!url) {
    throw new Error('URL is undefined')
  }

  return await client('DELETE', url, data, config)
}
